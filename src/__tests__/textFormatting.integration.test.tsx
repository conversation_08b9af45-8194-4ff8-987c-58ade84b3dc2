import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { RichTextCellRenderer } from '../components/products/grid/RichTextCellRenderer';
import { RichTextCellEditor } from '../components/products/grid/RichTextCellEditor';
import {
  parseFormattedText,
  getPlainText,
  toggleFormatting,
  hasFormatting,
} from '../utils/textFormatting';
import { createRef } from 'react';

describe('Text Formatting Integration Tests', () => {
  describe('Complete formatting workflow', () => {
    it('should handle complete edit-to-display workflow', async () => {
      const user = userEvent.setup();
      const mockStopEditing = vi.fn();
      
      // Start with plain text
      const initialValue = 'Hello world';
      
      // 1. Render editor with plain text
      const editorRef = createRef<any>();
      const { rerender } = render(
        <RichTextCellEditor
          ref={editorRef}
          value={initialValue}
          stopEditing={mockStopEditing}
          node={{} as any}
          data={{} as any}
          colDef={{} as any}
          column={{} as any}
          api={{} as any}
          columnApi={{} as any}
          context={{} as any}
          onKeyDown={{} as any}
          charPress={null}
          rowIndex={0}
          cellStartedEdit={false}
          parseValue={vi.fn()}
          formatValue={vi.fn()}
        />
      );
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveValue('Hello world');
      
      // 2. Apply bold formatting to "world"
      input.setSelectionRange(6, 11);
      await user.keyboard('{Control>}b{/Control}');
      
      const formattedValue = editorRef.current?.getValue();
      expect(formattedValue).toBe('Hello <format bold="true">world</format>');
      
      // 3. Apply red formatting to "Hello"
      input.setSelectionRange(0, 5);
      await user.keyboard('{Control>}r{/Control}');
      
      const doubleFormattedValue = editorRef.current?.getValue();
      expect(doubleFormattedValue).toBe('<format red="true">Hello</format> <format bold="true">world</format>');
      
      // 4. Render the result in display component
      rerender(<RichTextCellRenderer value={doubleFormattedValue} />);
      
      // 5. Verify display rendering
      const helloElement = screen.getByText('Hello');
      expect(helloElement).toHaveStyle({ color: '#dc2626' });
      
      const worldElement = screen.getByText('world');
      expect(worldElement).toHaveStyle({ fontWeight: 'bold' });
    });

    it('should handle toggle formatting workflow', async () => {
      const user = userEvent.setup();
      const mockStopEditing = vi.fn();
      
      // Start with formatted text
      const initialValue = 'Hello <format bold="true">world</format>';
      
      const editorRef = createRef<any>();
      render(
        <RichTextCellEditor
          ref={editorRef}
          value={initialValue}
          stopEditing={mockStopEditing}
          node={{} as any}
          data={{} as any}
          colDef={{} as any}
          column={{} as any}
          api={{} as any}
          columnApi={{} as any}
          context={{} as any}
          onKeyDown={{} as any}
          charPress={null}
          rowIndex={0}
          cellStartedEdit={false}
          parseValue={vi.fn()}
          formatValue={vi.fn()}
        />
      );
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveValue('Hello world'); // Plain text in editor
      
      // Toggle off bold formatting
      input.setSelectionRange(6, 11);
      await user.keyboard('{Control>}b{/Control}');
      
      const unformattedValue = editorRef.current?.getValue();
      expect(unformattedValue).toBe('Hello world');
      
      // Toggle bold back on
      input.setSelectionRange(6, 11);
      await user.keyboard('{Control>}b{/Control}');
      
      const reformattedValue = editorRef.current?.getValue();
      expect(reformattedValue).toBe('Hello <format bold="true">world</format>');
    });

    it('should handle combined formatting workflow', async () => {
      const user = userEvent.setup();
      const mockStopEditing = vi.fn();
      
      const editorRef = createRef<any>();
      render(
        <RichTextCellEditor
          ref={editorRef}
          value="Hello world"
          stopEditing={mockStopEditing}
          node={{} as any}
          data={{} as any}
          colDef={{} as any}
          column={{} as any}
          api={{} as any}
          columnApi={{} as any}
          context={{} as any}
          onKeyDown={{} as any}
          charPress={null}
          rowIndex={0}
          cellStartedEdit={false}
          parseValue={vi.fn()}
          formatValue={vi.fn()}
        />
      );
      
      const input = screen.getByRole('textbox');
      
      // Apply both bold and red to "world"
      input.setSelectionRange(6, 11);
      await user.keyboard('{Control>}b{/Control}');
      
      input.setSelectionRange(6, 11);
      await user.keyboard('{Control>}r{/Control}');
      
      const combinedValue = editorRef.current?.getValue();
      expect(combinedValue).toBe('Hello <format bold="true" red="true">world</format>');
      
      // Verify display rendering
      const { rerender } = render(<div />); // Dummy render
      rerender(<RichTextCellRenderer value={combinedValue} />);
      
      const worldElement = screen.getByText('world');
      expect(worldElement).toHaveStyle({ 
        fontWeight: 'bold',
        color: '#dc2626'
      });
    });
  });

  describe('Utility function integration', () => {
    it('should maintain consistency between parsing and plain text extraction', () => {
      const testCases = [
        'Plain text',
        'Text with <format bold="true">bold</format>',
        'Text with <format red="true">red</format>',
        'Text with <format bold="true" red="true">both</format>',
        'Multiple <format bold="true">bold</format> and <format red="true">red</format> segments',
        '<format bold="true">Start bold</format> middle <format red="true">end red</format>',
      ];

      testCases.forEach(text => {
        const segments = parseFormattedText(text);
        const plainText = getPlainText(text);
        
        // Reconstruct plain text from segments
        const reconstructedPlain = segments.map(s => s.text).join('');
        
        expect(reconstructedPlain).toBe(plainText);
      });
    });

    it('should maintain formatting consistency through toggle operations', () => {
      const originalText = 'Hello <format bold="true">bold</format> and <format red="true">red</format> text';
      
      // Toggle bold off and on again
      const toggledOff = toggleFormatting(originalText, 6, 10, 'bold');
      const toggledOn = toggleFormatting(toggledOff, 6, 10, 'bold');
      
      expect(toggledOn).toBe(originalText);
    });

    it('should handle complex editing scenarios', () => {
      let text = 'Start middle end';
      
      // Apply bold to "middle"
      text = toggleFormatting(text, 6, 12, 'bold');
      expect(text).toBe('Start <format bold="true">middle</format> end');
      
      // Apply red to "Start"
      text = toggleFormatting(text, 0, 5, 'red');
      expect(text).toBe('<format red="true">Start</format> <format bold="true">middle</format> end');
      
      // Apply both to "end"
      text = toggleFormatting(text, 13, 16, 'bold');
      text = toggleFormatting(text, 13, 16, 'red');
      expect(text).toBe('<format red="true">Start</format> <format bold="true">middle</format> <format bold="true" red="true">end</format>');
      
      // Verify hasFormatting works
      expect(hasFormatting(text)).toBe(true);
      
      // Verify plain text extraction
      expect(getPlainText(text)).toBe('Start middle end');
    });
  });

  describe('Error handling integration', () => {
    it('should handle malformed input gracefully in complete workflow', () => {
      const malformedText = 'Malformed <format bold="true">unclosed tag';
      
      // Should not crash when rendering
      expect(() => render(<RichTextCellRenderer value={malformedText} />)).not.toThrow();
      
      // Should not crash when editing
      expect(() => render(
        <RichTextCellEditor
          value={malformedText}
          stopEditing={vi.fn()}
          node={{} as any}
          data={{} as any}
          colDef={{} as any}
          column={{} as any}
          api={{} as any}
          columnApi={{} as any}
          context={{} as any}
          onKeyDown={{} as any}
          charPress={null}
          rowIndex={0}
          cellStartedEdit={false}
          parseValue={vi.fn()}
          formatValue={vi.fn()}
        />
      )).not.toThrow();
    });

    it('should handle empty and null values in complete workflow', () => {
      const testValues = ['', null, undefined];
      
      testValues.forEach(value => {
        // Should not crash when rendering
        expect(() => render(<RichTextCellRenderer value={value as any} />)).not.toThrow();
        
        // Should not crash when editing
        expect(() => render(
          <RichTextCellEditor
            value={value as any}
            stopEditing={vi.fn()}
            node={{} as any}
            data={{} as any}
            colDef={{} as any}
            column={{} as any}
            api={{} as any}
            columnApi={{} as any}
            context={{} as any}
            onKeyDown={{} as any}
            charPress={null}
            rowIndex={0}
            cellStartedEdit={false}
            parseValue={vi.fn()}
            formatValue={vi.fn()}
          />
        )).not.toThrow();
      });
    });
  });

  describe('Performance integration', () => {
    it('should handle large text with many formatting segments efficiently', () => {
      let largeText = '';
      for (let i = 0; i < 100; i++) {
        largeText += `Segment ${i} <format bold="true">bold ${i}</format> `;
      }
      
      // Should not crash or take too long
      const start = performance.now();
      
      expect(() => render(<RichTextCellRenderer value={largeText} />)).not.toThrow();
      expect(() => parseFormattedText(largeText)).not.toThrow();
      expect(() => getPlainText(largeText)).not.toThrow();
      
      const end = performance.now();
      expect(end - start).toBeLessThan(1000); // Should complete within 1 second
    });
  });

  describe('Keyboard shortcuts integration', () => {
    it('should handle all keyboard shortcut combinations', async () => {
      const user = userEvent.setup();
      const mockStopEditing = vi.fn();
      
      const editorRef = createRef<any>();
      render(
        <RichTextCellEditor
          ref={editorRef}
          value="Test text for shortcuts"
          stopEditing={mockStopEditing}
          node={{} as any}
          data={{} as any}
          colDef={{} as any}
          column={{} as any}
          api={{} as any}
          columnApi={{} as any}
          context={{} as any}
          onKeyDown={{} as any}
          charPress={null}
          rowIndex={0}
          cellStartedEdit={false}
          parseValue={vi.fn()}
          formatValue={vi.fn()}
        />
      );
      
      const input = screen.getByRole('textbox');
      
      // Test Ctrl+B
      input.setSelectionRange(0, 4);
      await user.keyboard('{Control>}b{/Control}');
      expect(editorRef.current?.getValue()).toContain('<format bold="true">Test</format>');
      
      // Test Ctrl+R
      input.setSelectionRange(5, 9);
      await user.keyboard('{Control>}r{/Control}');
      expect(editorRef.current?.getValue()).toContain('<format red="true">text</format>');
      
      // Test Enter to save
      await user.keyboard('{Enter}');
      expect(mockStopEditing).toHaveBeenCalled();
      
      // Test Escape to cancel
      mockStopEditing.mockClear();
      input.setSelectionRange(10, 13);
      await user.keyboard('{Control>}b{/Control}');
      await user.keyboard('{Escape}');
      expect(mockStopEditing).toHaveBeenCalled();
      expect(editorRef.current?.getValue()).toBe('Test text for shortcuts'); // Should revert
    });
  });
});
