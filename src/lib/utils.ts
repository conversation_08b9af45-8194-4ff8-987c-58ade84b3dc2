import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function isMacOS(): boolean {
  return (
    typeof window !== "undefined" &&
    /Mac|iPod|iPhone|iPad/.test(navigator.platform)
  );
}

export function getHorizontalScrollModifierKey(): "metaKey" | "altKey" {
  return isMacOS() ? "metaKey" : "altKey";
}
