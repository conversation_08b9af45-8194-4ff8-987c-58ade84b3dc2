import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { Database } from "../types/supabase";
import { supabase } from "../lib/supabase";
import { GroupHeaderRow } from "../components/products/grid/GroupHeaderRenderer";
import { toast } from "@/hooks/use-toast";

// --- Определение типов для большей ясности кода ---
type Product = Database["public"]["Tables"]["products"]["Row"] & {
  product_images?: Array<Database["public"]["Tables"]["product_images"]["Row"]>;
};

type ProductGroup = Database["public"]["Tables"]["product_groups"]["Row"];
type RowData = Product | GroupHeaderRow; // Объединенный тип для данных в таблице
// ------------------------------------------------

export function useProducts({
  showHidden = false,
}: {
  showHidden?: boolean;
} = {}) {
  return useQuery<{ data: RowData[] }>({
    queryKey: ["products", { showHidden }],
    queryFn: async () => {
      console.log(
        "[useProducts] Начало получения данных о продуктах и группах"
      );

      // --- Параллельная выборка продуктов и групп ---
      const [productsResponse, groupsResponse] = await Promise.all([
        supabase
          .from("products")
          .select("*, product_images(*)")
          .eq("is_hidden", showHidden),
        supabase
          .from("product_groups")
          .select("*")
          .order("order_index", { ascending: true }),
      ]);
      // ----------------------------------------

      const { data: productsData, error: productsError } = productsResponse;
      const { data: groupsData, error: groupsError } = groupsResponse;

      if (productsError) throw productsError;
      if (groupsError) throw groupsError;

      const products = (productsData || []) as Product[];
      const groups = (groupsData || []) as ProductGroup[];

      console.log(
        `[useProducts] Получено ${products.length} продуктов и ${groups.length} групп`
      );
      console.log(
        "[useProducts] Порядок групп:",
        groups.map((g) => ({ id: g.id, name: g.name, order: g.order_index }))
      );

      // --- Обработка данных: сортировка и вставка заголовков ---
      const groupMap = new Map(groups.map((g) => [g.id, g]));
      const processedData: RowData[] = [];

      // Счетчик продуктов в каждой группе
      const groupProductCount: Record<string, number> = {};

      // Подсчет продуктов в каждой группе
      products.forEach((product) => {
        if (product.group_id) {
          groupProductCount[product.group_id] =
            (groupProductCount[product.group_id] || 0) + 1;
        }
      });

      console.log(
        "[useProducts] Кол-во продуктов в группах:",
        groupProductCount
      );

      // Сортировка продуктов: по группе -> по orderInGroup -> по имени
      console.log("[useProducts] Начало сортировки продуктов...");
      const sortedProducts = [...products].sort((a, b) => {
        const groupA = a.group_id ? groupMap.get(a.group_id) : null;
        const groupB = b.group_id ? groupMap.get(b.group_id) : null;

        const groupAOrder = groupA?.order_index ?? Infinity;
        const groupBOrder = groupB?.order_index ?? Infinity;

        // Сначала сортируем по порядку групп
        if (groupAOrder !== groupBOrder) {
          return groupAOrder - groupBOrder;
        }

        // Затем сортируем по порядку внутри группы
        const orderA = a.order_in_group ?? 0;
        const orderB = b.order_in_group ?? 0;
        if (orderA !== orderB) {
          return orderA - orderB;
        }

        // И лишь затем по имени (если orderInGroup совпадает)
        return (a.name ?? "").localeCompare(b.name ?? "");
      });

      // Выводим для отладки первые несколько отсортированных продуктов
      console.log(
        "[useProducts] Первые 5 отсортированных продуктов:",
        sortedProducts.slice(0, 5).map((p) => ({
          id: p.id,
          name: p.name,
          group_id: p.group_id,
          order_in_group: p.order_in_group,
        }))
      );

      // Группировка продуктов по ID группы
      const productsByGroup: Record<string, Product[]> = {};
      const ungroupedProducts: Product[] = [];

      sortedProducts.forEach((product) => {
        // Убедимся, что у продукта есть custom_fields
        const normalizedProduct = {
          ...product,
          custom_fields: product.custom_fields || {},
        };

        if (normalizedProduct.group_id) {
          if (!productsByGroup[normalizedProduct.group_id]) {
            productsByGroup[normalizedProduct.group_id] = [];
          }
          productsByGroup[normalizedProduct.group_id].push(normalizedProduct);
        } else {
          ungroupedProducts.push(normalizedProduct);
        }
      });

      console.log(
        "[useProducts] Группировка завершена: ",
        Object.keys(productsByGroup).map((groupId) => ({
          groupId,
          groupName: groupMap.get(groupId)?.name || "Неизвестная группа",
          productCount: productsByGroup[groupId].length,
        }))
      );
      console.log(
        "[useProducts] Негруппированных продуктов:",
        ungroupedProducts.length
      );

      // Добавление заголовков групп и продуктов в итоговый массив
      console.log("[useProducts] Формирование итогового массива данных...");
      groups.forEach((group) => {
        // Добавляем заголовок группы
        processedData.push({
          isGroupHeader: true,
          groupId: group.id,
          groupName: group.name,
          totalProducts: groupProductCount[group.id] || 0,
        });

        // Добавляем продукты этой группы
        const groupProducts = productsByGroup[group.id] || [];
        if (groupProducts.length > 0) {
          processedData.push(...groupProducts);
        }
      });

      // В конце добавляем продукты без группы (если есть)
      if (ungroupedProducts.length > 0) {
        // Опционально: можно добавить заголовок для негруппированных продуктов
        processedData.push({
          isGroupHeader: true,
          groupId: "ungrouped",
          groupName: "Без группы",
          totalProducts: ungroupedProducts.length,
        });
        processedData.push(...ungroupedProducts);
      }
      // --------------------------------------------

      console.log(
        `[useProducts] Итоговый массив содержит ${processedData.length} элементов`
      );
      return { data: processedData };
    },
  });
}

export function useProductMutations() {
  const queryClient = useQueryClient();

  const update = useMutation({
    mutationFn: async ({
      id,
      updates,
    }: {
      id: string;
      updates: Partial<Database["public"]["Tables"]["products"]["Update"]>;
    }) => {
      try {
        console.log(`[API] Обновление продукта:`, { id, updates });

        // Проверяем, нет ли в updates динамических полей, которые должны быть внутри custom_fields
        const sanitizedUpdates = { ...updates } as Record<string, unknown>;
        const dynamicFields: Record<string, unknown> = {};
        let hasDynamicFields = false;

        // Ищем поля, которые не являются стандартными полями таблицы products
        // и добавляем их в custom_fields
        for (const key in sanitizedUpdates) {
          // Проверяем, является ли поле динамическим (не входит в схему таблицы)
          if (
            ![
              "id",
              "name",
              "weight",
              "price",
              "validity_period",
              "is_hidden",
              "custom_fields",
              "created_at",
              "updated_at",
              "group_id",
              "order_in_group", // Добавляем новое поле order_in_group в список стандартных полей
            ].includes(key)
          ) {
            dynamicFields[key] = sanitizedUpdates[key];
            delete sanitizedUpdates[key]; // Удаляем поле из основных обновлений
            hasDynamicFields = true;
          }
        }

        // Если найдены динамические поля, добавляем их в custom_fields
        if (hasDynamicFields) {
          // Получаем текущие данные продукта, чтобы объединить custom_fields
          const { data: existingProduct } = await supabase
            .from("products")
            .select("custom_fields")
            .eq("id", id)
            .single();

          const existingCustomFields = (existingProduct?.custom_fields ||
            {}) as Record<string, unknown>;

          // Объединяем существующие поля с новыми
          sanitizedUpdates.custom_fields = {
            ...existingCustomFields,
            ...dynamicFields,
          };

          console.log(
            `[API] Обнаружены динамические поля, перемещены в custom_fields:`,
            {
              originalUpdates: updates,
              dynamicFields,
              newCustomFields: sanitizedUpdates.custom_fields,
            }
          );
        }

        // Проверка и санитизация данных custom_fields
        if (sanitizedUpdates.custom_fields !== undefined) {
          // Убедимся, что custom_fields - валидный JSON
          try {
            // Преобразуем в строку и обратно для очистки от недопустимых значений
            const cleanJSON = JSON.parse(
              JSON.stringify(sanitizedUpdates.custom_fields)
            );
            sanitizedUpdates.custom_fields = cleanJSON;
          } catch (error) {
            console.error("Ошибка при санитизации custom_fields:", error);
            sanitizedUpdates.custom_fields = {}; // Используем пустой объект если невалидные данные
          }
        }

        const { data, error } = await supabase
          .from("products")
          .update(
            sanitizedUpdates as Partial<
              Database["public"]["Tables"]["products"]["Update"]
            >
          )
          .eq("id", id)
          .select()
          .single();

        if (error) {
          console.error(`[API] Ошибка при обновлении продукта:`, {
            id,
            updates: sanitizedUpdates,
            error: {
              message: error.message,
              details: error.details,
              hint: error.hint,
              code: error.code,
            },
          });
          throw error;
        }

        return data;
      } catch (error) {
        console.error(`[API] Неожиданная ошибка при обновлении продукта:`, {
          id,
          updates,
          error,
        });
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const updateImages = useMutation({
    mutationFn: async ({
      productId,
      images,
    }: {
      productId: string;
      images: Database["public"]["Tables"]["product_images"]["Insert"][];
    }) => {
      // Сначала удаляем все существующие изображения
      await supabase
        .from("product_images")
        .delete()
        .eq("product_id", productId);

      // Затем добавляем новые
      const { data, error } = await supabase
        .from("product_images")
        .insert(images.map((img) => ({ ...img, product_id: productId })))
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const remove = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase.from("products").delete().eq("id", id);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const toggleVisibility = useMutation({
    mutationFn: async ({
      ids,
      isHidden,
    }: {
      ids: string[];
      isHidden: boolean;
    }) => {
      const { error } = await supabase
        .from("products")
        .update({ is_hidden: isHidden })
        .in("id", ids);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const create = useMutation({
    mutationFn: async (
      product: Omit<Database["public"]["Tables"]["products"]["Insert"], "id">
    ) => {
      try {
        // Убедимся, что у нового продукта есть custom_fields
        const productWithCustomFields = {
          ...product,
          custom_fields: product.custom_fields || {},
        };

        // Проверка и санитизация данных custom_fields
        try {
          const cleanJSON = JSON.parse(
            JSON.stringify(productWithCustomFields.custom_fields)
          );
          productWithCustomFields.custom_fields = cleanJSON;
        } catch (error) {
          console.error("Ошибка при санитизации custom_fields:", error);
          productWithCustomFields.custom_fields = {}; // Используем пустой объект если невалидные данные
        }

        const { data, error } = await supabase
          .from("products")
          .insert(productWithCustomFields)
          .select()
          .single();

        if (error) {
          console.error(`[API] Ошибка при создании продукта:`, {
            product: productWithCustomFields,
            error: {
              message: error.message,
              details: error.details,
              hint: error.hint,
              code: error.code,
            },
          });
          throw error;
        }

        return data;
      } catch (error) {
        console.error(`[API] Неожиданная ошибка при создании продукта:`, {
          product,
          error,
        });
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  // Новая мутация для массового обновления порядка продуктов
  const bulkUpdateOrder = useMutation({
    mutationFn: async (
      updates: Array<{ id: string; order_in_group: number }>
    ) => {
      console.log(
        `[BulkUpdate] Начало массового обновления порядка для ${updates.length} продуктов`
      );

      try {
        // Выполняем обновления через Promise.all
        const promises = updates.map((u) => {
          console.log(
            `[BulkUpdate] Обновление продукта ${u.id} -> order_in_group: ${u.order_in_group}`
          );
          return supabase
            .from("products")
            .update({ order_in_group: u.order_in_group })
            .eq("id", u.id);
        });

        const results = await Promise.allSettled(promises);

        // Проверяем на ошибки более детально
        const failed = results.filter(
          (r) => r.status === "rejected"
        ) as PromiseRejectedResult[];
        const errors = results
          .filter(
            (r) =>
              r.status === "fulfilled" &&
              (r.value as { error: unknown | null }).error !== null
          )
          .map((r) => {
            const result = r as PromiseFulfilledResult<{
              error: unknown;
              status: number;
            }>;
            return {
              error: result.value.error,
              status: result.value.status,
            };
          });

        if (failed.length > 0 || errors.length > 0) {
          console.error(
            "[BulkUpdate] Ошибки при обновлении порядка продуктов:",
            {
              rejections: failed.map((f) => f.reason),
              apiErrors: errors,
            }
          );

          throw new Error(
            `Не удалось обновить порядок ${
              failed.length + errors.length
            } продуктов.`
          );
        }

        console.log(
          `[BulkUpdate] Успешно обновлен порядок для ${updates.length} продуктов`
        );
      } catch (error) {
        console.error(
          "[BulkUpdate] Произошла ошибка при выполнении массового обновления:",
          error
        );
        throw error;
      }
    },
    onSuccess: () => {
      console.log(
        "[BulkUpdate] Инвалидация кеша после успешного обновления порядка продуктов"
      );
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast({ title: "Успех", description: "Порядок продуктов обновлен" });
    },
    onError: (error) => {
      console.error("[BulkUpdate] Ошибка в мутации:", error);
      toast({
        title: "Ошибка",
        description: `Ошибка обновления порядка продуктов: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  return {
    update,
    updateImages,
    remove,
    toggleVisibility,
    create,
    bulkUpdateOrder,
  };
}
