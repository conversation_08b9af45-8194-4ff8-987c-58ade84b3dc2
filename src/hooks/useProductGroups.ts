import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "../lib/supabase";
import type { Database } from "../types/supabase";
import { toast } from "@/hooks/use-toast";

export type ProductGroup =
  Database["public"]["Tables"]["product_groups"]["Row"];

export function useProductGroups() {
  return useQuery({
    queryKey: ["product_groups"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("product_groups")
        .select("*")
        .order("order_index", { ascending: true });

      if (error) throw error;
      return { data: data as ProductGroup[] };
    },
  });
}

export function useProductGroupMutations() {
  const queryClient = useQueryClient();

  const create = useMutation({
    mutationFn: async (
      group: Omit<
        Database["public"]["Tables"]["product_groups"]["Insert"],
        "id"
      >
    ) => {
      const { data, error } = await supabase
        .from("product_groups")
        .insert(group)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product_groups"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast({ title: "Успех", description: "Группа создана" });
    },
    onError: (error) => {
      toast({
        title: "Ошибка",
        description: `Ошибка создания группы: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const update = useMutation({
    mutationFn: async ({
      id,
      updates,
    }: {
      id: string;
      updates: Partial<
        Database["public"]["Tables"]["product_groups"]["Update"]
      >;
    }) => {
      const { data, error } = await supabase
        .from("product_groups")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product_groups"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
    onError: (error) => {
      toast({
        title: "Ошибка",
        description: `Ошибка обновления группы: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const remove = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("product_groups")
        .delete()
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product_groups"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast({ title: "Успех", description: "Группа удалена" });
    },
    onError: (error) => {
      toast({
        title: "Ошибка",
        description: `Ошибка удаления группы: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const reorder = useMutation({
    mutationFn: async (groups: { id: string; orderIndex: number }[]) => {
      // Обновляем порядок групп с помощью транзакции
      const promises = groups.map(({ id, orderIndex }) =>
        supabase
          .from("product_groups")
          .update({ order_index: orderIndex })
          .eq("id", id)
      );

      await Promise.all(promises);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product_groups"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast({ title: "Успех", description: "Порядок групп обновлен" });
    },
    onError: (error) => {
      toast({
        title: "Ошибка",
        description: `Ошибка обновления порядка групп: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Добавляем новую мутацию для массового обновления order_index в группах
  const bulkUpdateOrder = useMutation({
    mutationFn: async (updates: Array<{ id: string; order_index: number }>) => {
      console.log(
        `[GroupBulkUpdate] Начало массового обновления порядка для ${updates.length} групп`
      );

      try {
        // Выполняем обновления через Promise.all
        const promises = updates.map((u) => {
          console.log(
            `[GroupBulkUpdate] Обновление группы ${u.id} -> order_index: ${u.order_index}`
          );
          return supabase
            .from("product_groups")
            .update({ order_index: u.order_index })
            .eq("id", u.id);
        });

        const results = await Promise.allSettled(promises);

        // Проверяем на ошибки более детально
        const failed = results.filter(
          (r) => r.status === "rejected"
        ) as PromiseRejectedResult[];
        const errors = results
          .filter(
            (r) =>
              r.status === "fulfilled" &&
              (r.value as { error: unknown | null }).error !== null
          )
          .map((r) => {
            const result = r as PromiseFulfilledResult<{
              error: unknown;
              status: number;
            }>;
            return {
              error: result.value.error,
              status: result.value.status,
            };
          });

        if (failed.length > 0 || errors.length > 0) {
          console.error(
            "[GroupBulkUpdate] Ошибки при обновлении порядка групп:",
            {
              rejections: failed.map((f) => f.reason),
              apiErrors: errors,
            }
          );

          throw new Error(
            `Не удалось обновить порядок ${
              failed.length + errors.length
            } групп.`
          );
        }

        console.log(
          `[GroupBulkUpdate] Успешно обновлен порядок для ${updates.length} групп`
        );
      } catch (error) {
        console.error(
          "[GroupBulkUpdate] Произошла ошибка при выполнении массового обновления:",
          error
        );
        throw error;
      }
    },
    onSuccess: () => {
      console.log(
        "[GroupBulkUpdate] Инвалидация кеша после успешного обновления порядка групп"
      );
      queryClient.invalidateQueries({ queryKey: ["product_groups"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast({ title: "Успех", description: "Порядок групп обновлен" });
    },
    onError: (error) => {
      console.error("[GroupBulkUpdate] Ошибка в мутации:", error);
      toast({
        title: "Ошибка",
        description: `Ошибка обновления порядка групп: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  return {
    create,
    update,
    remove,
    reorder,
    bulkUpdateOrder,
  };
}
