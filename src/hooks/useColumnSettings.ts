import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { Database } from "../types/supabase";
import { supabase } from "../lib/supabase";

type ColumnSettingInsert =
  Database["public"]["Tables"]["column_settings"]["Insert"];
type ColumnSettingUpdate =
  Database["public"]["Tables"]["column_settings"]["Update"];

export function useColumnSettings() {
  return useQuery({
    queryKey: ["columnSettings"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("column_settings")
        .select("*")
        .order("order_index");

      if (error) throw error;
      return { data: data || [] };
    },
  });
}

export function useColumnSettingsMutations() {
  const queryClient = useQueryClient();

  const create = useMutation({
    mutationFn: async (column: ColumnSettingInsert) => {
      const { data, error } = await supabase
        .from("column_settings")
        .insert(column)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["columnSettings"] });
    },
  });

  const update = useMutation({
    mutationFn: async ({
      id,
      updates,
    }: {
      id: string;
      updates: ColumnSettingUpdate;
    }) => {
      const { data, error } = await supabase
        .from("column_settings")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["columnSettings"] });
    },
  });

  const remove = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("column_settings")
        .delete()
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["columnSettings"] });

      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const reorder = useMutation({
    mutationFn: async (columns: { id: string; order_index: number }[]) => {
      // Используем трюк с Promise.all для выполнения множественных обновлений
      await Promise.all(
        columns.map(({ id, order_index }) =>
          supabase.from("column_settings").update({ order_index }).eq("id", id)
        )
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["columnSettings"] });
    },
  });

  return { create, update, remove, reorder };
}
