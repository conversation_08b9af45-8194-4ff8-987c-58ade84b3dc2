import { useState, useCallback } from "react";
import {
  getPublicFolderContents,
  getDirectFileUrl,
} from "../services/yandexDisk";
import { proxyYandexUrl } from "../services/imageProxyService";

export interface FolderItem {
  name: string;
  preview?: string;
  public_url?: string;
  type: "dir" | "file";
  directUrl?: string | null; // null if not loaded yet or error
  directUrlError?: boolean; // Flag for directUrl loading error
}

export const useImageFolder = () => {
  const [folderContents, setFolderContents] = useState<FolderItem[]>([]);
  const [isLoadingContents, setIsLoadingContents] = useState(false);
  const [isLoadingDirectUrls, setIsLoadingDirectUrls] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Получение содержимого папки изображений
  const fetchFolderContents = useCallback(
    async (url: string): Promise<boolean> => {
      setIsLoadingContents(true);
      setError(null);
      try {
        const items = await getPublicFolderContents(url);
        // Фильтруем только файлы изображений
        const imageFiles = items.filter(
          (item) =>
            item.type === "file" &&
            item.name.match(/\.(jpeg|jpg|png|gif|webp)$/i)
        );

        // Обрабатываем preview URL асинхронно с fallback на прямую ссылку
        const imageItems = await Promise.all(
          imageFiles.map(async (item) => {
            let previewUrl = "";

            // Функция для повторных попыток получения превью
            const tryGetPreview = async (retries = 3): Promise<string> => {
              for (let i = 0; i < retries; i++) {
                // Сначала пытаемся использовать стандартный preview от Яндекса
                if (item.preview) {
                  try {
                    const url = await proxyYandexUrl(item.preview);
                    if (url) return url;
                  } catch (err) {
                    console.warn(
                      `Попытка ${i + 1}: Ошибка получения preview для ${
                        item.name
                      }:`,
                      err
                    );
                  }
                }

                // Если preview недоступен, используем прямую ссылку на файл
                if (item.public_url) {
                  try {
                    const directUrl = await getDirectFileUrl(url, item.name);
                    const proxiedUrl = await proxyYandexUrl(directUrl);
                    if (proxiedUrl) return proxiedUrl;
                  } catch (err) {
                    console.warn(
                      `Попытка ${i + 1}: Ошибка получения прямой ссылки для ${
                        item.name
                      }:`,
                      err
                    );
                  }
                }

                // Если это не последняя попытка, ждем перед следующей
                if (i < retries - 1) {
                  await new Promise((resolve) =>
                    setTimeout(resolve, 1000 * (i + 1))
                  ); // 1с, 2с, 3с
                }
              }
              return "";
            };

            previewUrl = await tryGetPreview();

            return {
              name: item.name,
              preview: previewUrl,
              public_url: item.public_url,
              type: item.type as "dir" | "file",
              directUrl: null,
              directUrlError: false,
            };
          })
        );

        setFolderContents(imageItems);
        return true;
      } catch (err) {
        setError(
          err instanceof Error
            ? `Ошибка получения содержимого папки: ${err.message}`
            : "Произошла ошибка при получении содержимого папки"
        );
        return false;
      } finally {
        setIsLoadingContents(false);
      }
    },
    []
  );

  // Получение прямой ссылки на изображение
  const getImageDirectUrl = useCallback(
    async (folderUrl: string, fileName: string): Promise<string | null> => {
      try {
        const directUrl = await getDirectFileUrl(folderUrl, fileName);
        const proxiedUrl = await proxyYandexUrl(directUrl);
        setSelectedImage(proxiedUrl);
        return proxiedUrl;
      } catch (err) {
        setError(
          err instanceof Error
            ? `Ошибка получения прямой ссылки: ${err.message}`
            : "Произошла ошибка при получении прямой ссылки на изображение"
        );
        return null;
      }
    },
    []
  );

  // Загрузка прямых ссылок для всех изображений
  const fetchAllDirectUrls = useCallback(
    async (publicUrl: string): Promise<void> => {
      if (folderContents.length === 0) {
        return;
      }

      setIsLoadingDirectUrls(true);
      setError(null);

      try {
        // Используем Promise.allSettled для обработки частичных ошибок
        const urlPromises = folderContents.map(async (item) => {
          if (item.type === "file") {
            try {
              const directUrl = await getDirectFileUrl(publicUrl, item.name);
              const proxiedUrl = await proxyYandexUrl(directUrl);
              return { name: item.name, directUrl: proxiedUrl, error: false };
            } catch (err) {
              console.error(`Ошибка загрузки URL для ${item.name}:`, err);
              return { name: item.name, directUrl: null, error: true };
            }
          }
          return { name: item.name, directUrl: null, error: false };
        });

        const results = await Promise.allSettled(urlPromises);

        // Обновляем folderContents с полученными directUrl
        setFolderContents((prevContents) =>
          prevContents.map((item) => {
            const result = results.find(
              (r) => r.status === "fulfilled" && r.value.name === item.name
            );
            if (result && result.status === "fulfilled") {
              return {
                ...item,
                directUrl: result.value.directUrl,
                directUrlError: result.value.error,
              };
            }
            return item;
          })
        );
      } catch (err) {
        setError(
          err instanceof Error
            ? `Ошибка загрузки прямых ссылок: ${err.message}`
            : "Произошла ошибка при загрузке прямых ссылок"
        );
      } finally {
        setIsLoadingDirectUrls(false);
      }
    },
    [folderContents]
  );

  // Функция для обеспечения загрузки всех directUrl
  const ensureAllDirectUrlsLoaded = useCallback(
    async (publicUrl: string): Promise<void> => {
      // Проверяем, есть ли элементы без directUrl
      const hasUnloadedUrls = folderContents.some(
        (item) =>
          item.type === "file" &&
          item.directUrl === null &&
          !item.directUrlError
      );

      if (hasUnloadedUrls) {
        await fetchAllDirectUrls(publicUrl);
      }
    },
    [folderContents, fetchAllDirectUrls]
  );

  return {
    folderContents,
    isLoadingContents,
    isLoadingDirectUrls,
    error,
    setError,
    fetchFolderContents,
    getImageDirectUrl,
    fetchAllDirectUrls,
    ensureAllDirectUrlsLoaded,
    selectedImage,
    setSelectedImage,
  };
};
