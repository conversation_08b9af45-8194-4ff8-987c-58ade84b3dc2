import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import { 
  userService, 
  type AdminUserView, 
  type CreateUserPayload, 
  type SetUserRolePayload,
  type DeleteUserPayload
} from '@/services/userService';

const usersQueryKey = ['admin', 'users'];

export function useUsers() {
  return useQuery<AdminUserView[], Error>({
    queryKey: usersQueryKey,
    queryFn: userService.getUsers,
  });
}

export function useUserMutations() {
  const queryClient = useQueryClient();

  const createUserMutation = useMutation<AdminUserView, Error, CreateUserPayload>({
    mutationFn: userService.createUser,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: usersQueryKey });
      toast({ title: 'Успех', description: `Пользователь ${data.email || data.id} создан.` });
    },
    onError: (error) => {
      toast({ title: 'Ошибка', description: `Не удалось создать пользователя: ${error.message}`, variant: 'destructive' });
    },
  });

  const setUserRoleMutation = useMutation<AdminUserView, Error, SetUserRolePayload>({
    mutationFn: userService.setUserRole,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: usersQueryKey });
      toast({ title: 'Успех', description: `Роль пользователя ${data.email || data.id} обновлена.` });
    },
    onError: (error) => {
      toast({ title: 'Ошибка', description: `Не удалось обновить роль: ${error.message}`, variant: 'destructive' });
    },
  });

  const deleteUserMutation = useMutation<{ message: string }, Error, DeleteUserPayload>({
    mutationFn: userService.deleteUser,
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: usersQueryKey });
      toast({ title: 'Успех', description: data.message || `Пользователь ${variables.userId} удален.` });
    },
    onError: (error) => {
      toast({ title: 'Ошибка', description: `Не удалось удалить пользователя: ${error.message}`, variant: 'destructive' });
    },
  });

  return {
    createUser: createUserMutation,
    setUserRole: setUserRoleMutation,
    deleteUser: deleteUserMutation,
  };
}
