import { useEffect, useRef } from "react";
import { getHorizontalScrollModifierKey } from "@/lib/utils";

interface UseHorizontalScrollOptions {
  enabled?: boolean;
  scrollSpeed?: number;
  smoothScrolling?: boolean;
  scrollSensitivity?: number;
}

export function useHorizontalScroll<T extends HTMLElement>(
  options: UseHorizontalScrollOptions = {}
) {
  const {
    enabled = true,
    scrollSpeed = 1,
    smoothScrolling = false,
    scrollSensitivity = 0.5,
  } = options;

  const elementRef = useRef<T>(null);
  const modifierKey = getHorizontalScrollModifierKey();

  useEffect(() => {
    const element = elementRef.current;
    if (!element || !enabled) {
      return;
    }

    const handleWheel = (event: WheelEvent) => {
      // Fix modifier key detection - use proper boolean checks
      const isModifierPressed =
        modifierKey === "metaKey" ? event.metaKey : event.altKey;

      if (!isModifierPressed) return;

      // Find the scrollable element - could be the element itself or a child
      let scrollableElement: HTMLElement = element;

      // Check if this is an AG Grid container and find the actual scrollable viewport
      // Try multiple selectors to find the correct scrollable element
      const agCenterColsViewport = element.querySelector(
        ".ag-center-cols-viewport"
      ) as HTMLElement;
      const agBodyViewport = element.querySelector(
        ".ag-body-viewport"
      ) as HTMLElement;
      const agBodyHorizontalScrollViewport = element.querySelector(
        ".ag-body-horizontal-scroll-viewport"
      ) as HTMLElement;

      // Priority order: center-cols-viewport > body-horizontal-scroll-viewport > body-viewport > element itself
      if (
        agCenterColsViewport &&
        agCenterColsViewport.scrollWidth > agCenterColsViewport.clientWidth
      ) {
        scrollableElement = agCenterColsViewport;
      } else if (
        agBodyHorizontalScrollViewport &&
        agBodyHorizontalScrollViewport.scrollWidth >
          agBodyHorizontalScrollViewport.clientWidth
      ) {
        scrollableElement = agBodyHorizontalScrollViewport;
      } else if (
        agBodyViewport &&
        agBodyViewport.scrollWidth > agBodyViewport.clientWidth
      ) {
        scrollableElement = agBodyViewport;
      } else if (element.scrollWidth > element.clientWidth) {
        scrollableElement = element;
      } else {
        return;
      }

      event.preventDefault();
      event.stopPropagation();

      // Use deltaY for vertical wheel movement, but apply it horizontally
      // Apply sensitivity to make scrolling feel more natural
      const scrollAmount = event.deltaY * scrollSpeed * scrollSensitivity;

      if (smoothScrolling) {
        scrollableElement.scrollBy({
          left: scrollAmount,
          behavior: "smooth",
        });
      } else {
        // Use direct assignment for immediate, smooth scrolling
        scrollableElement.scrollLeft += scrollAmount;
      }
    };

    element.addEventListener("wheel", handleWheel, { passive: false });

    return () => {
      element.removeEventListener("wheel", handleWheel);
    };
  }, [enabled, scrollSpeed, smoothScrolling, scrollSensitivity, modifierKey]);

  return elementRef;
}
