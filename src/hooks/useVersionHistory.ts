import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "../lib/supabase";
import { Database } from "../types/supabase";
import { toast } from "./use-toast";
import { useAuth } from "../lib/auth";

type VersionHistoryInsert =
  Database["public"]["Tables"]["version_history"]["Insert"];
type Product = Database["public"]["Tables"]["products"]["Row"];
type ProductGroup = Database["public"]["Tables"]["product_groups"]["Row"];
type ColumnSetting = Database["public"]["Tables"]["column_settings"]["Row"];
type ProductImage = Database["public"]["Tables"]["product_images"]["Row"];

interface SnapshotData {
  products: Product[];
  product_groups: ProductGroup[];
  column_settings: ColumnSetting[];
  product_images: ProductImage[];
}

export function useVersionHistory() {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Fetch version history list
  const { data: versions, isLoading: isLoadingVersions } = useQuery({
    queryKey: ["version_history"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("version_history")
        .select("id, created_at, comment, user_id")
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data;
    },
  });

  // Fetch specific version data
  const fetchVersionData = async (
    versionId: string
  ): Promise<SnapshotData | null> => {
    const { data, error } = await supabase
      .from("version_history")
      .select("snapshot_data")
      .eq("id", versionId)
      .single();

    if (error) {
      console.error("Error fetching version data:", error);
      toast({
        title: "Ошибка",
        description: "Не удалось загрузить данные версии",
        variant: "destructive",
      });
      return null;
    }

    return data.snapshot_data as SnapshotData;
  };

  // Save new version
  const saveVersion = useMutation({
    mutationFn: async ({
      comment,
      snapshotData,
    }: {
      comment?: string;
      snapshotData: SnapshotData;
    }) => {
      if (!user) throw new Error("User not authenticated");

      const versionData: VersionHistoryInsert = {
        user_id: user.id,
        comment: comment || null,
        snapshot_data: snapshotData,
      };

      const { data, error } = await supabase
        .from("version_history")
        .insert(versionData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["version_history"] });
      toast({
        title: "Успех",
        description: "Версия успешно сохранена",
      });
    },
    onError: (error) => {
      console.error("Error saving version:", error);
      toast({
        title: "Ошибка",
        description: "Не удалось сохранить версию",
        variant: "destructive",
      });
    },
  });

  // Rollback to version
  const rollbackToVersion = useMutation({
    mutationFn: async (versionId: string) => {
      if (!user) throw new Error("User not authenticated");

      // Get current session
      const { data: sessionData, error: sessionError } =
        await supabase.auth.getSession();
      if (sessionError) {
        console.error("Session error in rollback:", sessionError);
        throw new Error(sessionError.message || "Failed to get user session.");
      }
      if (!sessionData.session) {
        throw new Error("User not authenticated.");
      }
      const token = sessionData.session.access_token;

      // Call Edge Function using the same pattern as userService
      const { data, error } = await supabase.functions.invoke(
        "rollback-version",
        {
          body: { version_id: versionId },
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (error) {
        const errorMessage =
          data?.error?.message ||
          error.message ||
          "Edge function rollback-version call failed.";
        console.error(
          "Error invoking Edge Function rollback-version:",
          errorMessage,
          { data, error }
        );
        throw new Error(errorMessage);
      }

      if (
        data &&
        data.error &&
        typeof data.error === "object" &&
        "message" in data.error
      ) {
        console.error(
          "Functional error from Edge Function rollback-version:",
          data.error.message,
          data
        );
        throw new Error(data.error.message as string);
      }

      return data;
    },
    onSuccess: async () => {
      // Invalidate all data queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: ["products"] });
      queryClient.invalidateQueries({ queryKey: ["product_groups"] });
      queryClient.invalidateQueries({ queryKey: ["column_settings"] });
      queryClient.invalidateQueries({ queryKey: ["product_images"] });

      // Force refetch of column settings to ensure immediate UI update
      try {
        await queryClient.refetchQueries({ queryKey: ["columnSettings"] });
        console.log(
          "[useVersionHistory] Column settings refetched after rollback"
        );
      } catch (error) {
        console.error(
          "[useVersionHistory] Error refetching column settings:",
          error
        );
      }

      toast({
        title: "Успех",
        description: "Данные успешно восстановлены",
      });
    },
    onError: (error) => {
      console.error("Error rolling back to version:", error);
      toast({
        title: "Ошибка",
        description: "Не удалось восстановить версию",
        variant: "destructive",
      });
    },
  });

  // Collect current data for snapshot
  const collectCurrentData = async (): Promise<SnapshotData | null> => {
    try {
      // Fetch products (excluding group headers)
      const { data: products, error: productsError } = await supabase
        .from("products")
        .select("*")
        .order("group_id", { ascending: true })
        .order("order_in_group", { ascending: true });

      if (productsError) throw productsError;

      // Fetch product groups
      const { data: productGroups, error: groupsError } = await supabase
        .from("product_groups")
        .select("*")
        .order("order_index", { ascending: true });

      if (groupsError) throw groupsError;

      // Fetch column settings
      const { data: columnSettings, error: settingsError } = await supabase
        .from("column_settings")
        .select("*")
        .order("order_index", { ascending: true });

      if (settingsError) throw settingsError;

      // Fetch product images
      const { data: productImages, error: imagesError } = await supabase
        .from("product_images")
        .select("*")
        .order("product_id", { ascending: true })
        .order("sort_order", { ascending: true });

      if (imagesError) throw imagesError;

      return {
        products: products || [],
        product_groups: productGroups || [],
        column_settings: columnSettings || [],
        product_images: productImages || [],
      };
    } catch (error) {
      console.error("Error collecting current data:", error);
      toast({
        title: "Ошибка",
        description: "Не удалось собрать текущие данные",
        variant: "destructive",
      });
      return null;
    }
  };

  // Delete version
  const deleteVersion = useMutation({
    mutationFn: async (versionId: string) => {
      if (!user) throw new Error("User not authenticated");

      const { error } = await supabase
        .from("version_history")
        .delete()
        .eq("id", versionId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["version_history"] });
      toast({
        title: "Успех",
        description: "Версия успешно удалена",
      });
    },
    onError: (error) => {
      console.error("Error deleting version:", error);
      toast({
        title: "Ошибка",
        description: "Не удалось удалить версию",
        variant: "destructive",
      });
    },
  });

  return {
    versions,
    isLoadingVersions,
    saveVersion: saveVersion.mutate,
    isSavingVersion: saveVersion.isPending,
    rollbackToVersion: rollbackToVersion.mutate,
    isRollingBack: rollbackToVersion.isPending,
    deleteVersion: deleteVersion.mutate,
    isDeletingVersion: deleteVersion.isPending,
    fetchVersionData,
    collectCurrentData,
  };
}
