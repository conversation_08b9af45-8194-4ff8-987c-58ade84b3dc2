import { useState } from "react";
import { uploadFilesAndPublish } from "../services/yandexDisk";
import { useProductMutations } from "./useProducts";

interface UseImageUploadProps {
  productId: string;
}

export const useImageUpload = ({ productId }: UseImageUploadProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { updateImages } = useProductMutations();

  // Проверка, что все файлы - изображения
  const validateImages = (files: File[]): boolean => {
    const isAllImages = files.every((file) => file.type.startsWith("image/"));
    if (!isAllImages) {
      setError("Пожалуйста, загружайте только изображения");
      return false;
    }
    return true;
  };

  // Загрузка изображений
  const uploadImages = async (files: File[]): Promise<string | null> => {
    if (files.length === 0) return null;

    if (!validateImages(files)) return null;

    setIsLoading(true);
    setError(null);

    try {
      const url = await uploadFilesAndPublish(files);

      // Обновляем запись в базе данных
      await updateImages.mutateAsync({
        productId,
        images: [
          {
            yandex_disk_path: url,
            public_url: url,
            sort_order: 1,
            is_main: true,
            product_id: productId,
          },
        ],
      });

      return url;
    } catch (err) {
      setError(err instanceof Error ? err.message : "Ошибка при загрузке");
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    error,
    setError,
    uploadImages,
    validateImages,
  };
};
