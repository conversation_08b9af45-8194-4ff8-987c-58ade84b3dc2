CREATE TABLE "version_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"user_id" uuid NOT NULL,
	"comment" text,
	"snapshot_data" jsonb NOT NULL
);
--> statement-breakpoint
ALTER TABLE "version_history" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE INDEX "idx_version_history_created_at" ON "version_history" USING btree ("created_at");--> statement-breakpoint
CREATE POLICY "Admin and editor can view version history" ON "version_history" AS PERMISSIVE FOR SELECT TO "authenticated" USING (public.get_my_role() IN ('admin', 'editor'));--> statement-breakpoint
CREATE POLICY "Admin and editor can create versions" ON "version_history" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (
        public.get_my_role() IN ('admin', 'editor')
        AND auth.uid() = user_id
      );--> statement-breakpoint
CREATE POLICY "No one can update version history" ON "version_history" AS PERMISSIVE FOR UPDATE TO "authenticated" USING (false);--> statement-breakpoint
CREATE POLICY "No one can delete version history" ON "version_history" AS PERMISSIVE FOR DELETE TO "authenticated" USING (false);