import {
  pgTable,
  foreignKey,
  unique,
  pgPolicy,
  uuid,
  text,
  integer,
  boolean,
  timestamp,
  check,
  numeric,
  jsonb,
  pgEnum,
  index,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

export const columnType = pgEnum("column_type", ["text", "number"]);

// --- NEW: Product Groups Table ---
export const productGroups = pgTable(
  "product_groups",
  {
    id: uuid("id").defaultRandom().primaryKey().notNull(),
    name: text("name").notNull(),
    orderIndex: integer("order_index").notNull(),
    isHidden: boolean("is_hidden").default(false).notNull(),
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    })
      .defaultNow()
      .notNull(),
  },
  (table) => ({
    orderIdx: index("idx_product_groups_order_index").on(table.orderIndex),
    readAccessPolicy: pgPolicy("ProductGroups: Authenticated read access", {
      as: "permissive",
      for: "select",
      to: ["authenticated"],
      using: sql`
        (public.get_my_role() IN ('admin', 'editor', 'reader')) AND
        (is_hidden = false OR public.get_my_role() IN ('admin', 'editor'))
      `,
    }),
    writeAccessPolicy: pgPolicy("ProductGroups: Admin/Editor write access", {
      as: "permissive",
      for: "all",
      to: ["authenticated"],
      using: sql`public.get_my_role() IN ('admin', 'editor')`,
      withCheck: sql`public.get_my_role() IN ('admin', 'editor')`,
    }),
  })
);
// ----------------------------------

export const products = pgTable(
  "products",
  {
    id: uuid("id").defaultRandom().primaryKey().notNull(),
    name: text("name").notNull(),
    weight: numeric("weight").notNull(),
    price: numeric("price").notNull(),
    validityPeriod: integer("validity_period").notNull(),
    isHidden: boolean("is_hidden").default(false),
    customFields: jsonb("custom_fields").default({}),
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    }).defaultNow(),
    updatedAt: timestamp("updated_at", {
      withTimezone: true,
      mode: "string",
    }).defaultNow(),
    // --- NEW: Group ID Column ---
    groupId: uuid("group_id").references(() => productGroups.id, {
      onDelete: "set null", // Important: Set to null when group is deleted
    }),
    // --------------------------
    // --- NEW: Order within Group Column ---
    orderInGroup: integer("order_in_group").notNull().default(0),
    // --------------------------
  },
  (table) => {
    return {
      groupOrderIdx: index("idx_products_group_order").on(
        table.groupId,
        table.orderInGroup
      ),
      priceCheck: check("products_price_check", sql`price >= (0)::numeric`),
      validityPeriodCheck: check(
        "products_validity_period_check",
        sql`validity_period >= 0`
      ),
      weightCheck: check("products_weight_check", sql`weight >= (0)::numeric`),
      allowAllForAuthenticated: pgPolicy(
        "Allow all for authenticated (TEMP DEBUG)",
        {
          as: "permissive",
          for: "all",
          to: ["authenticated"],
          using: sql`true`,
          withCheck: sql`true`,
        }
      ),
    };
  }
);

// Таблица для хранения настроек столбцов
export const columnSettings = pgTable(
  "column_settings",
  {
    id: uuid("id").defaultRandom().primaryKey().notNull(),
    name: text("name").notNull(),
    displayName: text("display_name").notNull(),
    orderIndex: integer("order_index").notNull(),
    isVisible: boolean("is_visible").default(true),
    dataType: text("data_type").notNull(),
    prefix: text("prefix"),
    postfix: text("postfix"),
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    }).defaultNow(),
  },
  (table) => [
    unique("column_settings_name_unique").on(table.name),
    pgPolicy("ColumnSettings: Authenticated read access", {
      as: "permissive",
      for: "select",
      to: ["authenticated"],
      using: sql`public.get_my_role() IN ('admin', 'editor', 'reader')`,
    }),
    pgPolicy("ColumnSettings: Admin/Editor write access", {
      as: "permissive",
      for: "all",
      to: ["authenticated"],
      using: sql`public.get_my_role() IN ('admin', 'editor')`,
      withCheck: sql`public.get_my_role() IN ('admin', 'editor')`,
    }),
    pgPolicy("Allow public insertion for initialization", {
      as: "permissive",
      for: "insert",
      to: ["public"],
      withCheck: sql`true`,
    }),
  ]
);

export const productImages = pgTable(
  "product_images",
  {
    id: uuid("id").defaultRandom().primaryKey().notNull(),
    productId: uuid("product_id").notNull(),
    yandexDiskPath: text("yandex_disk_path").notNull(),
    publicUrl: text("public_url").notNull(),
    sortOrder: integer("sort_order").notNull(),
    isMain: boolean("is_main").default(false),
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    }).defaultNow(),
  },
  (table) => [
    foreignKey({
      columns: [table.productId],
      foreignColumns: [products.id],
      name: "product_images_product_id_fkey",
    }).onDelete("cascade"),
    unique("product_images_product_id_sort_order_key").on(
      table.productId,
      table.sortOrder
    ),
    pgPolicy("ProductImages: Authenticated read access", {
      as: "permissive",
      for: "select",
      to: ["authenticated"],
      using: sql`public.get_my_role() IN ('admin', 'editor', 'reader')`,
    }),
    pgPolicy("ProductImages: Admin/Editor write access", {
      as: "permissive",
      for: "all",
      to: ["authenticated"],
      using: sql`public.get_my_role() IN ('admin', 'editor')`,
      withCheck: sql`public.get_my_role() IN ('admin', 'editor')`,
    }),
  ]
);

// --- NEW: Session Lock Table ---
export const sessionLock = pgTable(
  "session_lock",
  {
    lockKey: text("lock_key").primaryKey().notNull(), // Ключ блокировки (e.g., "product_list_edit")
    userId: uuid("user_id"), // ID пользователя, держащего блокировку
    sessionId: text("session_id"), // Уникальный ID сессии клиента
    lockedAt: timestamp("locked_at", { withTimezone: true, mode: "string" }), // Время установки/обновления
  },
  () => ({
    // --- Policies ---
    // Разрешаем аутентифицированным пользователям читать и писать
    authenticatedAccessPolicy: pgPolicy("Allow authenticated access", {
      as: "permissive",
      for: "all",
      to: ["authenticated"],
      using: sql`true`,
      withCheck: sql`true`,
    }),
    // Опционально: Разрешаем публичное чтение, если нужно (может быть полезно для отладки)
    // publicReadPolicy: pgPolicy("Allow public read access", {
    //   as: "permissive",
    //   for: "select",
    //   to: ["public"],
    //   using: sql`true`,
    // }),
  })
);
// ----------------------------

// --- Version History Table ---
export const versionHistory = pgTable(
  "version_history",
  {
    id: uuid("id").defaultRandom().primaryKey().notNull(),
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    })
      .defaultNow()
      .notNull(),
    userId: uuid("user_id").notNull(),
    comment: text("comment"),
    snapshotData: jsonb("snapshot_data").notNull(),
  },
  (table) => ({
    createdAtIdx: index("idx_version_history_created_at").on(table.createdAt),
    // RLS Policies
    selectPolicy: pgPolicy("Admin and editor can view version history", {
      as: "permissive",
      for: "select",
      to: ["authenticated"],
      using: sql`public.get_my_role() IN ('admin', 'editor')`,
    }),
    insertPolicy: pgPolicy("Admin and editor can create versions", {
      as: "permissive",
      for: "insert",
      to: ["authenticated"],
      withCheck: sql`
        auth.uid() = user_id
        AND public.get_my_role() IN ('admin', 'editor')
      `,
    }),
    updatePolicy: pgPolicy("No one can update version history", {
      as: "permissive",
      for: "update",
      to: ["authenticated"],
      using: sql`false`,
    }),
    deletePolicy: pgPolicy("Admin and editor can delete version history", {
      as: "permissive",
      for: "delete",
      to: ["authenticated"],
      using: sql`public.get_my_role() IN ('admin', 'editor')`,
    }),
  })
);
