ALTER POLICY "Admin and editor can view version history" ON "version_history" TO authenticated USING (
        EXISTS (
          SELECT 1 FROM auth.users
          WHERE auth.users.id = auth.uid()
          AND (auth.users.raw_app_meta_data->>'role')::text IN ('admin', 'editor')
        )
      );--> statement-breakpoint
ALTER POLICY "Admin and editor can create versions" ON "version_history" TO authenticated WITH CHECK (
        auth.uid() = user_id
        AND EXISTS (
          SELECT 1 FROM auth.users
          WHERE auth.users.id = auth.uid()
          AND (auth.users.raw_app_meta_data->>'role')::text IN ('admin', 'editor')
        )
      );