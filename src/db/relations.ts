import { relations } from "drizzle-orm/relations";
import { products, productImages, productGroups } from "./schema";

export const productImagesRelations = relations(productImages, ({ one }) => ({
  product: one(products, {
    fields: [productImages.productId],
    references: [products.id],
  }),
}));

export const productsRelations = relations(products, ({ one, many }) => ({
  productImages: many(productImages),
  productGroup: one(productGroups, {
    fields: [products.groupId],
    references: [productGroups.id],
  }),
}));

export const productGroupsRelations = relations(productGroups, ({ many }) => ({
  products: many(products),
}));
