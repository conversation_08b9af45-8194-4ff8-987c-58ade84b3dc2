{"id": "ed880414-a13e-4fa7-9afe-c4432b55ba8c", "prevId": "8a13f403-488d-4ed6-b736-e91ea5bc6b9e", "version": "7", "dialect": "postgresql", "tables": {"public.column_settings": {"name": "column_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true}, "order_index": {"name": "order_index", "type": "integer", "primaryKey": false, "notNull": true}, "is_visible": {"name": "is_visible", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "data_type": {"name": "data_type", "type": "text", "primaryKey": false, "notNull": true}, "prefix": {"name": "prefix", "type": "text", "primaryKey": false, "notNull": false}, "postfix": {"name": "postfix", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"column_settings_name_unique": {"name": "column_settings_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {"ColumnSettings: Authenticated read access": {"name": "ColumnSettings: Authenticated read access", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "public.get_my_role() IN ('admin', 'editor', 'reader')"}, "ColumnSettings: Admin/Editor write access": {"name": "ColumnSettings: Admin/Editor write access", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "public.get_my_role() IN ('admin', 'editor')", "withCheck": "public.get_my_role() IN ('admin', 'editor')"}, "Allow public insertion for initialization": {"name": "Allow public insertion for initialization", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"], "withCheck": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.product_groups": {"name": "product_groups", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "order_index": {"name": "order_index", "type": "integer", "primaryKey": false, "notNull": true}, "is_hidden": {"name": "is_hidden", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_product_groups_order_index": {"name": "idx_product_groups_order_index", "columns": [{"expression": "order_index", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"ProductGroups: Authenticated read access": {"name": "ProductGroups: Authenticated read access", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\n        (public.get_my_role() IN ('admin', 'editor', 'reader')) AND\n        (is_hidden = false OR public.get_my_role() IN ('admin', 'editor'))\n      "}, "ProductGroups: Admin/Editor write access": {"name": "ProductGroups: Admin/Editor write access", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "public.get_my_role() IN ('admin', 'editor')", "withCheck": "public.get_my_role() IN ('admin', 'editor')"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.product_images": {"name": "product_images", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "yandex_disk_path": {"name": "yandex_disk_path", "type": "text", "primaryKey": false, "notNull": true}, "public_url": {"name": "public_url", "type": "text", "primaryKey": false, "notNull": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true}, "is_main": {"name": "is_main", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"product_images_product_id_fkey": {"name": "product_images_product_id_fkey", "tableFrom": "product_images", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"product_images_product_id_sort_order_key": {"name": "product_images_product_id_sort_order_key", "nullsNotDistinct": false, "columns": ["product_id", "sort_order"]}}, "policies": {"ProductImages: Authenticated read access": {"name": "ProductImages: Authenticated read access", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "public.get_my_role() IN ('admin', 'editor', 'reader')"}, "ProductImages: Admin/Editor write access": {"name": "ProductImages: Admin/Editor write access", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "public.get_my_role() IN ('admin', 'editor')", "withCheck": "public.get_my_role() IN ('admin', 'editor')"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "weight": {"name": "weight", "type": "numeric", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric", "primaryKey": false, "notNull": true}, "validity_period": {"name": "validity_period", "type": "integer", "primaryKey": false, "notNull": true}, "is_hidden": {"name": "is_hidden", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "custom_fields": {"name": "custom_fields", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "group_id": {"name": "group_id", "type": "uuid", "primaryKey": false, "notNull": false}, "order_in_group": {"name": "order_in_group", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {"idx_products_group_order": {"name": "idx_products_group_order", "columns": [{"expression": "group_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "order_in_group", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"products_group_id_product_groups_id_fk": {"name": "products_group_id_product_groups_id_fk", "tableFrom": "products", "tableTo": "product_groups", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Allow all for authenticated (TEMP DEBUG)": {"name": "Allow all for authenticated (TEMP DEBUG)", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "true", "withCheck": "true"}}, "checkConstraints": {"products_price_check": {"name": "products_price_check", "value": "price >= (0)::numeric"}, "products_validity_period_check": {"name": "products_validity_period_check", "value": "validity_period >= 0"}, "products_weight_check": {"name": "products_weight_check", "value": "weight >= (0)::numeric"}}, "isRLSEnabled": false}, "public.session_lock": {"name": "session_lock", "schema": "", "columns": {"lock_key": {"name": "lock_key", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false}, "locked_at": {"name": "locked_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Allow authenticated access": {"name": "Allow authenticated access", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "true", "withCheck": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.column_type": {"name": "column_type", "schema": "public", "values": ["text", "number"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}