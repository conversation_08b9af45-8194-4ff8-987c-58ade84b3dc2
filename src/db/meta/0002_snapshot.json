{"id": "5d9b3fbe-8cd4-47ea-8e96-27dd592da043", "prevId": "eff04783-9b5d-483a-af9c-c869361553b9", "version": "7", "dialect": "postgresql", "tables": {"public.column_settings": {"name": "column_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true}, "order_index": {"name": "order_index", "type": "integer", "primaryKey": false, "notNull": true}, "is_visible": {"name": "is_visible", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "data_type": {"name": "data_type", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"column_settings_name_unique": {"name": "column_settings_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {"Authenticated users can manage column settings": {"name": "Authenticated users can manage column settings", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "true", "withCheck": "true"}, "Public read access for column settings": {"name": "Public read access for column settings", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}, "Allow public insertion for initialization": {"name": "Allow public insertion for initialization", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"], "withCheck": "true"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.product_images": {"name": "product_images", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "yandex_disk_path": {"name": "yandex_disk_path", "type": "text", "primaryKey": false, "notNull": true}, "public_url": {"name": "public_url", "type": "text", "primaryKey": false, "notNull": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true}, "is_main": {"name": "is_main", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"product_images_product_id_fkey": {"name": "product_images_product_id_fkey", "tableFrom": "product_images", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"product_images_product_id_sort_order_key": {"name": "product_images_product_id_sort_order_key", "nullsNotDistinct": false, "columns": ["product_id", "sort_order"]}}, "policies": {"Authenticated users can manage product images": {"name": "Authenticated users can manage product images", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "true", "withCheck": "true"}, "Public read access for product images": {"name": "Public read access for product images", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "weight": {"name": "weight", "type": "numeric", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric", "primaryKey": false, "notNull": true}, "validity_period": {"name": "validity_period", "type": "integer", "primaryKey": false, "notNull": true}, "is_hidden": {"name": "is_hidden", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "custom_fields": {"name": "custom_fields", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"Authenticated users can create products": {"name": "Authenticated users can create products", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "true"}, "Authenticated users can read products": {"name": "Authenticated users can read products", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"]}, "Authenticated users can delete products": {"name": "Authenticated users can delete products", "as": "PERMISSIVE", "for": "DELETE", "to": ["public"]}, "Authenticated users can update products": {"name": "Authenticated users can update products", "as": "PERMISSIVE", "for": "UPDATE", "to": ["public"]}, "Public read access for non-hidden products": {"name": "Public read access for non-hidden products", "as": "PERMISSIVE", "for": "SELECT", "to": ["public"]}, "Anyone can create products": {"name": "Anyone can create products", "as": "PERMISSIVE", "for": "INSERT", "to": ["public"]}}, "checkConstraints": {"products_price_check": {"name": "products_price_check", "value": "price >= (0)::numeric"}, "products_validity_period_check": {"name": "products_validity_period_check", "value": "validity_period >= 0"}, "products_weight_check": {"name": "products_weight_check", "value": "weight >= (0)::numeric"}}, "isRLSEnabled": false}}, "enums": {"public.column_type": {"name": "column_type", "schema": "public", "values": ["text", "number"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}