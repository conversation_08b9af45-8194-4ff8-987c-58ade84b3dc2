ALTER POLICY "Public read access for column settings" ON "column_settings" RENAME TO "ColumnSettings: Authenticated read access";--> statement-breakpoint
ALTER POLICY "Authenticated users can manage column settings" ON "column_settings" RENAME TO "ColumnSettings: Admin/Editor write access";--> statement-breakpoint
ALTER POLICY "Public read access for groups" ON "product_groups" RENAME TO "ProductGroups: Authenticated read access";--> statement-breakpoint
ALTER POLICY "Authenticated users can manage groups" ON "product_groups" RENAME TO "ProductGroups: Admin/Editor write access";--> statement-breakpoint
ALTER POLICY "Public read access for product images" ON "product_images" RENAME TO "ProductImages: Authenticated read access";--> statement-breakpoint
ALTER POLICY "Authenticated users can manage product images" ON "product_images" RENAME TO "ProductImages: Admin/Editor write access";--> statement-breakpoint
ALTER POLICY "Authenticated users can read products" ON "products" RENAME TO "Products: Authenticated read access";--> statement-breakpoint
ALTER POLICY "Authenticated users can create products" ON "products" RENAME TO "Products: Admin/Editor write access";--> statement-breakpoint
DROP POLICY "Authenticated users can delete products" ON "products" CASCADE;--> statement-breakpoint
DROP POLICY "Authenticated users can update products" ON "products" CASCADE;--> statement-breakpoint
DROP POLICY "Public read access for non-hidden products" ON "products" CASCADE;--> statement-breakpoint
DROP POLICY "Anyone can create products" ON "products" CASCADE;