CREATE TABLE "column_settings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"display_name" text NOT NULL,
	"order_index" integer NOT NULL,
	"is_visible" boolean DEFAULT true,
	"data_type" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "column_settings_name_unique" UNIQUE("name")
);
--> statement-breakpoint
ALTER TABLE "column_settings" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE POLICY "Authenticated users can manage column settings" ON "column_settings" AS PERMISSIVE FOR ALL TO "authenticated" USING (true) WITH CHECK (true);--> statement-breakpoint
CREATE POLICY "Public read access for column settings" ON "column_settings" AS PERMISSIVE FOR SELECT TO public;