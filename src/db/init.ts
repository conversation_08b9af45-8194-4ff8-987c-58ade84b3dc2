import { supabase } from "../lib/supabase";

/**
 * Инициализирует таблицу column_settings, если она пуста.
 * Эта функция вызывается при запуске приложения.
 */
export async function initializeColumnSettings() {
  console.log("Запуск инициализации column_settings...");

  // Проверяем, есть ли уже записи в таблице
  const { count, error: countError } = await supabase
    .from("column_settings")
    .select("*", { count: "exact", head: true });

  if (countError) {
    console.error("Ошибка при подсчете записей column_settings:", countError);
    return;
  }

  // Если записи уже есть, не добавляем начальные данные
  if (count && count > 0) {
    console.log(
      "Таблица column_settings уже содержит данные, пропускаем инициализацию"
    );
    return;
  }

  // Начальные данные для column_settings
  const defaultColumns = [
    {
      name: "name",
      display_name: "Наименование",
      order_index: 0,
      is_visible: true,
      data_type: "text",
      prefix: null,
      postfix: null,
    },
    {
      name: "weight",
      display_name: "Упаковка",
      order_index: 1,
      is_visible: true,
      data_type: "number",
      prefix: null,
      postfix: "кг",
    },
    {
      name: "validity_period",
      display_name: "Сроки реализации",
      order_index: 2,
      is_visible: true,
      data_type: "number",
      prefix: null,
      postfix: "мес.",
    },
    {
      name: "price",
      display_name: "Цена",
      order_index: 3,
      is_visible: true,
      data_type: "number",
      prefix: null,
      postfix: "₽",
    },
    {
      name: "is_hidden",
      display_name: "Статус",
      order_index: 4,
      is_visible: true,
      data_type: "text",
      prefix: null,
      postfix: null,
    },
    {
      name: "product_images",
      display_name: "Изображения",
      order_index: 5,
      is_visible: true,
      data_type: "text",
      prefix: null,
      postfix: null,
    },
    {
      name: "created_at",
      display_name: "Дата добавления",
      order_index: 6,
      is_visible: true,
      data_type: "text",
      prefix: null,
      postfix: null,
    },
    {
      name: "group_id",
      display_name: "Группа",
      order_index: 7,
      is_visible: true,
      data_type: "text",
      prefix: null,
      postfix: null,
    },
  ];

  // Добавляем начальные данные
  const { error: insertError } = await supabase
    .from("column_settings")
    .insert(defaultColumns);

  if (insertError) {
    console.error(
      "Ошибка при добавлении начальных данных в column_settings:",
      insertError
    );
  } else {
    console.log(
      "Таблица column_settings успешно заполнена начальными данными с префиксами/постфиксами"
    );
  }
}
