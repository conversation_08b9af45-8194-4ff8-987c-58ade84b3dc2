import { useState } from 'react';
import { useUsers, useUserMutations } from '@/hooks/useUsers';
import { useAuth } from '@/lib/auth';
import { Modal } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Divider } from '@/components/ui/Divider';
import { IconButton } from '@/components/ui/IconButton';
import { Plus, Trash2 } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import type { AdminUserView } from '@/services/userService';

interface UserManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type UserRole = 'reader' | 'editor' | 'admin';

export function UserManagementModal({ isOpen, onClose }: UserManagementModalProps) {
  const { user: currentUser } = useAuth();
  const { data: users, isLoading: isLoadingUsers, error: usersError } = useUsers();
  const { createUser, setUserRole, deleteUser } = useUserMutations();

  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserPassword, setNewUserPassword] = useState('');
  const [newUserRole, setNewUserRole] = useState<UserRole>('reader');

  const handleAddUser = async () => {
    if (!newUserEmail.trim() || !newUserPassword.trim()) {
      toast({ title: 'Ошибка', description: 'Email и пароль обязательны.', variant: 'destructive' });
      return;
    }
    if (newUserPassword.length < 6) {
      toast({ title: 'Ошибка', description: 'Пароль должен быть не менее 6 символов.', variant: 'destructive' });
      return;
    }
    try {
      await createUser.mutateAsync({
        email: newUserEmail.trim(),
        password: newUserPassword,
        role: newUserRole === 'admin' ? 'editor' : newUserRole,
      });
      setNewUserEmail('');
      setNewUserPassword('');
      setNewUserRole('reader');
    } catch {
      // Ошибка уже обрабатывается в useUserMutations
    }
  };

  const handleRoleChange = async (userId: string, newRole: UserRole) => {
    const targetUser = users?.find(u => u.id === userId);
    if (currentUser?.id === userId && targetUser?.role === 'admin' && newRole !== 'admin') {
        toast({ title: 'Предупреждение', description: 'Администратор не может изменить свою роль на не-администратора через этот интерфейс.', variant: 'default' });
        return;
    }

    try {
      await setUserRole.mutateAsync({ userId, newRole });
    } catch {
      // Ошибка уже обрабатывается в useUserMutations
    }
  };

  const handleDeleteUser = async (userToDelete: AdminUserView) => {
    if (currentUser?.id === userToDelete.id) {
      toast({ title: 'Ошибка', description: 'Вы не можете удалить свою учетную запись.', variant: 'destructive' });
      return;
    }
    if (window.confirm(`Вы уверены, что хотите удалить пользователя ${userToDelete.email || userToDelete.id}?`)) {
      try {
        await deleteUser.mutateAsync({ userId: userToDelete.id });
      } catch {
        // Ошибка уже обрабатывается в useUserMutations
      }
    }
  };
  
  const displayUsers = users?.filter(u => u.id !== currentUser?.id);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Управление пользователями"
      size="lg"
      bodyClassName="p-6"
      footer={
        <div className="flex justify-end">
          <Button variant="secondary" onClick={onClose}>Закрыть</Button>
        </div>
      }
    >
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-3">Добавить нового пользователя</h3>
          <div className="space-y-3">
            <Input
              id="newUserEmail"
              label="Email"
              type="email"
              value={newUserEmail}
              onChange={(e) => setNewUserEmail(e.target.value)}
              placeholder="<EMAIL>"
              disabled={createUser.isPending}
            />
            <Input
              id="newUserPassword"
              label="Пароль (мин. 6 символов)"
              type="password"
              value={newUserPassword}
              onChange={(e) => setNewUserPassword(e.target.value)}
              placeholder="******"
              disabled={createUser.isPending}
            />
            <div>
              <Label htmlFor="newUserRole" className="mb-1 block text-sm font-medium">Роль</Label>
              <Select
                value={newUserRole}
                onValueChange={(value: UserRole) => setNewUserRole(value)}
                disabled={createUser.isPending}
              >
                <SelectTrigger id="newUserRole">
                  <SelectValue placeholder="Выберите роль" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="reader">Читатель</SelectItem>
                  <SelectItem value="editor">Редактор</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button onClick={handleAddUser} disabled={createUser.isPending} className="w-full sm:w-auto">
              <Plus className="w-4 h-4 mr-2" />
              {createUser.isPending ? 'Добавление...' : 'Добавить пользователя'}
            </Button>
          </div>
        </div>

        <Divider />

        <div>
          <h3 className="text-lg font-semibold mb-3">Существующие пользователи</h3>
          {isLoadingUsers && <p>Загрузка пользователей...</p>}
          {usersError && <p className="text-red-500">Ошибка загрузки пользователей: {usersError.message}</p>}
          {!isLoadingUsers && !usersError && (!displayUsers || displayUsers.length === 0) && (
            <p className="text-gray-500">Нет других пользователей для управления.</p>
          )}
          {!isLoadingUsers && !usersError && displayUsers && displayUsers.length > 0 && (
            <ScrollArea className="h-[300px] border rounded-md">
              <div className="p-1">
                {displayUsers.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between gap-2 p-2 hover:bg-gray-50 rounded"
                  >
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate" title={user.email}>{user.email || 'N/A'}</p>
                      <p className="text-xs text-gray-500">
                        Роль: {user.role || 'не назначена'}
                      </p>
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <Select
                        value={user.role === 'admin' ? 'editor' : (user.role as UserRole) || 'reader'}
                        onValueChange={(newRole: UserRole) => handleRoleChange(user.id, newRole)}
                        disabled={setUserRole.isPending && setUserRole.variables?.userId === user.id || user.role === 'admin'}
                      >
                        <SelectTrigger className="w-[120px] h-8 text-xs">
                          <SelectValue placeholder="Роль" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="reader">Читатель</SelectItem>
                          <SelectItem value="editor">Редактор</SelectItem>
                        </SelectContent>
                      </Select>
                      <IconButton
                        icon={<Trash2 size={16} />}
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:bg-red-100"
                        onClick={() => handleDeleteUser(user)}
                        disabled={(deleteUser.isPending && deleteUser.variables?.userId === user.id) || user.role === 'admin'}
                        tooltip="Удалить пользователя"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </div>
    </Modal>
  );
}
