import { useState, useCallback } from "react";
import {
  uploadFilesAndPublish,
  getPublicFolderContents,
  getDirectFileUrl,
  deleteFile,
  getUploadUrl,
  uploadFile,
} from "../services/yandexDisk";
import { Input } from "@/components/ui/Input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface FolderItem {
  name: string;
  preview?: string;
  public_url?: string;
  type: "dir" | "file";
  directUrl?: string;
}

const UploadTest = () => {
  const [isDragging, setIsDragging] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [publicUrl, setPublicUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [folderContents, setFolderContents] = useState<FolderItem[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isLoadingContents, setIsLoadingContents] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [folderPath, setFolderPath] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isDeletingAll, setIsDeletingAll] = useState(false);
  const [folderName, setFolderName] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string | null>(null);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    setError(null);

    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;

    // Проверяем, что все файлы - изображения
    const isAllImages = files.every((file) => file.type.startsWith("image/"));
    if (!isAllImages) {
      setError("Пожалуйста, загружайте только изображения");
      return;
    }

    setIsLoading(true);
    try {
      // Создаем имя папки на основе времени
      const timestamp = new Date().getTime();
      const targetFolder = `uploads_${timestamp}`;
      const path = `/${targetFolder}`;

      const url = await uploadFilesAndPublish(files, targetFolder);
      setPublicUrl(url);
      setFolderPath(path);
      setFolderName(targetFolder);
      await fetchFolderContents(url);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Произошла ошибка при загрузке"
      );
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleFileSelect = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      if (files.length === 0) return;

      setError(null);
      setIsLoading(true);
      try {
        // Создаем имя папки на основе времени
        const timestamp = new Date().getTime();
        const targetFolder = `uploads_${timestamp}`;
        const path = `/${targetFolder}`;

        const url = await uploadFilesAndPublish(files, targetFolder);
        setPublicUrl(url);
        setFolderPath(path);
        setFolderName(targetFolder);
        await fetchFolderContents(url);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Произошла ошибка при загрузке"
        );
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const handleUploadMore = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      if (files.length === 0 || !folderName || !publicUrl) return;

      // Проверяем, что все файлы - изображения
      const isAllImages = files.every((file) => file.type.startsWith("image/"));
      if (!isAllImages) {
        setError("Пожалуйста, загружайте только изображения");
        return;
      }

      setIsUploading(true);
      setError(null);
      setSuccessMessage(null);

      try {
        // Загружаем файлы по одному в существующую папку
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          setUploadProgress(
            `Загрузка ${i + 1} из ${files.length}: ${file.name}`
          );

          const filePath = `/${folderName}/${file.name}`;
          const uploadUrl = await getUploadUrl(filePath);
          await uploadFile(uploadUrl, file);
        }

        setUploadProgress(null);
        setSuccessMessage(
          `Дополнительные файлы успешно загружены (${files.length} шт.)`
        );

        // Обновляем содержимое папки
        await fetchFolderContents(publicUrl);

        // Скрываем сообщение через 3 секунды
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        setError(
          err instanceof Error
            ? `Ошибка загрузки дополнительных файлов: ${err.message}`
            : "Произошла ошибка при загрузке дополнительных файлов"
        );
        setUploadProgress(null);
      } finally {
        setIsUploading(false);
        // Очищаем input
        if (e.target) {
          e.target.value = "";
        }
      }
    },
    [folderName, publicUrl]
  );

  const fetchFolderContents = async (url: string) => {
    setIsLoadingContents(true);
    try {
      const items = await getPublicFolderContents(url);
      // Фильтруем только файлы изображений
      const imageItems = items
        .filter(
          (item) =>
            item.type === "file" &&
            item.name.match(/\.(jpeg|jpg|png|gif|webp)$/i)
        )
        .map((item) => ({
          name: item.name,
          preview: item.preview,
          public_url: item.public_url,
          type: item.type,
        }));

      setFolderContents(imageItems);
    } catch (err) {
      setError(
        err instanceof Error
          ? `Ошибка получения содержимого папки: ${err.message}`
          : "Произошла ошибка при получении содержимого папки"
      );
    } finally {
      setIsLoadingContents(false);
    }
  };

  const handleImageClick = async (item: FolderItem) => {
    console.log({ item });
    if (!publicUrl) {
      setError("Ссылка на публичную папку недоступна");
      return;
    }

    try {
      const directUrl = await getDirectFileUrl(publicUrl, item.name);

      // Обновляем элемент в массиве folderContents с directUrl
      setFolderContents((prev) =>
        prev.map((i) => (i.name === item.name ? { ...i, directUrl } : i))
      );

      setSelectedImage(directUrl);
      setIsDialogOpen(true);
    } catch (err) {
      setError(
        err instanceof Error
          ? `Ошибка получения прямой ссылки: ${err.message}`
          : "Произошла ошибка при получении прямой ссылки на изображение"
      );
    }
  };

  const handleViewImages = useCallback(() => {
    if (publicUrl) {
      fetchFolderContents(publicUrl);
    }
  }, [publicUrl]);

  const handleDeleteImage = async (item: FolderItem, e: React.MouseEvent) => {
    e.stopPropagation(); // Предотвращаем открытие изображения при клике на кнопку удаления

    if (!folderPath) {
      setError("Путь к папке не найден");
      return;
    }

    // Формируем полный путь к файлу
    const filePath = `${folderPath}/${item.name}`;

    if (window.confirm(`Вы действительно хотите удалить файл ${item.name}?`)) {
      setIsDeleting(item.name);
      setError(null);
      setSuccessMessage(null);

      try {
        await deleteFile(filePath);
        // Обновляем список файлов, удаляя удаленный файл
        setFolderContents((prev) => prev.filter((i) => i.name !== item.name));
        setSuccessMessage(`Файл ${item.name} успешно удален`);

        // Скрываем сообщение через 3 секунды
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        setError(
          err instanceof Error
            ? `Ошибка удаления файла: ${err.message}`
            : "Произошла ошибка при удалении файла"
        );
      } finally {
        setIsDeleting(null);
      }
    }
  };

  const handleDeleteAllImages = async () => {
    if (!folderPath || folderContents.length === 0) {
      return;
    }

    if (
      window.confirm(
        `Вы действительно хотите удалить все ${folderContents.length} файлов?`
      )
    ) {
      setIsDeletingAll(true);
      setError(null);
      setSuccessMessage(null);

      try {
        // Удаляем файлы последовательно
        for (const item of folderContents) {
          const filePath = `${folderPath}/${item.name}`;
          await deleteFile(filePath);
        }

        // Очищаем список файлов
        setFolderContents([]);
        setSuccessMessage(`Все файлы успешно удалены`);

        // Скрываем сообщение через 3 секунды
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        setError(
          err instanceof Error
            ? `Ошибка удаления файлов: ${err.message}`
            : "Произошла ошибка при удалении файлов"
        );
      } finally {
        setIsDeletingAll(false);
      }
    }
  };

  return (
    <div style={{ padding: "20px" }}>
      <h1>Тест загрузки файлов</h1>
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        style={{
          border: `2px dashed ${isDragging ? "#2196f3" : "#ccc"}`,
          borderRadius: "4px",
          padding: "20px",
          textAlign: "center",
          backgroundColor: isDragging ? "rgba(33, 150, 243, 0.1)" : "#fafafa",
          cursor: "pointer",
          transition: "all 0.3s ease",
          marginBottom: "20px",
        }}
      >
        <Input
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          style={{ display: "none" }}
          id="fileInput"
        />
        <label htmlFor="fileInput" style={{ cursor: "pointer" }}>
          {isLoading
            ? "Загрузка..."
            : "Перетащите изображения сюда или кликните для выбора"}
        </label>
      </div>

      {error && (
        <div style={{ color: "red", marginBottom: "10px" }}>{error}</div>
      )}

      {successMessage && (
        <div style={{ color: "green", marginBottom: "10px" }}>
          {successMessage}
        </div>
      )}

      {uploadProgress && (
        <div style={{ color: "blue", marginBottom: "10px" }}>
          {uploadProgress}
        </div>
      )}

      {publicUrl && (
        <div
          style={{
            padding: "10px",
            backgroundColor: "#e3f2fd",
            borderRadius: "4px",
            wordBreak: "break-all",
            marginBottom: "20px",
          }}
        >
          <p>Ссылка на загруженные файлы:</p>
          <a href={publicUrl} target="_blank" rel="noopener noreferrer">
            {publicUrl}
          </a>

          <div style={{ display: "flex", gap: "10px", marginTop: "10px" }}>
            <button
              onClick={handleViewImages}
              style={{
                padding: "8px 16px",
                backgroundColor: "#2196f3",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
              }}
            >
              {isLoadingContents
                ? "Загрузка..."
                : "Просмотреть загруженные изображения"}
            </button>

            <div style={{ position: "relative" }}>
              <Input
                type="file"
                multiple
                accept="image/*"
                onChange={handleUploadMore}
                style={{ display: "none" }}
                id="uploadMoreInput"
                disabled={isUploading}
              />
              <label
                htmlFor="uploadMoreInput"
                style={{
                  display: "inline-block",
                  padding: "8px 16px",
                  backgroundColor: "#4caf50",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: isUploading ? "not-allowed" : "pointer",
                }}
              >
                {isUploading ? "Загрузка..." : "Догрузить еще фото"}
              </label>
            </div>
          </div>
        </div>
      )}

      {folderContents.length > 0 && (
        <div>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "10px",
            }}
          >
            <h2>Загруженные изображения:</h2>
            <button
              onClick={handleDeleteAllImages}
              disabled={isDeletingAll}
              style={{
                padding: "8px 16px",
                backgroundColor: "#f44336",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: isDeletingAll ? "not-allowed" : "pointer",
              }}
            >
              {isDeletingAll ? "Удаление..." : "Удалить все файлы"}
            </button>
          </div>
          <div style={{ display: "flex", flexWrap: "wrap", gap: "10px" }}>
            {folderContents.map((item) => (
              <div
                key={item.name}
                onClick={() => handleImageClick(item)}
                style={{
                  width: "150px",
                  height: "150px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  overflow: "hidden",
                  cursor: "pointer",
                  position: "relative",
                }}
              >
                <div
                  style={{
                    width: "100%",
                    height: "120px",
                    backgroundImage: item.preview
                      ? `url(${item.preview})`
                      : "none",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundColor: "#f0f0f0",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {!item.preview && (
                    <span style={{ color: "#666", fontSize: "12px" }}>
                      Превью недоступно
                    </span>
                  )}
                </div>
                <div
                  style={{
                    padding: "5px",
                    fontSize: "12px",
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                  }}
                >
                  {item.name}
                </div>
                <button
                  onClick={(e) => handleDeleteImage(item, e)}
                  disabled={isDeleting === item.name}
                  style={{
                    position: "absolute",
                    top: "5px",
                    right: "5px",
                    background: "rgba(255, 0, 0, 0.7)",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    width: "24px",
                    height: "24px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    cursor: "pointer",
                    fontSize: "14px",
                  }}
                >
                  {isDeleting === item.name ? "..." : "✕"}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[80vw] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Просмотр изображения</DialogTitle>
          </DialogHeader>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              overflow: "auto",
              maxHeight: "70vh",
              margin: "20px 0",
            }}
          >
            {selectedImage && (
              <img
                src={selectedImage}
                alt="Просмотр изображения"
                style={{
                  maxWidth: "100%",
                  maxHeight: "70vh",
                  objectFit: "contain",
                }}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UploadTest;
