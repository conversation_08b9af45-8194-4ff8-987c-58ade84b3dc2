.ag-theme-compact {
  --ag-row-height: 28px;
  --ag-header-height: 32px;
  --ag-list-item-height: 24px;
  --ag-cell-horizontal-padding: 8px;
  --ag-cell-vertical-padding: 2px;
  --ag-grid-size: 2px;
  --ag-widget-container-vertical-padding: 2px;
  --ag-widget-container-horizontal-padding: 4px;
  --ag-widget-vertical-spacing: 2px;
}

.ag-theme-compact .ag-row {
  border-bottom-width: 1px;
}

.ag-theme-compact .ag-cell {
  padding-top: 2px;
  padding-bottom: 2px;
  line-height: 1.2;
  display: flex;
  align-items: center;
}

.ag-theme-compact .ag-header-cell {
  padding-top: 4px;
  padding-bottom: 4px;
  line-height: 1.2;
}

.ag-theme-compact .ag-header-cell-label {
  padding: 0;
}

.ag-theme-compact .ag-header-cell-text {
  font-size: 13px;
  font-weight: 600;
}

.ag-theme-compact .ag-cell-value {
  font-size: 13px;
  line-height: 1.2;
}

.ag-theme-compact .ag-checkbox {
  margin: 0;
}

.ag-theme-compact .ag-checkbox-input-wrapper {
  width: 16px;
  height: 16px;
}

.ag-theme-compact .ag-selection-checkbox {
  margin: 0;
}

.ag-theme-compact .ag-group-expanded,
.ag-theme-compact .ag-group-contracted {
  margin-right: 4px;
}

.ag-theme-compact .ag-icon {
  font-size: 12px;
}

.ag-theme-compact .ag-cell-edit-input {
  padding: 2px 4px;
  font-size: 13px;
  line-height: 1.2;
}

.ag-theme-compact .ag-popup-editor {
  padding: 2px;
}

.ag-theme-compact .ag-filter-toolpanel-group-level-0-header {
  height: 28px;
  line-height: 28px;
}

.ag-theme-compact .ag-filter-toolpanel-group-item {
  height: 24px;
  line-height: 24px;
}

.ag-theme-compact .group-header-row {
  height: 28px !important;
  max-height: 28px !important;
  min-height: 28px !important;
  overflow: hidden;
}

.ag-theme-compact .group-header-row > div {
  height: 28px !important;
  max-height: 28px !important;
  min-height: 28px !important;
  box-sizing: border-box;
}

.ag-theme-compact .group-header-row .flex {
  padding: 2px 8px !important;
  min-height: 28px !important;
  height: 28px !important;
  max-height: 28px !important;
  box-sizing: border-box;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.ag-theme-compact .group-header-row .text-sm {
  font-size: 13px;
  line-height: 1.2;
}

.ag-theme-compact .group-header-row .px-2 {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.ag-theme-compact .group-header-row .py-0\.5 {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
}

.ag-theme-compact .group-header-row .ml-1 {
  margin-left: 4px !important;
}

.ag-theme-compact .group-header-row .gap-1 {
  gap: 4px !important;
  align-items: center !important;
  height: 100% !important;
}

.ag-theme-compact .group-header-row .p-0\.5 {
  padding: 2px !important;
}

.ag-theme-compact .group-header-row .px-1\.5 {
  padding-left: 6px !important;
  padding-right: 6px !important;
}

.ag-theme-compact .group-header-row .py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.ag-theme-compact .group-header-row input {
  height: 20px !important;
  line-height: 1.2 !important;
  font-size: 13px !important;
  padding: 2px 6px !important;
  margin: 0 !important;
}

.ag-theme-compact .group-header-row .text-xs {
  font-size: 11px !important;
  line-height: 1.2 !important;
}

.ag-theme-compact .group-header-row .flex.gap-1 {
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}

.ag-theme-compact .group-header-row .flex.items-center {
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}

.ag-theme-compact .group-header-row button {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 !important;
}

.ag-theme-compact .group-header-row svg {
  width: 12px;
  height: 12px;
}

.ag-theme-compact .ag-full-width-row {
  border-bottom: 1px solid var(--ag-border-color);
  height: 28px !important;
  min-height: 28px !important;
  max-height: 28px !important;
}

.ag-theme-compact .ag-full-width-container {
  height: 28px !important;
  min-height: 28px !important;
  max-height: 28px !important;
}

.ag-theme-compact .ag-full-width-viewport {
  height: 28px !important;
  min-height: 28px !important;
  max-height: 28px !important;
}

.ag-theme-compact .ag-row-drag {
  margin-right: 4px;
}

.ag-theme-compact .ag-drag-handle {
  margin: 0;
  padding: 0;
  width: 16px;
  height: 16px;
}

.ag-theme-compact .ag-cell-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
}

.ag-theme-compact .ag-cell-inline-editing {
  padding: 0;
}

.ag-theme-compact .ag-cell-inline-editing input {
  padding: 2px 4px;
  font-size: 13px;
  line-height: 1.2;
  height: 22px;
}

.ag-theme-compact .ag-floating-filter-input {
  height: 28px;
  padding: 2px 4px;
  font-size: 13px;
}

.ag-theme-compact .ag-floating-filter-button {
  height: 28px;
  width: 28px;
}

.ag-theme-compact .ag-header-row {
  min-height: 32px;
}

.ag-theme-compact .ag-header-viewport {
  min-height: 32px;
}

.ag-theme-compact .ag-pinned-left-header {
  min-height: 32px;
}

.ag-theme-compact .ag-pinned-right-header {
  min-height: 32px;
}

.ag-theme-compact .ag-body-viewport {
  overflow-x: auto;
  overflow-y: auto;
}

.ag-theme-compact .ag-center-cols-viewport {
  overflow-x: auto;
}

.ag-theme-compact .ag-body-horizontal-scroll-viewport {
  height: 12px;
}

.ag-theme-compact .ag-horizontal-left-spacer,
.ag-theme-compact .ag-horizontal-right-spacer {
  height: 12px;
}

.ag-theme-compact .ag-body-horizontal-scroll {
  height: 12px;
}

.ag-theme-compact .ag-scrollbar-vertical {
  width: 12px;
}

.ag-theme-compact .ag-scrollbar-horizontal {
  height: 12px;
}

.ag-theme-compact .compact-header {
  padding: 4px 8px !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  line-height: 1.2 !important;
}

.ag-theme-compact .ag-cell-focus {
  border: 1px solid var(--ag-range-selection-border-color) !important;
}

.ag-theme-compact .ag-row-selected {
  background-color: var(--ag-selected-row-background-color) !important;
}

.ag-theme-compact .ag-row-hover {
  background-color: var(--ag-row-hover-color) !important;
}

.ag-theme-compact .ag-cell-range-selected {
  background-color: var(--ag-range-selection-background-color) !important;
}

.ag-theme-compact .ag-row-even {
  background-color: var(--ag-odd-row-background-color);
}

.ag-theme-compact .ag-row-odd {
  background-color: var(--ag-background-color);
}

.ag-theme-compact .ag-header-select-all {
  margin: 0 !important;
}

.ag-theme-compact .ag-selection-checkbox .ag-input-wrapper {
  width: 16px !important;
  height: 16px !important;
}

.ag-theme-compact .ag-checkbox-input {
  width: 16px !important;
  height: 16px !important;
}

.ag-theme-compact .ag-header-checkbox .ag-input-wrapper {
  width: 16px !important;
  height: 16px !important;
}

.ag-theme-compact .ag-cell-wrapper.ag-row-group {
  padding-left: 4px !important;
}

.ag-theme-compact .ag-group-expanded,
.ag-theme-compact .ag-group-contracted {
  margin-right: 2px !important;
}

.ag-theme-compact .ag-row-drag {
  margin-right: 2px !important;
  width: 16px !important;
  height: 16px !important;
}

.ag-theme-compact .ag-drag-handle {
  width: 16px !important;
  height: 16px !important;
  font-size: 12px !important;
}
