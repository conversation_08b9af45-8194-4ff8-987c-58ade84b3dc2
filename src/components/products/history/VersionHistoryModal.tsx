import { useState, useEffect } from "react";
import { format } from "date-fns";
import { ru } from "date-fns/locale";
import {
  History,
  Eye,
  RotateCcw,
  MessageSquare,
  User,
  Trash2,
} from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useVersionHistory } from "../../../hooks/useVersionHistory";
import { Database } from "../../../types/supabase";
import { supabase } from "../../../lib/supabase";
import { Skeleton } from "../../ui/skeleton";

type SnapshotData = {
  products: Array<Database["public"]["Tables"]["products"]["Row"]>;
  product_groups: Array<Database["public"]["Tables"]["product_groups"]["Row"]>;
  column_settings: Array<
    Database["public"]["Tables"]["column_settings"]["Row"]
  >;
};

interface VersionHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPreview: (versionId: string, data: SnapshotData) => void;
  currentPreviewVersionId?: string | null;
  onRollbackComplete?: () => void;
}

interface UserInfo {
  id: string;
  email: string;
  role?: string;
}

export function VersionHistoryModal({
  isOpen,
  onClose,
  onPreview,
  currentPreviewVersionId,
  onRollbackComplete,
}: VersionHistoryModalProps) {
  const {
    versions,
    isLoadingVersions,
    rollbackToVersion,
    isRollingBack,
    deleteVersion,
    isDeletingVersion,
    fetchVersionData,
  } = useVersionHistory();

  const [users, setUsers] = useState<Record<string, UserInfo>>({});
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [loadingVersionId, setLoadingVersionId] = useState<string | null>(null);

  // Fetch user information for version authors
  useEffect(() => {
    if (!versions || versions.length === 0) return;

    const fetchUsers = async () => {
      setLoadingUsers(true);
      const userIds = [...new Set(versions.map((v) => v.user_id))];
      const userMap: Record<string, UserInfo> = {};

      try {
        const { data } = await supabase.functions.invoke(
          "get-users-with-roles"
        );
        if (data && Array.isArray(data)) {
          for (const userId of userIds) {
            const user = data.find(
              (u: { id: string; email: string; role?: string }) =>
                u.id === userId
            );
            if (user) {
              userMap[userId] = {
                id: user.id,
                email: user.email,
                role: user.role,
              };
            }
          }
        }
      } catch (error) {
        console.error("Error fetching user info:", error);
      } finally {
        setLoadingUsers(false);
      }

      setUsers(userMap);
    };

    fetchUsers();
  }, [versions]);

  const handlePreview = async (versionId: string) => {
    setLoadingVersionId(versionId);
    const data = await fetchVersionData(versionId);
    setLoadingVersionId(null);

    if (data) {
      onPreview(versionId, data);
      onClose();
    }
  };

  const handleRollback = async (versionId: string) => {
    if (
      !confirm(
        "Вы уверены, что хотите откатить все данные к этой версии? Это действие необратимо."
      )
    ) {
      return;
    }

    rollbackToVersion(versionId, {
      onSuccess: () => {
        onClose();
        // Notify parent component that rollback completed
        onRollbackComplete?.();
      },
    });
  };

  const handleDelete = async (versionId: string) => {
    if (
      !confirm(
        "Вы уверены, что хотите удалить эту версию? Это действие необратимо."
      )
    ) {
      return;
    }

    deleteVersion(versionId);
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "d MMMM yyyy, HH:mm", { locale: ru });
  };

  const getUserDisplay = (userId: string) => {
    const user = users[userId];
    if (!user) {
      return loadingUsers ? (
        <div className="flex items-center gap-2">
          <Skeleton className="h-3 w-3 rounded-full" />
          <Skeleton className="h-4 w-24" />
        </div>
      ) : (
        <div className="flex items-center gap-2" title={`User ID: ${userId}`}>
          <User className="h-3 w-3 text-gray-400" />
          <span className="text-sm text-gray-500 italic">Email не найден</span>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-2">
        <User className="h-3 w-3" />
        <span className="text-sm">{user.email}</span>
        {user.role && (
          <Badge variant="secondary" size="sm">
            {user.role === "admin"
              ? "Админ"
              : user.role === "editor"
              ? "Редактор"
              : "Читатель"}
          </Badge>
        )}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            История версий
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="h-[500px] pr-4">
          {isLoadingVersions ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="p-4 border rounded-lg space-y-2">
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-3 w-32" />
                  <Skeleton className="h-8 w-full" />
                </div>
              ))}
            </div>
          ) : versions && versions.length > 0 ? (
            <div className="space-y-4">
              {versions.map((version) => (
                <div
                  key={version.id}
                  className={`p-4 border rounded-lg transition-colors ${
                    currentPreviewVersionId === version.id
                      ? "border-blue-500 bg-blue-50"
                      : "hover:bg-gray-50"
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="space-y-1">
                      <div className="font-medium">
                        {formatDate(version.created_at)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {getUserDisplay(version.user_id)}
                      </div>
                    </div>
                    {currentPreviewVersionId === version.id && (
                      <Badge variant="info">Просматривается</Badge>
                    )}
                  </div>

                  {version.comment && (
                    <div className="flex items-start gap-2 mt-2 mb-3">
                      <MessageSquare className="h-4 w-4 text-gray-400 mt-0.5" />
                      <p className="text-sm text-gray-700">{version.comment}</p>
                    </div>
                  )}

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePreview(version.id)}
                      disabled={
                        loadingVersionId === version.id ||
                        isRollingBack ||
                        isDeletingVersion
                      }
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      {loadingVersionId === version.id
                        ? "Загрузка..."
                        : "Просмотреть"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRollback(version.id)}
                      disabled={
                        isRollingBack ||
                        loadingVersionId !== null ||
                        isDeletingVersion
                      }
                      className="text-orange-600 hover:text-orange-700"
                    >
                      <RotateCcw className="h-4 w-4 mr-1" />
                      {isRollingBack ? "Откат..." : "Откатить"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(version.id)}
                      disabled={
                        isRollingBack ||
                        loadingVersionId !== null ||
                        isDeletingVersion
                      }
                      className="text-red-600 hover:text-red-700"
                      title="Удалить версию"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      {isDeletingVersion ? "Удаление..." : "Удалить"}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <History className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>История версий пуста</p>
              <p className="text-sm mt-1">
                Сохраните первую версию, чтобы начать отслеживать изменения
              </p>
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
