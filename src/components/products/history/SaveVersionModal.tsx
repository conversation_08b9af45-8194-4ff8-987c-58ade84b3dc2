import { useState } from "react";
import { Save } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/Button";
import { Textarea } from "@/components/ui/textarea";

interface SaveVersionModalProps {
  isOpen: boolean;
  onSave: (comment?: string) => void;
  onCancel: () => void;
}

export function SaveVersionModal({
  isOpen,
  onSave,
  onCancel,
}: SaveVersionModalProps) {
  const [comment, setComment] = useState("");

  const handleSave = () => {
    onSave(comment.trim() || undefined);
    setComment("");
  };

  const handleCancel = () => {
    onCancel();
    setComment("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleCancel}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Save className="h-5 w-5" />
            Сохранить версию
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <label
              htmlFor="version-comment"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Комментарий к версии (необязательно)
            </label>
            <Textarea
              id="version-comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Добавьте описание изменений..."
              rows={3}
              className="w-full"
            />
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Отмена
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-1" />
            Сохранить
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
