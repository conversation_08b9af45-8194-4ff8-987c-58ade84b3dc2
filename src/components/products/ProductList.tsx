import { useEffect, useState, useCallback } from "react";
import { AgGridReact } from "ag-grid-react";
import { useProducts, useProductMutations } from "../../hooks/useProducts";
import { useAuth } from "../../lib/auth";
import { ColumnSettingsModal } from "./modals/ColumnSettingsModal";
import { toast } from "@/hooks/use-toast";
import { ProductToolbar } from "./ProductToolbar";
import { PDFExportSettingsModal } from "./export/PDFExportSettingsModal";
import { useColumnSettings } from "../../hooks/useColumnSettings";
import { VersionHistoryModal } from "./history/VersionHistoryModal";
import { SaveVersionModal } from "./history/SaveVersionModal";
import { useVersionHistory } from "../../hooks/useVersionHistory";
import {
  IsFullWidthRowParams,
  GetRowIdParams,
  RowDragEndEvent,
  IRowNode,
  RowDragMoveEvent,
  RowDragLeaveEvent,
} from "ag-grid-community";
import { useQueryClient } from "@tanstack/react-query";
import {
  useProductGroups,
  useProductGroupMutations,
} from "../../hooks/useProductGroups";
import type { Database } from "../../types/supabase"; // Импортируем тип Database

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import "./ProductList.css";

// Импорт хуков, созданных при рефакторинге
import { useProductGrid } from "./grid/useProductGrid";
import { useProductColumns } from "./columns/useProductColumns";
import { useProductExport } from "./export/useProductExport";
import {
  GroupHeaderRenderer,
  GroupHeaderRow,
} from "./grid/GroupHeaderRenderer"; // Импортируем GroupHeaderRow
import { useProductEditHandlers } from "./editHandlers/useProductEditHandlers";
import { autoFocusNewProduct, autoFocusNewGroup } from "./utils/autoFocusUtils";
import { useAutoSizing } from "./hooks/useAutoSizing";
import { useHorizontalScroll } from "../../hooks/useHorizontalScroll";
import { useEditingState } from "./hooks/useEditingState";

// Определяем тип Product локально для ясности внутри этого файла
type Product = Database["public"]["Tables"]["products"]["Row"] & {
  product_images?: Array<Database["public"]["Tables"]["product_images"]["Row"]>;
};
type ProductUpdate = Database["public"]["Tables"]["products"]["Update"];
type RowData = Product | GroupHeaderRow; // Корректный тип для данных строки

export function ProductList() {
  const { signOut, userRole } = useAuth();
  const [showHidden, setShowHidden] = useState(false);
  const [isExportSettingsOpen, setIsExportSettingsOpen] = useState(false);
  const [isColumnSettingsOpen, setIsColumnSettingsOpen] = useState(false);
  const [isVersionHistoryOpen, setIsVersionHistoryOpen] = useState(false);
  const [isPreviewing, setIsPreviewing] = useState(false);
  const [previewData, setPreviewData] = useState<{
    products: Product[];
    product_groups: Array<
      Database["public"]["Tables"]["product_groups"]["Row"]
    >;
    column_settings: Array<
      Database["public"]["Tables"]["column_settings"]["Row"]
    >;
  } | null>(null);
  const [currentPreviewVersionId, setCurrentPreviewVersionId] = useState<
    string | null
  >(null);
  const queryClient = useQueryClient();

  // Version history hook
  const { saveVersion, collectCurrentData, rollbackToVersion } =
    useVersionHistory();

  // Состояние и обработчики таблицы
  const { gridApi, selectedRows, onGridReady, onSelectionChanged } =
    useProductGrid();

  // Колонки таблицы - в режиме предварительного просмотра ведем себя как reader
  const effectiveUserRole = isPreviewing ? "reader" : userRole;
  const { columnDefs, defaultColDef } = useProductColumns({
    userRole: effectiveUserRole,
    previewColumnSettings:
      isPreviewing && previewData ? previewData.column_settings : null,
    previewGroupsData:
      isPreviewing && previewData ? previewData.product_groups : null,
    isPreviewMode: isPreviewing,
  });

  // Auto-sizing functionality
  const { triggerAutoSize, triggerColumnAutoSize, initializeAutoSizing } =
    useAutoSizing(gridApi);

  // Horizontal scrolling functionality
  const tableContainerRef = useHorizontalScroll<HTMLDivElement>({
    enabled: true,
    scrollSpeed: 1,
    smoothScrolling: false,
    scrollSensitivity: 0.8,
  });

  // Editing state management for controlling row dragging
  const {
    isAnyEditing,
    onCellEditingStarted,
    onCellEditingStopped,
    setGroupEditing,
  } = useEditingState();

  // Log when row dragging is suppressed due to editing
  useEffect(() => {
    const shouldSuppressRowDrag =
      effectiveUserRole === "reader" || isAnyEditing;
    console.log("[ProductList] Row drag suppression:", {
      effectiveUserRole,
      isAnyEditing,
      shouldSuppressRowDrag,
    });

    // Dynamically update suppressRowDrag and rowDragEntireRow using gridApi
    if (gridApi) {
      gridApi.setGridOption("suppressRowDrag", shouldSuppressRowDrag);
      // Also disable entire row dragging when editing
      gridApi.setGridOption("rowDragEntireRow", !shouldSuppressRowDrag);
    }
  }, [effectiveUserRole, isAnyEditing, gridApi]);

  // Обработчики редактирования - используем селективное изменение размера для редактирования ячеек
  const { onCellValueChanged } = useProductEditHandlers(
    triggerAutoSize, // Fallback для полного изменения размера таблицы
    triggerColumnAutoSize // Селективное изменение размера колонки при редактировании
  );

  // Экспорт в PDF и Excel
  const {
    isExporting,
    isPreviewingPDF,
    isExportingExcel,
    handleExportToPDF,
    handleExportToExcel,
  } = useProductExport();

  // Загрузка групп
  const { data: groupsData } = useProductGroups();
  const { data: columnSettingsData } = useColumnSettings();

  // Use preview data for groups and column settings if in preview mode
  const displayGroupsData =
    isPreviewing && previewData
      ? { data: previewData.product_groups }
      : groupsData;

  const displayColumnSettingsData =
    isPreviewing && previewData
      ? { data: previewData.column_settings }
      : columnSettingsData;

  // Мутации продуктов и групп
  const {
    remove,
    toggleVisibility,
    create,
    update: updateProduct,
  } = useProductMutations();
  const { create: createGroup, bulkUpdateOrder: bulkUpdateGroupOrder } =
    useProductGroupMutations();

  // --- НОВОЕ: Функции для полноширинных строк ---
  const isFullWidthRow = useCallback(
    (params: IsFullWidthRowParams): boolean => {
      // Проверяем, существуют ли данные и есть ли у них свойство isGroupHeader
      return !!params.rowNode?.data?.isGroupHeader;
    },
    []
  );

  const getRowId = useCallback((params: GetRowIdParams): string => {
    if (params.data?.isGroupHeader) {
      return `group-${params.data.groupId}`;
    }
    // Убеждаемся, что данные и id существуют перед обращением
    return params.data?.id ?? `temp-${Math.random()}`; // Предоставляем запасной вариант или обрабатываем ошибку
  }, []);

  const getRowHeight = useCallback((): number => {
    // Все строки (включая группы) должны иметь одинаковую высоту для компактности
    return 28;
  }, []);
  // -------------------------------------------

  // =======================================================================
  // ===== УЛУЧШЕННАЯ ФУНКЦИЯ handleRowDragEnd =============================
  // =======================================================================
  const handleRowDragEnd = useCallback(
    async (event: RowDragEndEvent<RowData>) => {
      const movingNode = event.node;
      const overNode = event.overNode;

      if (!overNode || !gridApi || !movingNode.data) {
        console.log(
          "[DragEnd] Недопустимое состояние или сброс за пределами таблицы."
        );
        return;
      }

      console.log("[DragEnd] Начало обработки завершения перетаскивания", {
        movingNodeType:
          "isGroupHeader" in movingNode.data && movingNode.data.isGroupHeader
            ? "Группа"
            : "Продукт",
        movingNodeId: movingNode.id,
        overNodeId: overNode.id,
        overIndex: event.overIndex,
      });

      // --- Получаем текущий визуальный порядок из сетки ---
      const displayedRowsData: RowData[] = [];
      gridApi.forEachNodeAfterFilterAndSort((node: IRowNode<RowData>) => {
        if (node.data) {
          displayedRowsData.push(node.data);
        }
      });

      // Type guard функция для определения GroupHeaderRow
      const isGroupHeader = (item: RowData): item is GroupHeaderRow => {
        return "isGroupHeader" in item && item.isGroupHeader === true;
      };

      // Type guard функция для определения Product
      const isProduct = (item: RowData): item is Product => {
        return (
          "id" in item &&
          !("isGroupHeader" in item && item.isGroupHeader === true)
        );
      };

      // --- Сценарий 1: Перемещение ПРОДУКТА ---
      if (isProduct(movingNode.data)) {
        const movedProduct = movingNode.data; // Теперь TypeScript знает, что это Product
        const originalGroupId = movedProduct.group_id;
        const originalOrderInGroup = movedProduct.order_in_group;

        console.log("[DragEnd][Продукт] Перемещение продукта:", {
          id: movedProduct.id,
          name: movedProduct.name,
          originalGroupId: originalGroupId ?? "без группы",
          originalOrder: originalOrderInGroup,
        });

        // --- Определяем ID целевой группы ---
        let targetGroupId: string | null = null;
        let lastSeenGroupId: string | null = null;

        for (const row of displayedRowsData) {
          if (isGroupHeader(row)) {
            lastSeenGroupId = row.groupId === "ungrouped" ? null : row.groupId;
            // Если сброшено прямо на заголовок группы
            if (
              overNode.data &&
              isGroupHeader(overNode.data) &&
              row.groupId === overNode.data.groupId
            ) {
              targetGroupId = lastSeenGroupId;
              console.log(
                `[DragEnd][Продукт] Цель определена сбросом НА заголовок группы: ${
                  targetGroupId ?? "без группы"
                }`
              );
              break; // Нашли цель
            }
          } else if (
            isProduct(row) &&
            overNode.data !== undefined &&
            isProduct(overNode.data) &&
            row.id === overNode.data.id
          ) {
            // Если сброшено на продукт, целевая группа - последняя просмотренная группа
            targetGroupId = lastSeenGroupId;
            console.log(
              `[DragEnd][Продукт] Цель определена сбросом НА продукт (ID: ${
                row.id
              }): ${targetGroupId ?? "без группы"}`
            );
            break; // Нашли цель
          }
        }

        // Запасной вариант, если сброшено в самый конец? Должно быть обработано логикой цикла.
        if (targetGroupId === undefined) {
          console.warn(
            "[DragEnd][Продукт] Не удалось точно определить целевую группу, используем последнюю просмотренную группу."
          );
          targetGroupId = lastSeenGroupId;
        }

        console.log(
          `[DragEnd][Продукт] Итоговый ID целевой группы: ${
            targetGroupId ?? "без группы"
          }`
        );

        // --- Собираем продукты в целевой группе (новый порядок) ---
        const targetGroupProducts: Product[] = [];
        let currentProcessingGroupId: string | null = null;

        displayedRowsData.forEach((row) => {
          if (isGroupHeader(row)) {
            currentProcessingGroupId =
              row.groupId === "ungrouped" ? null : row.groupId;
          } else if (isProduct(row)) {
            // Продукт принадлежит целевой группе, если:
            // 1. Это перемещаемый продукт и его целевая группа соответствует
            // 2. Это НЕ перемещаемый продукт, но его текущая группа соответствует цели
            const productGroup = row.group_id;
            if (row.id === movedProduct.id) {
              if (currentProcessingGroupId === targetGroupId) {
                targetGroupProducts.push(row);
                console.log(
                  `[DragEnd][Продукт] Добавлен перемещаемый продукт ${row.id} в список целевой группы.`
                );
              }
            } else if (productGroup === targetGroupId) {
              targetGroupProducts.push(row);
              console.log(
                `[DragEnd][Продукт] Добавлен существующий продукт ${row.id} в список целевой группы.`
              );
            }
          }
        });

        console.log(
          `[DragEnd][Продукт] Собраны продукты для целевой группы (${
            targetGroupId ?? "без группы"
          }):`,
          targetGroupProducts.map((p) => ({ id: p.id, name: p.name }))
        );

        // --- Подготавливаем обновления ---
        const updatesToSend: Array<{ id: string; updates: ProductUpdate }> = [];
        targetGroupProducts.forEach((product, index) => {
          const newOrderInGroup = index;
          const updatePayload: ProductUpdate = {};
          let needsUpdate = false;

          // Проверяем, нужно ли обновлять группу (только для перемещаемого продукта)
          if (
            product.id === movedProduct.id &&
            originalGroupId !== targetGroupId
          ) {
            updatePayload.group_id = targetGroupId;
            needsUpdate = true;
            console.log(
              `[DragEnd][Продукт] Обновление для ${
                product.id
              }: Изменение группы с ${originalGroupId ?? "без группы"} на ${
                targetGroupId ?? "без группы"
              }`
            );
          }

          // Проверяем, нужно ли обновлять порядок
          if (product.order_in_group !== newOrderInGroup) {
            updatePayload.order_in_group = newOrderInGroup;
            needsUpdate = true;
            console.log(
              `[DragEnd][Продукт] Обновление для ${product.id}: Изменение порядка с ${product.order_in_group} на ${newOrderInGroup}`
            );
          }

          if (needsUpdate) {
            updatesToSend.push({ id: product.id, updates: updatePayload });
          }
        });

        console.log(
          `[DragEnd][Продукт] Подготовлено ${updatesToSend.length} обновлений:`,
          updatesToSend
        );

        // --- Выполняем обновления ---
        if (updatesToSend.length > 0) {
          try {
            console.log("[DragEnd][Продукт] Отправка обновлений на сервер...");
            // Используем Promise.all для параллельной отправки обновлений
            await Promise.all(
              updatesToSend.map((upd) => updateProduct.mutateAsync(upd))
            );
            toast({
              title: "Успех",
              description: "Порядок продуктов обновлен",
            });
            console.log("[DragEnd][Продукт] Обновления успешны.");
            // Инвалидация происходит в onSuccess мутации
          } catch (error) {
            console.error(
              "[DragEnd][Продукт] Ошибка при обновлении порядка/группы продукта:",
              error
            );
            toast({
              title: "Ошибка",
              description: "Ошибка при обновлении порядка продуктов",
              variant: "destructive",
            });
            // Рассмотрим откат визуальных изменений или повторную выборку для безопасности
            queryClient.invalidateQueries({ queryKey: ["products"] });
          }
        } else {
          console.log(
            "[DragEnd][Продукт] Не обнаружено изменений порядка или группы, обновления не отправлены."
          );
        }
      }
      // --- Сценарий 2: Перемещение ЗАГОЛОВКА ГРУППЫ ---
      else if (isGroupHeader(movingNode.data)) {
        const groupUpdates: Array<{ id: string; order_index: number }> = [];
        let currentOrderIndex = 0;

        console.log(
          "[DragEnd][Группа] Обработка перемещения заголовка группы."
        );

        displayedRowsData.forEach((row) => {
          if (isGroupHeader(row) && row.groupId !== "ungrouped") {
            const groupId = row.groupId;
            // Проверяем, действительно ли изменился order_index по сравнению с исходными данными
            const originalGroup = displayGroupsData?.data.find(
              (g) => g.id === groupId
            );
            if (
              originalGroup &&
              originalGroup.order_index !== currentOrderIndex
            ) {
              groupUpdates.push({
                id: groupId,
                order_index: currentOrderIndex,
              });
              console.log(
                `[DragEnd][Группа] Требуется обновление для ${groupId}: новый индекс ${currentOrderIndex}`
              );
            } else if (!originalGroup) {
              console.warn(
                `[DragEnd][Группа] Не удалось найти исходные данные для группы ${groupId}`
              );
            } else {
              console.log(
                `[DragEnd][Группа] Нет изменения индекса для ${groupId} (все еще ${currentOrderIndex})`
              );
            }
            currentOrderIndex++;
          }
        });

        console.log(
          `[DragEnd][Группа] Подготовлено ${groupUpdates.length} обновлений групп:`,
          groupUpdates
        );

        if (groupUpdates.length > 0) {
          try {
            console.log(
              "[DragEnd][Группа] Отправка обновлений групп на сервер..."
            );
            await bulkUpdateGroupOrder.mutateAsync(groupUpdates);
            toast({ title: "Успех", description: "Порядок групп обновлен" });
            console.log("[DragEnd][Группа] Обновления групп успешны.");
            // Инвалидация происходит в onSuccess мутации
          } catch (error) {
            console.error(
              "[DragEnd][Группа] Ошибка при обновлении порядка групп:",
              error
            );
            toast({
              title: "Ошибка",
              description: "Ошибка при обновлении порядка групп",
              variant: "destructive",
            });
            queryClient.invalidateQueries({ queryKey: ["product_groups"] });
            queryClient.invalidateQueries({ queryKey: ["products"] }); // Также инвалидируем продукты, так как их отображение зависит от порядка групп
          }
        } else {
          console.log(
            "[DragEnd][Группа] Не обнаружено изменений порядка групп, обновления не отправлены."
          );
        }
      }
    },
    [
      gridApi,
      updateProduct,
      bulkUpdateGroupOrder,
      queryClient,
      displayGroupsData?.data,
    ] // Добавлена зависимость displayGroupsData
  );
  // =======================================================================
  // ===== КОНЕЦ УЛУЧШЕННОЙ handleRowDragEnd ==============================
  // =======================================================================

  const {
    data: productData,
    isLoading,
    error: productsError,
  } = useProducts({
    showHidden,
  });

  // Function to process preview data similar to useProducts logic
  const processPreviewData = useCallback(
    (
      products: Product[],
      groups: Array<Database["public"]["Tables"]["product_groups"]["Row"]>,
      showHiddenProducts: boolean
    ): RowData[] => {
      // Filter products based on showHidden flag
      const filteredProducts = products.filter(
        (product) => product.is_hidden === showHiddenProducts
      );

      const groupMap = new Map(groups.map((g) => [g.id, g]));
      const processedData: RowData[] = [];

      // Count products in each group
      const groupProductCount: Record<string, number> = {};
      filteredProducts.forEach((product) => {
        if (product.group_id) {
          groupProductCount[product.group_id] =
            (groupProductCount[product.group_id] || 0) + 1;
        }
      });

      // Sort products by group order -> order_in_group -> name
      const sortedProducts = [...filteredProducts].sort((a, b) => {
        const groupA = a.group_id ? groupMap.get(a.group_id) : null;
        const groupB = b.group_id ? groupMap.get(b.group_id) : null;

        const groupAOrder = groupA?.order_index ?? Infinity;
        const groupBOrder = groupB?.order_index ?? Infinity;

        if (groupAOrder !== groupBOrder) {
          return groupAOrder - groupBOrder;
        }

        const orderA = a.order_in_group ?? 0;
        const orderB = b.order_in_group ?? 0;
        if (orderA !== orderB) {
          return orderA - orderB;
        }

        return (a.name ?? "").localeCompare(b.name ?? "");
      });

      // Group products by group ID
      const productsByGroup: Record<string, Product[]> = {};
      const ungroupedProducts: Product[] = [];

      sortedProducts.forEach((product) => {
        const normalizedProduct = {
          ...product,
          custom_fields: product.custom_fields || {},
        };

        if (normalizedProduct.group_id) {
          if (!productsByGroup[normalizedProduct.group_id]) {
            productsByGroup[normalizedProduct.group_id] = [];
          }
          productsByGroup[normalizedProduct.group_id].push(normalizedProduct);
        } else {
          ungroupedProducts.push(normalizedProduct);
        }
      });

      // Add group headers and products to final array
      groups.forEach((group) => {
        // Add group header
        processedData.push({
          isGroupHeader: true,
          groupId: group.id,
          groupName: group.name,
          totalProducts: groupProductCount[group.id] || 0,
        });

        // Add products of this group
        const groupProducts = productsByGroup[group.id] || [];
        if (groupProducts.length > 0) {
          processedData.push(...groupProducts);
        }
      });

      // Add ungrouped products at the end
      if (ungroupedProducts.length > 0) {
        processedData.push({
          isGroupHeader: true,
          groupId: "ungrouped",
          groupName: "Без группы",
          totalProducts: ungroupedProducts.length,
        });
        processedData.push(...ungroupedProducts);
      }

      return processedData;
    },
    []
  );

  // Use preview data if in preview mode, otherwise use regular data
  const displayData =
    isPreviewing && previewData
      ? processPreviewData(
          previewData.products,
          previewData.product_groups,
          showHidden
        )
      : productData?.data;

  const handleAddProduct = useCallback(
    (groupId: string | null) => {
      if (!gridApi) return;

      let orderInGroup = 0;
      if (groupId && productData?.data) {
        const productsInGroup = productData.data.filter(
          (item): item is Product =>
            "id" in item &&
            !("isGroupHeader" in item && item.isGroupHeader === true) &&
            item.group_id === groupId
        );
        if (productsInGroup.length > 0) {
          orderInGroup =
            Math.max(...productsInGroup.map((p) => p.order_in_group || 0)) + 1;
        }
      }

      // Создаем новый пустой продукт
      const newProduct = {
        name: "",
        weight: 0,
        price: 0,
        validity_period: 30,
        is_hidden: false,
        custom_fields: {},
        group_id: groupId,
        order_in_group: orderInGroup,
      };

      create.mutate(newProduct, {
        onSuccess: (createdProduct) => {
          // После успешного создания выбираем новую строку и начинаем редактирование
          toast({ title: "Успех", description: "Создана новая запись" });

          // Используем новую утилиту автофокуса
          autoFocusNewProduct(
            gridApi,
            createdProduct.id,
            columnDefs,
            effectiveUserRole
          );
        },
        onError: (error) => {
          toast({
            title: "Ошибка",
            description: "Ошибка при создании записи",
            variant: "destructive",
          });
          console.error("Ошибка при создании продукта:", error);
        },
      });
    },
    [gridApi, create, productData?.data, columnDefs, effectiveUserRole]
  );

  const handleAddGroup = useCallback(async () => {
    if (!groupsData?.data) return;

    try {
      // Находим максимальный текущий order_index
      const maxOrderIndex = groupsData.data.reduce(
        (max, g) => Math.max(max, g.order_index || 0),
        -1
      );

      // Создаем новую группу
      const newGroup = await createGroup.mutateAsync({
        name: "Новая группа",
        order_index: maxOrderIndex + 1,
      });

      // Обновление произойдет через invalidateQueries в мутации

      // Используем новую утилиту автофокуса для групп
      autoFocusNewGroup(gridApi, newGroup.id);
    } catch (error) {
      console.error("Ошибка при создании группы:", error);
    }
  }, [groupsData, createGroup, gridApi]);

  // Отображение ошибки при загрузке данных
  useEffect(() => {
    if (productsError) {
      console.error("[ProductList] Ошибка при загрузке данных:", productsError);
      toast({
        title: "Ошибка",
        description: "Ошибка при загрузке данных продуктов",
        variant: "destructive",
      });
    }
  }, [productsError]);

  const handleToggleVisibility = () => {
    if (selectedRows.length === 0) return;
    const ids = selectedRows.map((row) => row.id);
    const isHidden = !selectedRows[0].is_hidden;
    toggleVisibility.mutate({ ids, isHidden });
  };

  const handleDelete = () => {
    if (selectedRows.length === 0) return;

    console.log(
      `[UI] Попытка удаления ${selectedRows.length} продуктов:`,
      selectedRows.map((row) => row.id)
    );

    if (confirm("Вы уверены, что хотите удалить выбранные продукты?")) {
      const deleteProducts = async () => {
        for (const row of selectedRows) {
          try {
            await remove.mutateAsync(row.id);
          } catch (error) {
            console.error(
              `[UI] Ошибка при удалении продукта ${row.id}:`,
              error
            );
            // Продолжаем удаление остальных продуктов
          }
        }
      };

      deleteProducts();
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error("Ошибка при выходе:", error);
      toast({
        title: "Ошибка",
        description: "Ошибка при выходе из системы",
        variant: "destructive",
      });
    }
  };

  // Version history handlers
  const [isSaveVersionModalOpen, setIsSaveVersionModalOpen] = useState(false);

  const handleSaveVersion = () => {
    setIsSaveVersionModalOpen(true);
  };

  const handleSaveVersionConfirm = async (comment?: string) => {
    const currentData = await collectCurrentData();
    if (currentData) {
      saveVersion({
        comment: comment || undefined,
        snapshotData: currentData,
      });
    }
    setIsSaveVersionModalOpen(false);
  };

  const handleSaveVersionCancel = () => {
    setIsSaveVersionModalOpen(false);
  };

  const handleVersionPreview = (
    versionId: string,
    data: typeof previewData
  ) => {
    setCurrentPreviewVersionId(versionId);
    setPreviewData(data);
    setIsPreviewing(true);
  };

  const handleExitPreview = () => {
    setCurrentPreviewVersionId(null);
    setPreviewData(null);
    setIsPreviewing(false);

    // Force refresh of column settings when exiting preview mode
    // This ensures that any cached column state is cleared
    queryClient.invalidateQueries({ queryKey: ["columnSettings"] });
  };

  const handleRollbackVersion = () => {
    if (!currentPreviewVersionId) return;

    if (
      !confirm(
        "Вы уверены, что хотите откатить все данные к этой версии? Это действие необратимо."
      )
    ) {
      return;
    }

    rollbackToVersion(currentPreviewVersionId, {
      onSuccess: () => {
        handleExitPreview();
        toast({
          title: "Успех",
          description: "Данные успешно откачены к выбранной версии",
        });
      },
      onError: (error) => {
        console.error("Ошибка при откате версии:", error);
        toast({
          title: "Ошибка",
          description: "Ошибка при откате к версии",
          variant: "destructive",
        });
      },
    });
  };

  const handleRollbackComplete = () => {
    console.log("[ProductList] Rollback completed, forcing column refresh");

    // Force invalidation and refetch of column settings to ensure immediate UI update
    queryClient.invalidateQueries({ queryKey: ["columnSettings"] });
    queryClient.refetchQueries({ queryKey: ["columnSettings"] });

    // Also invalidate products to ensure data consistency
    queryClient.invalidateQueries({ queryKey: ["products"] });
    queryClient.invalidateQueries({ queryKey: ["product_groups"] });

    // Trigger full table auto-sizing after rollback to ensure proper layout
    setTimeout(() => {
      triggerAutoSize();
    }, 200);
  };

  // Обновляем сетку при изменении columnDefs
  useEffect(() => {
    if (gridApi) {
      gridApi.setColumnDefs(columnDefs);
    }
  }, [gridApi, columnDefs]);

  // Force column refresh when switching between preview and live modes
  useEffect(() => {
    console.log("[ProductList] Preview mode changed:", isPreviewing);
    if (gridApi && columnDefs) {
      // Force a complete refresh of column definitions
      gridApi.setColumnDefs(columnDefs);
    }
  }, [isPreviewing, gridApi, columnDefs]);

  // Автоматическое изменение размера колонок при готовности сетки
  useEffect(() => {
    if (gridApi) {
      initializeAutoSizing();

      // Set initial row drag settings
      const shouldSuppressRowDrag =
        effectiveUserRole === "reader" || isAnyEditing;
      gridApi.setGridOption("suppressRowDrag", shouldSuppressRowDrag);
      gridApi.setGridOption("rowDragEntireRow", !shouldSuppressRowDrag);
    }
  }, [gridApi, initializeAutoSizing, effectiveUserRole, isAnyEditing]);

  // Автоматическое изменение размера колонок при изменении данных
  useEffect(() => {
    if (displayData && displayData.length > 0) {
      triggerAutoSize();
    }
  }, [displayData, triggerAutoSize]);

  // Автоматическое изменение размера колонок при изменении определений колонок
  useEffect(() => {
    if (columnDefs && columnDefs.length > 0) {
      triggerAutoSize();
    }
  }, [columnDefs, triggerAutoSize]);

  // Обработчик перемещения при перетаскивании строк
  const handleRowDragMove = useCallback((event: RowDragMoveEvent) => {
    console.log("[DragMove] Перемещение строки над:", {
      movingNode: event.node.data.isGroupHeader
        ? `Группа: ${event.node.data.groupName}`
        : `Продукт: ${event.node.data.name}`,
      overNode: event.overNode?.data
        ? event.overNode.data.isGroupHeader
          ? `Группа: ${event.overNode.data.groupName}`
          : `Продукт: ${event.overNode.data.name}`
        : "Нет узла",
    });
  }, []);

  // Обработчик выхода за пределы таблицы при перетаскивании
  const handleRowDragLeave = useCallback((event: RowDragLeaveEvent) => {
    console.log("[DragLeave] Выход за пределы таблицы:", {
      movingNode: event.node.data.isGroupHeader
        ? `Группа: ${event.node.data.groupName}`
        : `Продукт: ${event.node.data.name}`,
    });
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    );
  }

  // Отображение ошибки загрузки
  if (productsError) {
    return (
      <div className="flex flex-col items-center justify-center h-96 gap-4">
        <div className="text-red-500 text-xl">Ошибка загрузки данных</div>
        <button
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={() =>
            queryClient.invalidateQueries({ queryKey: ["products"] })
          }
        >
          Попробовать снова
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh-2rem)] gap-4 p-4">
      <ProductToolbar
        showHidden={showHidden}
        selectedRows={selectedRows}
        onAddProduct={handleAddProduct}
        onAddGroupClick={handleAddGroup}
        onToggleVisibility={handleToggleVisibility}
        onDelete={handleDelete}
        onExportToPDF={(preview) =>
          handleExportToPDF(
            displayData,
            columnDefs,
            displayColumnSettingsData?.data,
            preview
          )
        }
        onExportToExcel={() =>
          handleExportToExcel(
            displayData,
            columnDefs,
            displayColumnSettingsData?.data
          )
        }
        onExportSettingsClick={() => setIsExportSettingsOpen(true)}
        onColumnSettingsClick={() => setIsColumnSettingsOpen(true)}
        onSignOut={handleSignOut}
        onToggleShowHidden={() => setShowHidden(!showHidden)}
        isExporting={isExporting}
        isPreviewingPDF={isPreviewingPDF}
        isExportingExcel={isExportingExcel}
        hasProducts={!!displayData?.length}
        onSaveVersion={handleSaveVersion}
        onVersionHistoryClick={() => setIsVersionHistoryOpen(true)}
        isPreviewing={isPreviewing}
        onExitPreview={handleExitPreview}
        onRollbackVersion={handleRollbackVersion}
        currentPreviewVersionId={currentPreviewVersionId}
      />

      <div
        ref={tableContainerRef}
        className="flex-grow ag-theme-alpine ag-theme-compact"
      >
        <AgGridReact
          rowData={displayData || []}
          columnDefs={columnDefs}
          defaultColDef={{
            ...defaultColDef,
            sortable: true,
            resizable: true,
          }}
          autoSizeStrategy={{
            type: "fitCellContents",
            skipHeader: false,
          }}
          rowHeight={28}
          headerHeight={32}
          getRowHeight={getRowHeight}
          rowSelection="multiple"
          onGridReady={onGridReady}
          onSelectionChanged={onSelectionChanged}
          onCellValueChanged={onCellValueChanged}
          onCellEditingStarted={onCellEditingStarted}
          onCellEditingStopped={onCellEditingStopped}
          suppressRowClickSelection
          isFullWidthRow={isFullWidthRow}
          fullWidthCellRenderer={GroupHeaderRenderer}
          getRowId={getRowId}
          context={{ effectiveUserRole, setGroupEditing }}
          rowDragManaged={effectiveUserRole !== "reader"}
          rowDragMultiRow={true}
          suppressMoveWhenRowDragging={false}
          onRowDragEnd={handleRowDragEnd}
          animateRows={true}
          onRowDragMove={handleRowDragMove}
          onRowDragLeave={handleRowDragLeave}
        />
      </div>

      {isExportSettingsOpen && (
        <PDFExportSettingsModal
          isOpen={isExportSettingsOpen}
          onClose={() => setIsExportSettingsOpen(false)}
          columnDefs={columnDefs}
        />
      )}

      {isColumnSettingsOpen && (
        <ColumnSettingsModal
          isOpen={isColumnSettingsOpen}
          onClose={() => setIsColumnSettingsOpen(false)}
        />
      )}

      {isVersionHistoryOpen && (
        <VersionHistoryModal
          isOpen={isVersionHistoryOpen}
          onClose={() => setIsVersionHistoryOpen(false)}
          onPreview={handleVersionPreview}
          currentPreviewVersionId={currentPreviewVersionId}
          onRollbackComplete={handleRollbackComplete}
        />
      )}

      {isSaveVersionModalOpen && (
        <SaveVersionModal
          isOpen={isSaveVersionModalOpen}
          onSave={handleSaveVersionConfirm}
          onCancel={handleSaveVersionCancel}
        />
      )}
    </div>
  );
}
