import type { Database } from "../../../types/supabase";

type Product = Database["public"]["Tables"]["products"]["Row"];

/**
 * Проверка является ли поле пользовательским по колонке или параметрам редактора
 */
export function isCustomFieldColumn(
  field: string | undefined,
  cellEditorParams: Record<string, unknown> | undefined
): boolean {
  if (!field) return false;
  return (
    field.startsWith("custom_fields.") ||
    cellEditorParams?.isCustomField === true
  );
}

/**
 * Получает имя пользовательского поля из пути колонки
 */
export function extractCustomFieldName(field: string | undefined): string {
  if (!field) return "unknown_field";

  if (field.startsWith("custom_fields.")) {
    return field.split(".")[1];
  }
  if (field.startsWith("custom_field.")) {
    return field.split(".")[1];
  }

  return field;
}

/**
 * Преобразует пользовательские поля продукта из строки в объект при необходимости
 */
export function parseProductCustomFields(
  product: Product
): Record<string, unknown> {
  if (!product.custom_fields) {
    return {};
  }

  // Если это строка, пытаемся распарсить JSON
  if (typeof product.custom_fields === "string") {
    try {
      return JSON.parse(product.custom_fields);
    } catch (e) {
      console.error("[ERROR] Не удалось распарсить строку custom_fields:", e);
      return {};
    }
  }

  // Если это объект, используем его
  if (
    typeof product.custom_fields === "object" &&
    product.custom_fields !== null
  ) {
    return product.custom_fields as Record<string, unknown>;
  }

  return {};
}

/**
 * Подготавливает значение пользовательского поля для сохранения
 */
export function prepareCustomFieldValue(
  value: unknown,
  isNumberField: boolean
): unknown {
  // Если это числовое поле и значение строка, преобразуем в число
  if (isNumberField && typeof value === "string") {
    const parsedValue = parseFloat(value);
    if (!isNaN(parsedValue)) {
      return parsedValue;
    }
  }

  return value;
}

/**
 * Обновляет пользовательское поле в объекте custom_fields
 */
export function updateCustomFieldValue(
  currentFields: Record<string, unknown>,
  fieldName: string,
  value: unknown,
  isNumberField: boolean
): Record<string, unknown> {
  const updatedValue = prepareCustomFieldValue(value, isNumberField);

  // Создаем новую копию объекта
  const newFields = { ...currentFields };
  newFields[fieldName] = updatedValue;

  return newFields;
}

/**
 * Подготавливает пользовательские поля для отправки на сервер
 */
export function prepareCustomFieldsForSave(
  customFields: Record<string, unknown>
): Record<string, unknown> {
  // Преобразуем в корректный JSON формат для Supabase
  try {
    // Глубокое клонирование объекта через JSON
    return JSON.parse(JSON.stringify(customFields));
  } catch (error) {
    console.error("Ошибка при форматировании custom_fields:", error);
    return customFields;
  }
}
