import {
  Eye,
  EyeOff,
  FileText,
  LogOut,
  Plus,
  <PERSON>tings,
  Trash2,
  Menu,
  Columns,
  Layers,
  Download,
  ChevronDown,
  FileSpreadsheet,
  Users,
  Save,
  History,
  RotateCcw,
} from "lucide-react";
import { useProductGroups } from "../../hooks/useProductGroups";
import { useState } from "react";
import { useAuth } from "../../lib/auth";
import type { Database } from "../../types/supabase";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Divider } from "@/components/ui/Divider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet";
import { ProductGroupsModal } from "./modals/ProductGroupsModal";
import { UserManagementModal } from "../admin/users/UserManagementModal";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ScrollArea } from "@/components/ui/scroll-area";
import { IconButton } from "@/components/ui/IconButton";

type Product = Database["public"]["Tables"]["products"]["Row"];

export interface ProductToolbarProps {
  showHidden: boolean;
  selectedRows: Product[];
  hasProducts: boolean;
  isExporting: boolean;
  isPreviewingPDF: boolean;
  isExportingExcel: boolean;
  onAddProduct: (groupId: string | null) => void;
  onAddGroupClick?: () => void;
  onToggleVisibility: () => void;
  onDelete: () => void;
  onExportToPDF: (preview: boolean) => void;
  onExportToExcel: () => void;
  onExportSettingsClick: () => void;
  onColumnSettingsClick: () => void;
  onSignOut: () => void;
  onToggleShowHidden: () => void;
  onSaveVersion?: () => void;
  onVersionHistoryClick?: () => void;
  isPreviewing?: boolean;
  onExitPreview?: () => void;
  onRollbackVersion?: () => void;
  currentPreviewVersionId?: string | null;
}

export function ProductToolbar({
  showHidden,
  selectedRows,
  hasProducts,
  isExporting,
  isPreviewingPDF,
  isExportingExcel,
  onAddProduct,
  onAddGroupClick,
  onToggleVisibility,
  onDelete,
  onExportToPDF,
  onExportToExcel,
  onExportSettingsClick,
  onColumnSettingsClick,
  onSignOut,
  onToggleShowHidden,
  onSaveVersion,
  onVersionHistoryClick,
  isPreviewing,
  onExitPreview,
  onRollbackVersion,
  currentPreviewVersionId,
}: ProductToolbarProps) {
  const { data: groupsData, isLoading: isLoadingGroups } = useProductGroups();
  const { userRole } = useAuth();
  const [isGroupsModalOpen, setIsGroupsModalOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserManagementModalOpen, setIsUserManagementModalOpen] =
    useState(false);

  const hasSelection = selectedRows.length > 0;
  const currentViewTitle = showHidden
    ? "Скрытые продукты"
    : "Активные продукты";

  const handleOpenGroupsModal = () => {
    setIsGroupsModalOpen(true);
    setIsMobileMenuOpen(false);
  };

  const createDropdownMenuItem = (
    label: string,
    icon: React.ReactNode,
    action: () => void,
    disabled: boolean = false,
    isDestructive: boolean = false,
    closeSheetOnClick: boolean = false
  ) => {
    const itemContent = (
      <div className="flex items-center gap-2">
        {icon}
        <span>{label}</span>
      </div>
    );

    const handleClick = () => {
      action();
      if (closeSheetOnClick) {
        setIsMobileMenuOpen(false);
      }
    };

    return (
      <DropdownMenuItem
        onClick={handleClick}
        disabled={disabled}
        className={`${isDestructive ? "text-red-600 focus:text-red-700" : ""}`}
      >
        {itemContent}
      </DropdownMenuItem>
    );
  };

  const SheetActionItem: React.FC<{
    label: string;
    icon: React.ReactNode;
    action: () => void;
    disabled?: boolean;
    variant?: "default" | "secondary" | "destructive" | "outline";
    isToggle?: boolean;
    isActive?: boolean;
  }> = ({
    label,
    icon,
    action,
    disabled = false,
    variant = "secondary",
    isToggle = false,
    isActive = false,
  }) => (
    <SheetClose asChild>
      <Button
        variant={isToggle ? (isActive ? "default" : "outline") : variant}
        onClick={action}
        disabled={disabled}
        className={`w-full justify-start gap-2 px-3 py-2 text-sm ${
          isActive && isToggle ? "font-semibold" : ""
        }`}
      >
        {icon}
        {label}
      </Button>
    </SheetClose>
  );

  const DesktopToolbar = () => (
    <div className="flex items-center justify-between gap-4 w-full">
      <div className="flex items-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="text-xl font-bold text-gray-900 hover:bg-gray-100 px-3 py-2"
              aria-label="Переключить вид продуктов"
            >
              {currentViewTitle}
              <ChevronDown className="ml-2 h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuRadioGroup
              value={showHidden ? "hidden" : "active"}
              onValueChange={(value) => {
                if (
                  (value === "hidden" && !showHidden) ||
                  (value === "active" && showHidden)
                ) {
                  onToggleShowHidden();
                }
              }}
            >
              <DropdownMenuRadioItem value="active">
                Активные продукты
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="hidden">
                Скрытые продукты
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>
        {(userRole === "reader" || isPreviewing) && (
          <Badge variant="info" size="sm" className="ml-2">
            {isPreviewing ? "Режим просмотра версии" : "Только для чтения"}
          </Badge>
        )}
      </div>

      <div className="flex items-center gap-2 flex-grow">
        {userRole !== "reader" && !isPreviewing && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="default" size="sm">
                <Plus className="h-4 w-4 mr-1" />
                Добавить
                <ChevronDown className="h-4 w-4 ml-1 opacity-70" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem onClick={() => onAddProduct(null)}>
                <Plus className="h-4 w-4 mr-2" />
                Продукт (без группы)
              </DropdownMenuItem>
              {!isLoadingGroups &&
                groupsData?.data &&
                groupsData.data.length > 0 && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Добавить в группу</DropdownMenuLabel>
                    {groupsData.data.map((group) => (
                      <DropdownMenuItem
                        key={group.id}
                        onClick={() => onAddProduct(group.id)}
                      >
                        <Plus className="h-4 w-4 mr-2 opacity-50" />
                        {group.name}
                      </DropdownMenuItem>
                    ))}
                  </>
                )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        {userRole !== "reader" && !isPreviewing && onAddGroupClick && (
          <Button onClick={onAddGroupClick} variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-1" />
            Группу
          </Button>
        )}

        {userRole !== "reader" && !isPreviewing && hasSelection && (
          <Divider orientation="vertical" className="h-6 mx-1" />
        )}

        <TooltipProvider delayDuration={100}>
          {userRole !== "reader" && !isPreviewing && hasSelection && (
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  icon={
                    selectedRows[0]?.is_hidden ? (
                      <Eye className="h-4 w-4" />
                    ) : (
                      <EyeOff className="h-4 w-4" />
                    )
                  }
                  onClick={onToggleVisibility}
                  variant="ghost"
                  size="sm"
                  tooltip={
                    selectedRows[0]?.is_hidden
                      ? "Показать выбранные"
                      : "Скрыть выбранные"
                  }
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {selectedRows[0]?.is_hidden
                    ? "Показать выбранные"
                    : "Скрыть выбранные"}
                </p>
              </TooltipContent>
            </Tooltip>
          )}
          {userRole !== "reader" && !isPreviewing && hasSelection && (
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  icon={<Trash2 className="h-4 w-4" />}
                  onClick={onDelete}
                  variant="ghost"
                  size="sm"
                  className="text-red-600 hover:bg-red-100 hover:text-red-700"
                  tooltip="Удалить выбранные"
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>Удалить выбранные</p>
              </TooltipContent>
            </Tooltip>
          )}
        </TooltipProvider>
      </div>

      <div className="flex items-center gap-2">
        {userRole !== "reader" && !isPreviewing && onSaveVersion && (
          <Button onClick={onSaveVersion} variant="outline" size="sm">
            <Save className="h-4 w-4 mr-1" />
            Сохранить версию
          </Button>
        )}

        {isPreviewing && onExitPreview && (
          <>
            <Button onClick={onExitPreview} variant="default" size="sm">
              Вернуться к актуальной версии
            </Button>
            {onRollbackVersion && currentPreviewVersionId && (
              <Button
                onClick={onRollbackVersion}
                variant="outline"
                size="sm"
                className="text-orange-600 hover:text-orange-700"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Откатить к этой версии
              </Button>
            )}
          </>
        )}

        {userRole !== "reader" && !isPreviewing && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-1" />
                Настройки
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {createDropdownMenuItem(
                "Настройка столбцов",
                <Columns className="h-4 w-4" />,
                onColumnSettingsClick
              )}
              {createDropdownMenuItem(
                "Управление группами",
                <Layers className="h-4 w-4" />,
                handleOpenGroupsModal
              )}

              {onVersionHistoryClick && (
                <>
                  <DropdownMenuSeparator />
                  {createDropdownMenuItem(
                    "История версий",
                    <History className="h-4 w-4" />,
                    onVersionHistoryClick
                  )}
                </>
              )}
              {userRole === "admin" && (
                <>
                  <DropdownMenuSeparator />
                  {createDropdownMenuItem(
                    "Управление пользователями",
                    <Users className="h-4 w-4" />,
                    () => setIsUserManagementModalOpen(true)
                  )}
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={
                !hasProducts &&
                !(isExporting || isPreviewingPDF || isExportingExcel)
              }
            >
              <Download className="h-4 w-4 mr-1" />
              Экспорт
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-60">
            {createDropdownMenuItem(
              isPreviewingPDF ? "Открытие..." : "Предпросмотр PDF",
              <FileText className="h-4 w-4" />,
              () => onExportToPDF(true),
              isPreviewingPDF || isExporting || isExportingExcel || !hasProducts
            )}
            {createDropdownMenuItem(
              isExporting ? "Создание PDF..." : "Экспорт в PDF",
              <Download className="h-4 w-4" />,
              () => onExportToPDF(false),
              isExporting || isPreviewingPDF || isExportingExcel || !hasProducts
            )}
            {createDropdownMenuItem(
              isExportingExcel ? "Создание Excel..." : "Экспорт в Excel",
              <FileSpreadsheet className="h-4 w-4" />,
              onExportToExcel,
              isExportingExcel || isExporting || isPreviewingPDF || !hasProducts
            )}
            {createDropdownMenuItem(
              "Настройки экспорта",
              <Settings className="h-4 w-4" />,
              onExportSettingsClick
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <TooltipProvider delayDuration={100}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={onSignOut}
                variant="ghost"
                size="icon"
                className="ml-1 w-9 h-9"
              >
                <LogOut className="h-4 w-4" />
                <span className="sr-only">Выйти</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Выйти</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );

  const MobileToolbar = () => (
    <div className="flex items-center justify-between w-full">
      <h1 className="text-xl font-bold text-gray-900">{currentViewTitle}</h1>
      <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon">
            <Menu />
            <span className="sr-only">Открыть меню</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="right" className="w-[300px] sm:w-[340px] p-0">
          <SheetHeader className="p-4 border-b">
            <SheetTitle>Меню</SheetTitle>
          </SheetHeader>
          <ScrollArea className="h-[calc(100%-57px)]">
            <div className="p-4 space-y-2">
              <div>
                <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                  Вид
                </h3>
                <SheetActionItem
                  label="Активные продукты"
                  icon={<Eye className="h-4 w-4" />}
                  action={() => {
                    if (showHidden) onToggleShowHidden();
                    setIsMobileMenuOpen(false);
                  }}
                  isToggle={true}
                  isActive={!showHidden}
                />
                <SheetActionItem
                  label="Скрытые продукты"
                  icon={<EyeOff className="h-4 w-4" />}
                  action={() => {
                    if (!showHidden) onToggleShowHidden();
                    setIsMobileMenuOpen(false);
                  }}
                  isToggle={true}
                  isActive={showHidden}
                />
              </div>
              <Divider className="my-3" />

              {userRole !== "reader" && !isPreviewing && (
                <div>
                  <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                    Создать
                  </h3>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="secondary"
                        className="w-full justify-start gap-2 px-3 py-2 text-sm"
                      >
                        <Plus className="h-4 w-4" />
                        Добавить продукт
                        <ChevronDown className="h-4 w-4 ml-auto opacity-70" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      side="right"
                      className="w-[calc(100vw-2rem-32px-16px)] min-w-[250px]"
                    >
                      <DropdownMenuItem
                        onClick={() => {
                          onAddProduct(null);
                          setIsMobileMenuOpen(false);
                        }}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Продукт (без группы)
                      </DropdownMenuItem>
                      {!isLoadingGroups &&
                        groupsData?.data &&
                        groupsData.data.length > 0 && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuLabel>
                              Добавить в группу
                            </DropdownMenuLabel>
                            {groupsData.data.map((group) => (
                              <DropdownMenuItem
                                key={group.id}
                                onClick={() => {
                                  onAddProduct(group.id);
                                  setIsMobileMenuOpen(false);
                                }}
                              >
                                <Plus className="h-4 w-4 mr-2 opacity-50" />
                                {group.name}
                              </DropdownMenuItem>
                            ))}
                          </>
                        )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                  {onAddGroupClick && (
                    <SheetActionItem
                      label="Добавить группу"
                      icon={<Plus className="h-4 w-4" />}
                      action={() => {
                        onAddGroupClick();
                        setIsMobileMenuOpen(false);
                      }}
                    />
                  )}
                </div>
              )}

              {userRole !== "reader" && !isPreviewing && hasSelection && (
                <>
                  <Divider className="my-3" />
                  <div>
                    <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                      Действия с выбранным
                    </h3>
                    <SheetActionItem
                      label={
                        selectedRows[0]?.is_hidden
                          ? "Показать выбранные"
                          : "Скрыть выбранные"
                      }
                      icon={
                        selectedRows[0]?.is_hidden ? (
                          <Eye className="h-4 w-4" />
                        ) : (
                          <EyeOff className="h-4 w-4" />
                        )
                      }
                      action={() => {
                        onToggleVisibility();
                        setIsMobileMenuOpen(false);
                      }}
                      disabled={!hasSelection}
                    />
                    <SheetActionItem
                      label="Удалить выбранные"
                      icon={<Trash2 className="h-4 w-4" />}
                      action={() => {
                        onDelete();
                        setIsMobileMenuOpen(false);
                      }}
                      disabled={!hasSelection}
                      variant="destructive"
                    />
                  </div>
                </>
              )}
              <Divider className="my-3" />

              {userRole !== "reader" && !isPreviewing && (
                <div>
                  <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                    Настройки вида
                  </h3>
                  <SheetActionItem
                    label="Настройка столбцов"
                    icon={<Columns className="h-4 w-4" />}
                    action={() => {
                      onColumnSettingsClick();
                      setIsMobileMenuOpen(false);
                    }}
                  />
                  <SheetActionItem
                    label="Управление группами"
                    icon={<Layers className="h-4 w-4" />}
                    action={() => {
                      handleOpenGroupsModal();
                    }}
                  />

                  {onVersionHistoryClick && (
                    <SheetActionItem
                      label="История версий"
                      icon={<History className="h-4 w-4" />}
                      action={() => {
                        onVersionHistoryClick();
                        setIsMobileMenuOpen(false);
                      }}
                    />
                  )}
                </div>
              )}

              {userRole !== "reader" && !isPreviewing && onSaveVersion && (
                <>
                  <Divider className="my-3" />
                  <div>
                    <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                      Версии
                    </h3>
                    <SheetActionItem
                      label="Сохранить версию"
                      icon={<Save className="h-4 w-4" />}
                      action={() => {
                        onSaveVersion();
                        setIsMobileMenuOpen(false);
                      }}
                    />
                  </div>
                </>
              )}

              {isPreviewing && onExitPreview && (
                <>
                  <Divider className="my-3" />
                  <div>
                    <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                      Режим просмотра
                    </h3>
                    <SheetActionItem
                      label="Вернуться к актуальной версии"
                      icon={<History className="h-4 w-4" />}
                      action={() => {
                        onExitPreview();
                        setIsMobileMenuOpen(false);
                      }}
                      variant="default"
                    />
                    {onRollbackVersion && currentPreviewVersionId && (
                      <SheetActionItem
                        label="Откатить к этой версии"
                        icon={<RotateCcw className="h-4 w-4" />}
                        action={() => {
                          onRollbackVersion();
                          setIsMobileMenuOpen(false);
                        }}
                        variant="outline"
                      />
                    )}
                  </div>
                </>
              )}

              {userRole === "admin" && (
                <>
                  <Divider className="my-3" />
                  <div>
                    <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                      Администрирование
                    </h3>
                    <SheetActionItem
                      label="Управление пользователями"
                      icon={<Users className="h-4 w-4" />}
                      action={() => {
                        setIsUserManagementModalOpen(true);
                      }}
                    />
                  </div>
                </>
              )}
              <Divider className="my-3" />

              <div>
                <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                  Экспорт
                </h3>
                <SheetActionItem
                  label={
                    isPreviewingPDF ? "Открытие PDF..." : "Предпросмотр PDF"
                  }
                  icon={<FileText className="h-4 w-4" />}
                  action={() => {
                    onExportToPDF(true);
                    setIsMobileMenuOpen(false);
                  }}
                  disabled={
                    isPreviewingPDF ||
                    isExporting ||
                    isExportingExcel ||
                    !hasProducts
                  }
                  variant="outline"
                />
                <SheetActionItem
                  label={isExporting ? "Создание PDF..." : "Экспорт в PDF"}
                  icon={<Download className="h-4 w-4" />}
                  action={() => {
                    onExportToPDF(false);
                    setIsMobileMenuOpen(false);
                  }}
                  disabled={
                    isExporting ||
                    isPreviewingPDF ||
                    isExportingExcel ||
                    !hasProducts
                  }
                  variant="outline"
                />
                <SheetActionItem
                  label={
                    isExportingExcel ? "Создание Excel..." : "Экспорт в Excel"
                  }
                  icon={<FileSpreadsheet className="h-4 w-4" />}
                  action={() => {
                    onExportToExcel();
                    setIsMobileMenuOpen(false);
                  }}
                  disabled={
                    isExportingExcel ||
                    isExporting ||
                    isPreviewingPDF ||
                    !hasProducts
                  }
                  variant="outline"
                />
                <SheetActionItem
                  label="Настройки экспорта"
                  icon={<Settings className="h-4 w-4" />}
                  action={() => {
                    onExportSettingsClick();
                    setIsMobileMenuOpen(false);
                  }}
                />
              </div>
              <Divider className="my-3" />

              <div>
                <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                  Аккаунт
                </h3>
                <SheetActionItem
                  label="Выйти"
                  icon={<LogOut className="h-4 w-4" />}
                  action={() => {
                    onSignOut();
                    setIsMobileMenuOpen(false);
                  }}
                />
              </div>
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
    </div>
  );

  return (
    <>
      <div className="hidden md:flex w-full">
        <DesktopToolbar />
      </div>
      <div className="flex md:hidden w-full">
        <MobileToolbar />
      </div>

      {isGroupsModalOpen && (
        <ProductGroupsModal
          isOpen={isGroupsModalOpen}
          onClose={() => setIsGroupsModalOpen(false)}
        />
      )}

      {isUserManagementModalOpen && userRole === "admin" && (
        <UserManagementModal
          isOpen={isUserManagementModalOpen}
          onClose={() => setIsUserManagementModalOpen(false)}
        />
      )}
    </>
  );
}
