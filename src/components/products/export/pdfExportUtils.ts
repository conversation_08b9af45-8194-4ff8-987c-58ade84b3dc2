import { ColDef } from "ag-grid-community";
import type { Database } from "../../../types/supabase";

// Определяем тип для настроек колонки из БД, если он еще не импортирован глобально
type DbColumnSetting = Database["public"]["Tables"]["column_settings"]["Row"];

// Интерфейсы для настроек экспорта PDF
export interface PDFHeaderSettings {
  date: string;
  companyName: string;
  office: string;
  officePhone: string;
  warehouse: string;
  warehousePhone: string;
  managerName: string;
  managerContact: string;
  deliveryInfo: string[];
}

export interface PDFExportColumnSettings {
  field: string;
  headerName: string;
  selected: boolean;
  dataType?: "text" | "number";
  prefix?: string | null;
  postfix?: string | null;
}

export interface PDFExportSettings {
  columns: PDFExportColumnSettings[];
  header: PDFHeaderSettings;
  includeGroups: boolean;
}

// Начальные настройки заголовка (переименовываем для ясности)
export const initialHeaderSettings: PDFHeaderSettings = {
  date: new Date().toLocaleDateString("ru-RU"),
  companyName: "ООО Эдельвейс",
  office: "г. Санкт-Петербург, ул. Магнитогорская, 51",
  officePhone: "(812) 313-26-36",
  warehouse: "г. Санкт-Петербург, п. Шушары, Московское шоссе, д.82",
  warehousePhone: "+79119723492",
  managerName: "",
  managerContact: "",
  deliveryInfo: [
    "ДОСТАВКА ДО 500КГ ПО СПБ 5 РУБ/КГ(мин. партия 200кг)",
    "ДОСТАВКА ОТ 500КГ ДО 1000КГ ПО СПБ З РУБ/КГ",
    "ДОСТАВКА ОТ 1000КГ ПО СПБ 1 РУБ/КГ (в том числе по акции)",
  ],
};

// Дефолтные настройки столбцов (переименовываем для ясности)
export const initialColumnSettings: PDFExportColumnSettings[] = [
  {
    field: "name",
    headerName: "Наименование",
    selected: true,
    prefix: null,
    postfix: null,
  },
  {
    field: "weight",
    headerName: "Упаковка",
    selected: true,
    prefix: null,
    postfix: "кг",
  },
  {
    field: "validity_period",
    headerName: "Сроки реализации",
    selected: true,
    prefix: null,
    postfix: "мес.",
  },
  {
    field: "price",
    headerName: "Цена",
    selected: true,
    prefix: null,
    postfix: "₽",
  },
];

// Комплексные начальные настройки
export const initialPDFSettings: PDFExportSettings = {
  columns: initialColumnSettings,
  header: initialHeaderSettings,
  includeGroups: true,
};

// Функция для получения настроек столбцов на основе существующих определений столбцов
export const getColumnSettingsFromDefs = (
  columnDefs: ColDef[],
  allDbColumnSettings: DbColumnSetting[] = []
): PDFExportColumnSettings[] => {
  if (!columnDefs || columnDefs.length === 0) {
    if (allDbColumnSettings.length > 0) {
      return allDbColumnSettings
        .filter((setting) => setting.is_visible && setting.name)
        .sort((a, b) => a.order_index - b.order_index)
        .map((setting) => ({
          field: setting.name.startsWith("field_")
            ? `custom_fields.${setting.name}`
            : setting.name,
          headerName: setting.display_name,
          selected: true,
          dataType: setting.data_type === "number" ? "number" : "text",
          prefix: setting.prefix,
          postfix: setting.postfix,
        }));
    }
    return initialColumnSettings;
  }

  return columnDefs
    .filter((col) => col.field && !col.headerCheckboxSelection)
    .map((colDef) => {
      const fieldName = colDef.field!;
      let originalName = fieldName;
      if (fieldName.startsWith("custom_fields.")) {
        originalName = fieldName.split(".")[1];
      }

      const dbSetting = allDbColumnSettings.find(
        (s) => s.name === originalName
      );

      let dataType: "text" | "number" = "text";
      if (dbSetting) {
        dataType = dbSetting.data_type === "number" ? "number" : "text";
      } else if (
        colDef.cellEditorParams?.dataType === "number" ||
        colDef.cellEditor === "agNumberCellEditor" ||
        ["price", "weight", "validity_period"].includes(originalName)
      ) {
        dataType = "number";
      }

      return {
        field: fieldName,
        headerName: colDef.headerName || fieldName,
        selected: true,
        dataType: dataType,
        prefix: dbSetting?.prefix ?? null,
        postfix: dbSetting?.postfix ?? null,
      };
    });
};

// Функция загрузки настроек из localStorage
export const loadPDFSettings = (): PDFExportSettings => {
  let settings: PDFExportSettings | null = null;

  try {
    const savedSettings = localStorage.getItem("pdfExportSettings");
    if (savedSettings) {
      const parsed = JSON.parse(savedSettings);
      settings = {
        ...initialPDFSettings,
        ...parsed,
        header: {
          ...initialPDFSettings.header,
          ...(parsed.header || {}),
        },
        columns: parsed.columns || initialPDFSettings.columns,
        includeGroups:
          parsed.includeGroups !== undefined
            ? parsed.includeGroups
            : initialPDFSettings.includeGroups,
      };
    }
  } catch (error) {
    console.error("Ошибка при загрузке настроек PDF:", error);
  }

  return settings || initialPDFSettings;
};

// Функция сохранения настроек в localStorage
export const savePDFSettings = (settings: PDFExportSettings) => {
  try {
    localStorage.setItem("pdfExportSettings", JSON.stringify(settings));
  } catch (error) {
    console.error("Ошибка при сохранении настроек PDF:", error);
  }
};
