import { useCallback, useState } from "react";
import { toast } from "@/hooks/use-toast";
import { exportToPDFMake } from "../../../services/pdfmakeExport";
import { exportToExcel } from "../../../services/excelExport";
import type { Database } from "../../../types/supabase";
import {
  loadPDFSettings,
  getColumnSettingsFromDefs,
  PDFExportSettings,
} from "./pdfExportUtils";
import { GroupHeaderRow } from "../grid/GroupHeaderRenderer";
import { ColDef } from "ag-grid-community";

type Product = Database["public"]["Tables"]["products"]["Row"] & {
  product_images?: Array<Database["public"]["Tables"]["product_images"]["Row"]>;
};
type RowData = Product | GroupHeaderRow;
type DbColumnSetting = Database["public"]["Tables"]["column_settings"]["Row"];

export function useProductExport() {
  const [isExporting, setIsExporting] = useState(false);
  const [isPreviewingPDF, setIsPreviewingPDF] = useState(false);
  const [isExportingExcel, setIsExportingExcel] = useState(false);

  const getFinalExportSettings = (
    columnDefs: ColDef[],
    allDbColumnSettings: DbColumnSetting[] | undefined
  ): PDFExportSettings => {
    const loadedSettings = loadPDFSettings();
    const actualExportColumns = getColumnSettingsFromDefs(
      columnDefs,
      allDbColumnSettings || []
    );
    const finalColumns = actualExportColumns.map((colDef) => {
      const savedColumnSetting = loadedSettings.columns.find(
        (savedCol) => savedCol.field === colDef.field
      );
      return {
        ...colDef,
        selected: savedColumnSetting ? savedColumnSetting.selected : true,
      };
    });
    return {
      ...loadedSettings,
      columns: finalColumns,
    };
  };

  const handleExportToPDF = useCallback(
    async (
      data: RowData[] | undefined,
      columnDefs: ColDef[],
      allDbColumnSettings: DbColumnSetting[] | undefined,
      preview = false
    ) => {
      if (!data || data.length === 0) {
        toast({
          title: "Ошибка",
          description: "Нет данных для экспорта",
          variant: "destructive",
        });
        return;
      }

      try {
        if (preview) {
          setIsPreviewingPDF(true);
        } else {
          setIsExporting(true);
        }

        const finalExportSettings = getFinalExportSettings(
          columnDefs,
          allDbColumnSettings
        );

        await exportToPDFMake(data, finalExportSettings, preview);

        if (!preview) {
          toast({ title: "Успех", description: "PDF успешно создан" });
        }
      } catch (error) {
        console.error("Ошибка при создании PDF:", error);
        toast({
          title: "Ошибка",
          description: "Ошибка при создании PDF",
          variant: "destructive",
        });
      } finally {
        if (preview) {
          setIsPreviewingPDF(false);
        } else {
          setIsExporting(false);
        }
      }
    },
    []
  );

  const handleExportToExcel = useCallback(
    async (
      data: RowData[] | undefined,
      columnDefs: ColDef[],
      allDbColumnSettings: DbColumnSetting[] | undefined
    ) => {
      if (!data || data.length === 0) {
        toast({
          title: "Ошибка",
          description: "Нет данных для экспорта",
          variant: "destructive",
        });
        return;
      }

      try {
        setIsExportingExcel(true);
        const finalExportSettings = getFinalExportSettings(
          columnDefs,
          allDbColumnSettings
        );
        await exportToExcel(data, finalExportSettings);
        toast({ title: "Успех", description: "Excel файл успешно создан" });
      } catch (error) {
        console.error("Ошибка при создании Excel:", error);
        toast({
          title: "Ошибка",
          description: "Ошибка при создании Excel файла",
          variant: "destructive",
        });
      } finally {
        setIsExportingExcel(false);
      }
    },
    []
  );

  return {
    isExporting,
    isPreviewingPDF,
    isExportingExcel,
    handleExportToPDF,
    handleExportToExcel,
  };
}
