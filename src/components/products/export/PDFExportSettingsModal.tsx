import { useState, useEffect } from "react";
import { toast } from "@/hooks/use-toast";
import { ColDef } from "ag-grid-community";
import { Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/label";
import { Divider } from "@/components/ui/Divider";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { ScrollArea } from "@/components/ui/scroll-area";
import { IconButton } from "@/components/ui/IconButton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { useColumnSettings } from "../../../hooks/useColumnSettings";
import {
  PDFExportSettings,
  PDFHeaderSettings,
  loadPDFSettings,
  savePDFSettings,
  getColumnSettingsFromDefs,
} from "./pdfExportUtils";

interface PDFExportSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  columnDefs: ColDef[];
}

export const PDFExportSettingsModal: React.FC<PDFExportSettingsModalProps> = ({
  isOpen,
  onClose,
  columnDefs,
}) => {
  const { data: columnSettingsData, isLoading: isLoadingColumnSettings } =
    useColumnSettings();

  const [settings, setSettings] = useState<PDFExportSettings>(() => {
    const loadedSettings = loadPDFSettings();
    if (columnSettingsData?.data) {
      const actualColumns = getColumnSettingsFromDefs(
        columnDefs,
        columnSettingsData.data
      );
      const mergedColumns = actualColumns.map((col) => {
        const savedCol = loadedSettings.columns.find(
          (s) => s.field === col.field
        );
        return {
          ...col,
          selected: savedCol ? savedCol.selected : true,
        };
      });
      return {
        ...loadedSettings,
        columns: mergedColumns,
      };
    }
    return loadedSettings;
  });

  useEffect(() => {
    if (isOpen && columnSettingsData?.data && !isLoadingColumnSettings) {
      const actualColumns = getColumnSettingsFromDefs(
        columnDefs,
        columnSettingsData.data
      );
      setSettings((prevSettings) => {
        const mergedColumns = actualColumns.map((col) => {
          const savedCol = prevSettings.columns.find(
            (s) => s.field === col.field
          );
          return {
            ...col,
            selected: savedCol ? savedCol.selected : true,
          };
        });
        return {
          header: prevSettings.header,
          includeGroups: prevSettings.includeGroups,
          columns: mergedColumns,
        };
      });
    }
  }, [columnDefs, isOpen, columnSettingsData?.data, isLoadingColumnSettings]);

  const updateHeaderSetting = (
    key: keyof PDFHeaderSettings,
    value: string | string[]
  ) => {
    setSettings((prev) => ({
      ...prev,
      header: {
        ...prev.header,
        [key]: value,
      },
    }));
  };

  const updateDeliveryInfo = (index: number, value: string) => {
    const newDeliveryInfo = [...settings.header.deliveryInfo];
    newDeliveryInfo[index] = value;
    updateHeaderSetting("deliveryInfo", newDeliveryInfo);
  };

  const addDeliveryInfoLine = () => {
    updateHeaderSetting("deliveryInfo", [...settings.header.deliveryInfo, ""]);
  };

  const removeDeliveryInfoLine = (index: number) => {
    const newDeliveryInfo = [...settings.header.deliveryInfo];
    newDeliveryInfo.splice(index, 1);
    updateHeaderSetting("deliveryInfo", newDeliveryInfo);
  };

  const toggleColumnSelection = (field: string) => {
    setSettings((prev) => ({
      ...prev,
      columns: prev.columns.map((col) =>
        col.field === field ? { ...col, selected: !col.selected } : col
      ),
    }));
  };

  const toggleIncludeGroups = () => {
    setSettings((prev) => ({
      ...prev,
      includeGroups: !prev.includeGroups,
    }));
  };

  const handleSave = () => {
    savePDFSettings(settings);
    toast({ title: "Успех", description: "Настройки сохранены" });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="max-w-3xl w-[95vw]"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>Настройки экспорта PDF</DialogTitle>
        </DialogHeader>
        {isLoadingColumnSettings && !settings.columns.length ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
          </div>
        ) : (
          <ScrollArea className="max-h-[70vh] pr-6 -mr-6">
            <div className="space-y-6 py-4">
              {/* Секция: Заголовок и Контакты */}
              <div>
                <h3 className="text-lg font-semibold mb-3">
                  Заголовок и Контакты
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                  <div className="space-y-3">
                    <Input
                      id="pdf-date"
                      label="Дата"
                      value={settings.header.date}
                      onChange={(e) =>
                        updateHeaderSetting("date", e.target.value)
                      }
                      placeholder="Дата прайс-листа"
                    />
                    <Input
                      id="pdf-companyName"
                      label="Название компании"
                      value={settings.header.companyName}
                      onChange={(e) =>
                        updateHeaderSetting("companyName", e.target.value)
                      }
                      placeholder="Название компании"
                    />
                    <Input
                      id="pdf-office"
                      label="Офис"
                      value={settings.header.office}
                      onChange={(e) =>
                        updateHeaderSetting("office", e.target.value)
                      }
                      placeholder="Адрес офиса"
                    />
                    <Input
                      id="pdf-officePhone"
                      label="Телефон офиса"
                      value={settings.header.officePhone}
                      onChange={(e) =>
                        updateHeaderSetting("officePhone", e.target.value)
                      }
                      placeholder="Телефон офиса"
                    />
                  </div>
                  <div className="space-y-3">
                    <Input
                      id="pdf-warehouse"
                      label="Склад"
                      value={settings.header.warehouse}
                      onChange={(e) =>
                        updateHeaderSetting("warehouse", e.target.value)
                      }
                      placeholder="Адрес склада"
                    />
                    <Input
                      id="pdf-warehousePhone"
                      label="Телефон склада"
                      value={settings.header.warehousePhone}
                      onChange={(e) =>
                        updateHeaderSetting("warehousePhone", e.target.value)
                      }
                      placeholder="Телефон склада"
                    />
                    <Input
                      id="pdf-managerName"
                      label="Менеджер"
                      value={settings.header.managerName}
                      onChange={(e) =>
                        updateHeaderSetting("managerName", e.target.value)
                      }
                      placeholder="Имя менеджера (необязательно)"
                    />
                    <Input
                      id="pdf-managerContact"
                      label="Контакт менеджера"
                      value={settings.header.managerContact}
                      onChange={(e) =>
                        updateHeaderSetting("managerContact", e.target.value)
                      }
                      placeholder="Телефон/email (необязательно)"
                    />
                  </div>
                </div>
              </div>
              <Divider />
              {/* Секция: Информация о доставке */}
              <div>
                <h3 className="text-lg font-semibold mb-3">
                  Информация о доставке
                </h3>
                <TooltipProvider delayDuration={100}>
                  <div className="space-y-2">
                    {settings.header.deliveryInfo.map((info, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div className="flex-grow">
                          <Input
                            id={`delivery-${index}`}
                            value={info}
                            onChange={(e) =>
                              updateDeliveryInfo(index, e.target.value)
                            }
                            placeholder="Строка информации о доставке"
                          />
                        </div>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <IconButton
                              icon={<Trash2 size={16} />}
                              variant="ghost"
                              size="sm"
                              onClick={() => removeDeliveryInfoLine(index)}
                              className="text-red-600 hover:bg-red-100 flex-shrink-0"
                            />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Удалить строку</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    ))}
                  </div>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={addDeliveryInfoLine}
                        variant="outline"
                        size="sm"
                        className="mt-3"
                      >
                        <Plus size={16} className="mr-1" />
                        Добавить строку
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Добавить новую строку информации о доставке</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Divider />
              {/* Секция: Выбор столбцов */}
              <div>
                <h3 className="text-lg font-semibold mb-3">
                  Столбцы для экспорта
                </h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-x-6 gap-y-3">
                  {settings.columns
                    .filter(
                      (col) => col.field && !col.field.startsWith("checkbox")
                    )
                    .map((col) => (
                      <div key={col.field} className="flex items-center">
                        <Checkbox
                          id={`col-${col.field}`}
                          checked={col.selected}
                          onCheckedChange={() =>
                            toggleColumnSelection(col.field)
                          }
                          aria-labelledby={`label-col-${col.field}`}
                        />
                        <Label
                          htmlFor={`col-${col.field}`}
                          id={`label-col-${col.field}`}
                          className="ml-2 text-sm cursor-pointer select-none"
                        >
                          {col.headerName || col.field}
                        </Label>
                      </div>
                    ))}
                </div>
              </div>
              <Divider />
              {/* Секция: Отображение групп */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Группировка</h3>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="includeGroups"
                    checked={settings.includeGroups}
                    onCheckedChange={toggleIncludeGroups}
                    aria-labelledby="label-includeGroups"
                  />
                  <Label
                    htmlFor="includeGroups"
                    id="label-includeGroups"
                    className="text-sm cursor-pointer select-none"
                  >
                    Включать названия групп
                  </Label>
                </div>
              </div>
            </div>
          </ScrollArea>
        )}
        <DialogFooter className="mt-4">
          <DialogClose asChild>
            <Button variant="outline">Отмена</Button>
          </DialogClose>
          <Button onClick={handleSave}>Сохранить</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
