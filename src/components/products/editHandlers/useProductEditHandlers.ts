import { useCallback } from "react";
import { CellValueChangedEvent } from "ag-grid-community";
import { useProductMutations } from "../../../hooks/useProducts";
import { toast } from "@/hooks/use-toast";
import {
  isCustomFieldColumn,
  extractCustomFieldName,
  parseProductCustomFields,
  updateCustomFieldValue,
  prepareCustomFieldsForSave,
} from "../customFields/customFieldsHandler";
import { GroupHeaderRow } from "../grid/GroupHeaderRenderer";

import type { Database } from "../../../types/supabase";

type Product = Database["public"]["Tables"]["products"]["Row"];
type Json = Database["public"]["Tables"]["products"]["Row"]["custom_fields"];
type RowData = Product | GroupHeaderRow;

export function useProductEditHandlers(
  onEditComplete?: () => void,
  onColumnEditComplete?: (columnId: string) => void
) {
  const { update } = useProductMutations();

  const onCellValueChanged = useCallback(
    (event: CellValueChangedEvent<RowData>) => {
      // Игнорируем редактирование заголовков групп
      if ("isGroupHeader" in event.data && event.data.isGroupHeader) {
        return;
      }

      // Теперь безопасно приводим тип к Product
      const productData = event.data as Product;
      const { colDef } = event;
      const field = colDef.field;

      if (field === "is_hidden") return; // Обрабатываем видимость через тулбар

      // --- Добавляем обработку поля group_id ---
      if (field === "group_id") {
        // Для пустых значений или строки "null" устанавливаем null
        const groupId =
          event.newValue === "" || event.newValue === "null"
            ? null
            : event.newValue;

        update.mutate(
          {
            id: productData.id,
            updates: { group_id: groupId },
          },
          {
            onSuccess: () => {
              if (onColumnEditComplete && field) {
                onColumnEditComplete(field);
              } else {
                onEditComplete?.();
              }
            },
          }
        );
        return; // Прекращаем обработку для group_id
      }
      // -------------------------------------

      // Проверка типа данных для числовых полей
      const isNumberField = colDef.cellEditor === "agNumberCellEditor";
      let newValue = event.newValue;

      // Для числовых полей преобразуем строковые значения в числа
      if (isNumberField && typeof newValue === "string") {
        const parsedValue = parseFloat(newValue);
        if (!isNaN(parsedValue)) {
          newValue = parsedValue;
        }
      }

      // Обработка редактирования пользовательских полей
      // Проверяем, является ли поле динамическим по формату или по маркеру
      const isCustomField = isCustomFieldColumn(
        colDef.field,
        colDef.cellEditorParams
      );

      console.log(`[DEBUG] Изменение ячейки:`, {
        field: colDef.field,
        isCustomField,
        isNumberField,
        cellEditorParams: colDef.cellEditorParams,
        oldValue: event.oldValue,
        newValue,
        newValueType: typeof newValue,
        allData: productData,
      });

      if (isCustomField) {
        // Извлекаем имя поля
        const fieldName = extractCustomFieldName(colDef.field);

        // Получаем объект с пользовательскими полями
        const currentCustomFields = parseProductCustomFields(productData);

        // Обновляем значение в объекте
        const customFields = updateCustomFieldValue(
          currentCustomFields,
          fieldName,
          newValue,
          isNumberField
        );

        console.log(`[DEBUG] Обновление custom_fields:`, {
          id: productData.id,
          fieldName,
          oldValue: currentCustomFields[fieldName],
          newValue,
          newValueType: typeof newValue,
          isNumberField,
          newCustomFields: customFields,
        });

        try {
          // Конвертируем в корректный JSON формат для Supabase
          const cleanValue = prepareCustomFieldsForSave(customFields);

          // Обновляем продукт, отправляя полный обновленный объект custom_fields
          update.mutate(
            {
              id: productData.id,
              updates: {
                custom_fields: cleanValue as Json,
              },
            },
            {
              onSuccess: () => {
                if (onColumnEditComplete && field) {
                  onColumnEditComplete(field);
                } else {
                  onEditComplete?.();
                }
              },
            }
          );
        } catch (error) {
          console.error("Ошибка при форматировании custom_fields:", error);
          toast({
            title: "Ошибка",
            description: "Ошибка при обновлении поля",
            variant: "destructive",
          });
        }
      } else {
        // Обработка редактирования обычных полей
        update.mutate(
          {
            id: productData.id,
            updates: { [colDef.field as string]: newValue },
          },
          {
            onSuccess: () => {
              if (onColumnEditComplete && field) {
                onColumnEditComplete(field);
              } else {
                onEditComplete?.();
              }
            },
          }
        );
      }
    },
    [update, onEditComplete, onColumnEditComplete]
  );

  return {
    onCellValueChanged,
  };
}
