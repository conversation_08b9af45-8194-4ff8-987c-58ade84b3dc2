import { useState, useEffect, useMemo } from "react";
import { toast } from "@/hooks/use-toast";
import { Plus, Trash2, ArrowU<PERSON>, ArrowDown, GripVertical } from "lucide-react";
import {
  useColumnSettings,
  useColumnSettingsMutations,
} from "../../../hooks/useColumnSettings";
import type { Database } from "../../../types/supabase";
import { Button } from "@/components/ui/Button";
import { IconButton } from "@/components/ui/IconButton";
import { Input } from "@/components/ui/Input";
import { Modal } from "@/components/ui/Modal";
import { Divider } from "@/components/ui/Divider";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { isCustomField } from "../columns/productColumnUtils";
import { cn } from "@/lib/utils";

type ColumnSetting = Database["public"]["Tables"]["column_settings"]["Row"];

interface ColumnSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const HIDDEN_IN_SETTINGS_FIELDS = [
  "id",
  "updated_at",
  "order_in_group",
  "custom_fields",
];

function generateUniqueFieldName(): string {
  const uuid = crypto.randomUUID();
  return `field_${uuid.substring(0, 8)}`;
}

export function ColumnSettingsModal({
  isOpen,
  onClose,
}: ColumnSettingsModalProps) {
  const { data: columnSettingsData, isLoading } = useColumnSettings();
  const { create, update, remove, reorder } = useColumnSettingsMutations();
  const [newColumnDisplayName, setNewColumnDisplayName] = useState("");
  const [newColumnType, setNewColumnType] = useState<"text" | "number">("text");
  const [editableDisplayNames, setEditableDisplayNames] = useState<
    Record<string, string>
  >({});
  const [editablePrefixes, setEditablePrefixes] = useState<
    Record<string, string>
  >({});
  const [editablePostfixes, setEditablePostfixes] = useState<
    Record<string, string>
  >({});

  const allColumns = useMemo(
    () => columnSettingsData?.data || [],
    [columnSettingsData?.data]
  );

  const { standardColumns, customColumns } = useMemo(() => {
    const standard: ColumnSetting[] = [];
    const custom: ColumnSetting[] = [];
    allColumns
      .filter((col) => !HIDDEN_IN_SETTINGS_FIELDS.includes(col.name))
      .forEach((col) => {
        if (isCustomField(col.name)) {
          custom.push(col);
        } else {
          standard.push(col);
        }
      });
    // Сортируем оба списка по order_index перед возвратом
    standard.sort((a, b) => a.order_index - b.order_index);
    custom.sort((a, b) => a.order_index - b.order_index);
    return { standardColumns: standard, customColumns: custom };
  }, [allColumns]);

  useEffect(() => {
    if (columnSettingsData?.data) {
      const initialNames: Record<string, string> = {};
      const initialPrefixes: Record<string, string> = {};
      const initialPostfixes: Record<string, string> = {};

      columnSettingsData.data.forEach((col) => {
        initialNames[col.id] = col.display_name;
        initialPrefixes[col.id] = col.prefix || "";
        initialPostfixes[col.id] = col.postfix || "";
      });
      setEditableDisplayNames(initialNames);
      setEditablePrefixes(initialPrefixes);
      setEditablePostfixes(initialPostfixes);
    }
  }, [columnSettingsData?.data]);

  const handleDisplayNameChange = (id: string, value: string) => {
    setEditableDisplayNames((prev) => ({ ...prev, [id]: value }));
  };

  const handleDisplayNameBlur = async (column: ColumnSetting) => {
    const newDisplayName = editableDisplayNames[column.id]?.trim();
    if (newDisplayName && newDisplayName !== column.display_name) {
      try {
        await update.mutateAsync({
          id: column.id,
          updates: { display_name: newDisplayName },
        });
        toast({
          title: "Успех",
          description: `Отображаемое имя для "${column.name}" обновлено`,
        });
      } catch (error) {
        console.error("Ошибка при обновлении имени столбца:", error);
        toast({
          title: "Ошибка",
          description: "Не удалось обновить имя столбца",
          variant: "destructive",
        });
        handleDisplayNameChange(column.id, column.display_name);
      }
    } else {
      handleDisplayNameChange(column.id, column.display_name);
    }
  };

  const handlePrefixChange = (id: string, value: string) => {
    setEditablePrefixes((prev) => ({ ...prev, [id]: value }));
  };

  const handlePostfixChange = (id: string, value: string) => {
    setEditablePostfixes((prev) => ({ ...prev, [id]: value }));
  };

  const handlePrefixBlur = async (column: ColumnSetting) => {
    const newPrefix = editablePrefixes[column.id]?.trim();
    const currentDbPrefix = column.prefix || "";

    if (newPrefix !== undefined && newPrefix !== currentDbPrefix) {
      try {
        await update.mutateAsync({
          id: column.id,
          updates: { prefix: newPrefix === "" ? null : newPrefix },
        });
        toast({
          title: "Успех",
          description: `Префикс для "${column.display_name}" обновлен`,
        });
      } catch (error) {
        console.error("Ошибка при обновлении префикса столбца:", error);
        toast({
          title: "Ошибка",
          description: "Не удалось обновить префикс столбца",
          variant: "destructive",
        });
        handlePrefixChange(column.id, currentDbPrefix);
      }
    } else if (newPrefix === undefined) {
      handlePrefixChange(column.id, currentDbPrefix);
    }
  };

  const handlePostfixBlur = async (column: ColumnSetting) => {
    const newPostfix = editablePostfixes[column.id]?.trim();
    const currentDbPostfix = column.postfix || "";

    if (newPostfix !== undefined && newPostfix !== currentDbPostfix) {
      try {
        await update.mutateAsync({
          id: column.id,
          updates: { postfix: newPostfix === "" ? null : newPostfix },
        });
        toast({
          title: "Успех",
          description: `Постфикс для "${column.display_name}" обновлен`,
        });
      } catch (error) {
        console.error("Ошибка при обновлении постфикса столбца:", error);
        toast({
          title: "Ошибка",
          description: "Не удалось обновить постфикс столбца",
          variant: "destructive",
        });
        handlePostfixChange(column.id, currentDbPostfix);
      }
    } else if (newPostfix === undefined) {
      handlePostfixChange(column.id, currentDbPostfix);
    }
  };

  const handleCreateColumn = async () => {
    if (!newColumnDisplayName.trim()) {
      toast({
        title: "Ошибка",
        description: "Введите отображаемое имя столбца",
        variant: "destructive",
      });
      return;
    }
    const technicalName = generateUniqueFieldName();
    try {
      await create.mutateAsync({
        name: technicalName,
        display_name: newColumnDisplayName.trim(),
        data_type: newColumnType,
        order_index: allColumns.length,
        is_visible: true,
      });
      setNewColumnDisplayName("");
      setNewColumnType("text");
      toast({
        title: "Успех",
        description: "Пользовательский столбец успешно добавлен",
      });
    } catch (error) {
      console.error("Ошибка при создании столбца:", error);
      toast({
        title: "Ошибка",
        description: "Не удалось создать столбец",
        variant: "destructive",
      });
    }
  };

  const handleToggleVisibility = async (
    column: ColumnSetting,
    checked: boolean
  ) => {
    try {
      await update.mutateAsync({
        id: column.id,
        updates: { is_visible: checked },
      });
    } catch (error) {
      console.error("Ошибка при обновлении видимости столбца:", error);
      toast({
        title: "Ошибка",
        description: "Не удалось обновить видимость столбца",
        variant: "destructive",
      });
    }
  };

  const handleDeleteColumn = async (column: ColumnSetting) => {
    if (!isCustomField(column.name)) {
      toast({
        title: "Ошибка",
        description: "Стандартные поля не могут быть удалены.",
        variant: "destructive",
      });
      return;
    }
    if (
      window.confirm(
        `Вы уверены, что хотите удалить столбец "${column.display_name}"? \n\nВНИМАНИЕ: Все данные этого поля будут БЕЗВОЗВРАТНО удалены из всех продуктов!`
      )
    ) {
      try {
        // Вызываем напрямую удаление по ID (так как remove ожидает строку)
        await remove.mutateAsync(column.id);
        toast({
          title: "Успех",
          description: `Столбец "${column.display_name}" удален`,
        });
      } catch (error) {
        console.error("Ошибка при удалении столбца:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Неизвестная ошибка";
        toast({
          title: "Ошибка",
          description: `Не удалось удалить столбец: ${errorMessage}`,
          variant: "destructive",
        });
      }
    }
  };

  const handleMoveColumn = async (
    index: number,
    direction: "up" | "down",
    listType: "standard" | "custom"
  ) => {
    const list = listType === "standard" ? standardColumns : customColumns;
    if (
      (direction === "up" && index === 0) ||
      (direction === "down" && index === list.length - 1)
    ) {
      return;
    }

    const targetIndex = direction === "up" ? index - 1 : index + 1;
    const reorderedList = [...list];
    [reorderedList[index], reorderedList[targetIndex]] = [
      reorderedList[targetIndex],
      reorderedList[index],
    ];

    // Объединяем списки для получения полного порядка
    const combinedList =
      listType === "standard"
        ? [...reorderedList, ...customColumns]
        : [...standardColumns, ...reorderedList];

    // Готовим обновления только для измененных позиций
    const columnsToUpdate = combinedList
      .map((col, idx) => ({
        id: col.id,
        order_index: idx,
        current_order_index:
          allColumns.find((c) => c.id === col.id)?.order_index ?? -1,
      }))
      .filter((col) => col.order_index !== col.current_order_index);

    if (columnsToUpdate.length === 0) return;

    try {
      await reorder.mutateAsync(
        columnsToUpdate.map(({ id, order_index }) => ({ id, order_index }))
      );
      toast({ title: "Успех", description: "Порядок столбцов обновлен" });
    } catch (error) {
      console.error("Ошибка при изменении порядка столбцов:", error);
      toast({
        title: "Ошибка",
        description: "Не удалось изменить порядок столбцов",
        variant: "destructive",
      });
    }
  };

  const renderColumnItem = (
    column: ColumnSetting,
    index: number,
    listType: "standard" | "custom"
  ) => {
    const list = listType === "standard" ? standardColumns : customColumns;

    return (
      // Контейнер строки
      <div
        key={column.id}
        className="flex items-start md:items-center justify-between gap-2 md:gap-4 py-3 px-4 border-b last:border-b-0 hover:bg-gray-50"
      >
        {/* Левая часть: Перетаскивание + Имя + Тип + Префикс/Постфикс */}
        <div className="flex flex-col gap-2 flex-1 min-w-0">
          <div className="flex items-center gap-2 md:gap-3">
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="hidden sm:inline-block cursor-grab text-gray-400 hover:text-gray-600 mt-1 md:mt-0">
                    <GripVertical size={18} />
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Перетащить для изменения порядка</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <div className="flex-1 min-w-0">
              <Input
                value={editableDisplayNames[column.id] || ""}
                onChange={(e) =>
                  handleDisplayNameChange(column.id, e.target.value)
                }
                onBlur={() => handleDisplayNameBlur(column)}
                className="text-sm h-8"
                title="Отображаемое имя столбца"
              />
            </div>
            <span className="hidden xs:inline-block text-xs text-gray-500 uppercase w-12 text-center flex-shrink-0 mt-1 md:mt-0">
              {column.data_type}
            </span>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 pl-0 sm:pl-8 md:pl-11">
            <div>
              <Label
                htmlFor={`prefix-${column.id}`}
                className="text-xs text-gray-500 mb-1 block"
              >
                Префикс
              </Label>
              <Input
                id={`prefix-${column.id}`}
                value={editablePrefixes[column.id] || ""}
                onChange={(e) => handlePrefixChange(column.id, e.target.value)}
                onBlur={() => handlePrefixBlur(column)}
                className="text-sm h-8"
                placeholder=""
                title="Префикс значения (напр. '$', 'ID: ')"
              />
            </div>
            <div>
              <Label
                htmlFor={`postfix-${column.id}`}
                className="text-xs text-gray-500 mb-1 block"
              >
                Постфикс
              </Label>
              <Input
                id={`postfix-${column.id}`}
                value={editablePostfixes[column.id] || ""}
                onChange={(e) => handlePostfixChange(column.id, e.target.value)}
                onBlur={() => handlePostfixBlur(column)}
                className="text-sm h-8"
                placeholder=""
                title="Постфикс значения (напр. 'кг', 'шт.', '%')"
              />
            </div>
          </div>
        </div>

        {/* Правая часть: Управление (кнопки) */}
        <div className="flex items-center gap-1 md:gap-2 flex-shrink-0 mt-1 md:mt-0">
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  {" "}
                  {/* Обертка для Tooltip + Switch */}
                  <Switch
                    id={`visible-${column.id}`}
                    checked={column.is_visible}
                    onCheckedChange={(checked) =>
                      handleToggleVisibility(column, checked)
                    }
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {column.is_visible ? "Скрыть столбец" : "Показать столбец"}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Кнопки перемещения */}
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  icon={<ArrowUp size={16} />}
                  variant="ghost"
                  size="sm"
                  onClick={() => handleMoveColumn(index, "up", listType)}
                  disabled={index === 0}
                  className={cn(
                    "p-1",
                    index === 0 && "opacity-30 cursor-not-allowed"
                  )}
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>Переместить вверх</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  icon={<ArrowDown size={16} />}
                  variant="ghost"
                  size="sm"
                  onClick={() => handleMoveColumn(index, "down", listType)}
                  disabled={index === list.length - 1}
                  className={cn(
                    "p-1",
                    index === list.length - 1 && "opacity-30 cursor-not-allowed"
                  )}
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>Переместить вниз</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Кнопка удаления (только для пользовательских) */}
          {isCustomField(column.name) && (
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <IconButton
                    icon={
                      remove.isPending && remove.variables === column.id ? (
                        <div className="animate-spin h-4 w-4 border-2 border-red-600 rounded-full border-t-transparent" />
                      ) : (
                        <Trash2 size={16} />
                      )
                    }
                    variant="ghost"
                    size="sm"
                    className="p-1 text-red-600 hover:bg-red-100"
                    onClick={() => handleDeleteColumn(column)}
                    disabled={remove.isPending}
                  />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Удалить столбец</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>
    );
  };

  // Рендер основного компонента
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Настройки столбцов"
      size="xl"
      // Указываем bodyClassName для добавления паддингов к прокручиваемой области
      bodyClassName="p-6"
      // Передаем кнопку "Закрыть" в футер
      footer={
        <div className="flex justify-end">
          <Button variant="secondary" onClick={onClose}>
            Закрыть
          </Button>
        </div>
      }
    >
      {/* Контент теперь напрямую внутри Modal (в children), Modal сам добавит скролл */}

      {/* Форма добавления нового столбца */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Добавить новый столбец</h3>
        {/* Адаптивный контейнер для формы */}
        <div className="flex flex-col md:flex-row md:items-end gap-3 md:gap-4">
          <div className="flex-grow">
            <Label
              htmlFor="newColumnDisplayName"
              className="mb-1 block text-sm font-medium"
            >
              Отображаемое имя
            </Label>
            <Input
              id="newColumnDisplayName"
              value={newColumnDisplayName}
              onChange={(e) => setNewColumnDisplayName(e.target.value)}
              placeholder="Например, Артикул"
              className="h-9"
            />
          </div>
          <div className="w-full md:w-40 flex-shrink-0">
            <Label
              htmlFor="newColumnType"
              className="mb-1 block text-sm font-medium"
            >
              Тип поля
            </Label>
            <Select
              value={newColumnType}
              onValueChange={(value: "text" | "number") =>
                setNewColumnType(value)
              }
            >
              <SelectTrigger id="newColumnType" className="h-9">
                <SelectValue placeholder="Выберите тип" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">Текст</SelectItem>
                <SelectItem value="number">Число</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button
            onClick={handleCreateColumn}
            disabled={create.isPending || !newColumnDisplayName.trim()}
            className="h-9 w-full md:w-auto flex-shrink-0" // Адаптивная ширина кнопки
          >
            <Plus className="w-4 h-4 mr-2" />
            {create.isPending ? "Добавление..." : "Добавить"}
          </Button>
        </div>
      </div>

      <Divider label="Настройка отображения" className="my-6" />

      {/* Списки столбцов */}
      {isLoading ? (
        <div className="flex items-center justify-center p-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
        </div>
      ) : (
        <div className="space-y-6">
          <div>
            <div className="mb-2">
              <h4 className="text-base font-medium text-gray-800">
                Пользовательские поля
              </h4>
              <p className="text-xs text-gray-500">
                Можно изменять имя, видимость, порядок и удалять.
              </p>
            </div>
            <div className="border rounded-md">
              {customColumns.length > 0 ? (
                customColumns.map((col, index) =>
                  renderColumnItem(col, index, "custom")
                )
              ) : (
                <p className="p-4 text-sm text-gray-500">
                  Нет пользовательских столбцов.
                </p>
              )}
            </div>
          </div>

          <div>
            <div className="mb-2">
              <h4 className="text-base font-medium text-gray-800">
                Стандартные поля
              </h4>
              <p className="text-xs text-gray-500">
                Можно изменять видимость и порядок.
              </p>
            </div>
            <div className="border rounded-md">
              {standardColumns.length > 0 ? (
                standardColumns.map((col, index) =>
                  renderColumnItem(col, index, "standard")
                )
              ) : (
                <p className="p-4 text-sm text-gray-500">
                  Стандартные поля не найдены.
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Кнопка закрытия теперь рендерится через пропс `footer` компонента Modal */}
    </Modal>
  );
}
