import { useState, useEffect, useCallback } from "react";
import {
  useProductGroups,
  useProductGroupMutations,
  ProductGroup,
} from "../../../hooks/useProductGroups";
import { toast } from "@/hooks/use-toast";
import {
  Plus,
  Trash2,
  ArrowUp,
  ArrowDown,
  Pencil,
  GripVertical,
} from "lucide-react";
import { Button } from "../../ui/Button";
import { IconButton } from "../../ui/IconButton";
import { Input } from "../../ui/Input";
import { Modal } from "../../ui/Modal";
import { Divider } from "../../ui/Divider";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface ProductGroupsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ProductGroupsModal({
  isOpen,
  onClose,
}: ProductGroupsModalProps) {
  const { data: groupsData, isLoading } = useProductGroups();
  const { create, update, remove, reorder } = useProductGroupMutations();

  // Локальное состояние для групп (позволяет вносить изменения без перезапроса данных)
  const [groups, setGroups] = useState<ProductGroup[]>([]);

  // Состояние для новой группы
  const [newGroupName, setNewGroupName] = useState("");

  // Заменяем старые состояния редактирования
  // const [editingGroupId, setEditingGroupId] = useState<string | null>(null);
  // const [editingGroupName, setEditingGroupName] = useState("");
  // На новые для inline-редактирования
  const [isEditingId, setIsEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState(""); // Имя группы во время редактирования

  // Обновляем локальное состояние при получении данных
  useEffect(() => {
    if (groupsData?.data) {
      setGroups(groupsData.data);
      // Если редактируемая группа исчезла из данных (например, удалена), сбрасываем редактирование
      if (isEditingId && !groupsData.data.some((g) => g.id === isEditingId)) {
        setIsEditingId(null);
        setEditingName("");
      }
    }
  }, [groupsData?.data, isEditingId]);

  // Обработчик добавления новой группы
  const handleAddGroup = useCallback(() => {
    if (!newGroupName.trim()) {
      toast({
        title: "Ошибка",
        description: "Введите название группы",
        variant: "destructive",
      });
      return;
    }
    const maxOrder =
      groups.length > 0
        ? Math.max(...groups.map((g) => g.order_index ?? 0))
        : -1; // Добавим ?? 0 для надежности

    create.mutate(
      {
        name: newGroupName.trim(),
        order_index: maxOrder + 1,
      },
      {
        onSuccess: () => {
          setNewGroupName("");
          // toast.success("Группа создана"); // Уведомление уже есть в мутации
        },
        onError: (error) => {
          console.error("Ошибка при создании группы:", error);
          // toast.error("Не удалось создать группу"); // Уведомление уже есть в мутации
        },
      }
    );
  }, [newGroupName, groups, create]);

  // Обработчик начала редактирования (обновлен)
  const handleStartEdit = useCallback((group: ProductGroup) => {
    setIsEditingId(group.id);
    setEditingName(group.name);
  }, []);

  // Обработчик изменения в поле ввода
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setEditingName(e.target.value);
    },
    []
  );

  // Обработчик отмены редактирования
  const handleCancelEdit = useCallback(() => {
    setIsEditingId(null);
    setEditingName("");
  }, []);

  // Обработчик сохранения изменений (вызывается по Enter или Blur)
  const handleSaveEdit = useCallback(
    async (groupId: string) => {
      const originalGroup = groups.find((g) => g.id === groupId);
      if (!originalGroup) return; // Группа не найдена

      const newName = editingName.trim();

      // Если имя не изменилось или стало пустым, просто выходим из режима редактирования
      if (!newName || newName === originalGroup.name) {
        handleCancelEdit();
        return;
      }

      try {
        await update.mutateAsync({
          id: groupId,
          updates: { name: newName },
        });
        toast({ title: "Успех", description: "Имя группы обновлено" });
      } catch (error) {
        console.error("Ошибка при обновлении имени группы:", error);
        toast({
          title: "Ошибка",
          description: "Не удалось обновить имя группы",
          variant: "destructive",
        });
        // При ошибке можно вернуть старое имя в поле ввода, если нужно
        // setEditingName(originalGroup.name);
      } finally {
        // В любом случае выходим из режима редактирования
        handleCancelEdit();
      }
    },
    [editingName, groups, update, handleCancelEdit]
  );

  // Обработчик нажатия клавиш в поле ввода
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>, groupId: string) => {
      if (event.key === "Enter") {
        handleSaveEdit(groupId);
      } else if (event.key === "Escape") {
        handleCancelEdit();
      }
    },
    [handleSaveEdit, handleCancelEdit]
  );

  // Обработчик потери фокуса полем ввода
  const handleBlur = useCallback(
    (groupId: string) => {
      // Небольшая задержка, чтобы Enter мог сработать раньше blur
      setTimeout(() => {
        // Проверяем, не закрылся ли режим редактирования уже (например, через Enter)
        if (isEditingId === groupId) {
          handleSaveEdit(groupId);
        }
      }, 150);
    },
    [handleSaveEdit, isEditingId]
  ); // Добавили isEditingId в зависимости

  // Обработчик удаления группы (обновлен)
  const handleDeleteGroup = useCallback(
    (group: ProductGroup) => {
      if (
        window.confirm(
          `Удалить группу "${group.name}"? Продукты в этой группе станут негруппированными.`
        )
      ) {
        remove.mutate(group.id, {
          onSuccess: () => {
            // toast.success("Группа удалена"); // Уведомление уже в мутации
          },
          onError: (error) => {
            console.error("Ошибка при удалении группы:", error);
            // toast.error("Не удалось удалить группу"); // Уведомление уже в мутации
          },
        });
      }
    },
    [remove]
  );

  // Обработчики перемещения групп вверх/вниз (обновлен)
  const handleMoveGroup = useCallback(
    (groupId: string, direction: "up" | "down") => {
      const currentIndex = groups.findIndex((g) => g.id === groupId);
      if (currentIndex === -1) return;

      const targetIndex =
        direction === "up"
          ? Math.max(0, currentIndex - 1)
          : Math.min(groups.length - 1, currentIndex + 1);

      if (targetIndex === currentIndex) return;

      const newGroups = [...groups];
      [newGroups[currentIndex], newGroups[targetIndex]] = [
        newGroups[targetIndex],
        newGroups[currentIndex],
      ];

      const updatedOrder = newGroups.map((group, index) => ({
        id: group.id,
        order_index: index,
      }));

      // Оптимистичное обновление UI
      setGroups(newGroups.map((g, i) => ({ ...g, order_index: i })));

      // Отправляем изменения на сервер
      reorder.mutate(
        updatedOrder.map((u) => ({ id: u.id, orderIndex: u.order_index })),
        {
          onError: (error) => {
            console.error("Ошибка при изменении порядка групп:", error);
            // toast.error("Не удалось изменить порядок групп"); // Уведомление в мутации
            // Возвращаем исходное состояние при ошибке (опционально, зависит от UX)
            if (groupsData?.data) {
              setGroups(groupsData.data);
            }
          },
        }
      );
    },
    [groups, reorder, groupsData?.data, setGroups] // Добавили setGroups
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Управление группами продуктов"
      size="lg"
      bodyClassName="p-6"
      footer={
        <div className="flex justify-end gap-2">
          <Button variant="secondary" onClick={onClose}>
            Закрыть
          </Button>
        </div>
      }
    >
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Добавить новую группу</h3>
        <div className="flex items-center gap-2">
          <Input
            id="newGroupName"
            placeholder="Название группы"
            value={newGroupName}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setNewGroupName(e.target.value)
            }
            onKeyDown={(e: React.KeyboardEvent) =>
              e.key === "Enter" && handleAddGroup()
            }
            className="h-9 flex-1"
          />
          <Button
            variant="default"
            size="default"
            onClick={handleAddGroup}
            title="Добавить группу"
            disabled={create.isPending || !newGroupName.trim()}
            className="h-9"
          >
            <Plus size={18} className="mr-1" />
            {create.isPending ? "Добавление..." : "Добавить"}
          </Button>
        </div>
      </div>

      <Divider className="my-6" />

      <div className="mb-4 space-y-2">
        <div className="mb-2">
          <h3 className="text-lg font-semibold">Существующие группы</h3>
          <p className="text-xs text-gray-500">
            Двойной клик для редактирования имени. Используйте кнопки для
            управления.
          </p>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-20">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900" />
          </div>
        ) : groups.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            Нет созданных групп
          </div>
        ) : (
          <div className="border rounded-md">
            {groups.map((group, index) => (
              <div
                key={group.id}
                className="flex items-center justify-between gap-2 md:gap-4 py-3 px-4 border-b last:border-b-0 hover:bg-gray-50"
              >
                <div className="flex items-center gap-2 md:gap-3 flex-1 min-w-0">
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="hidden sm:inline-block cursor-grab text-gray-400 hover:text-gray-600">
                          <GripVertical size={18} />
                        </span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Перетащить для изменения порядка (не реализовано)</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  {isEditingId === group.id ? (
                    <Input
                      value={editingName}
                      onChange={handleInputChange}
                      onKeyDown={(e) => handleKeyDown(e, group.id)}
                      onBlur={() => handleBlur(group.id)}
                      className="text-sm h-8 flex-1"
                      autoFocus
                    />
                  ) : (
                    <span
                      className="text-sm flex-1 cursor-pointer hover:text-blue-600 truncate"
                      onDoubleClick={() => handleStartEdit(group)}
                      title={`${group.name} (Двойной клик для редактирования)`}
                    >
                      {group.name}
                    </span>
                  )}
                </div>

                <div className="flex items-center gap-1 flex-shrink-0">
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <IconButton
                          icon={<ArrowUp size={16} />}
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMoveGroup(group.id, "up")}
                          disabled={index === 0 || reorder.isPending}
                          className={cn(
                            "p-1",
                            index === 0 && "opacity-30 cursor-not-allowed"
                          )}
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Переместить вверх</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <IconButton
                          icon={<ArrowDown size={16} />}
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMoveGroup(group.id, "down")}
                          disabled={
                            index === groups.length - 1 || reorder.isPending
                          }
                          className={cn(
                            "p-1",
                            index === groups.length - 1 &&
                              "opacity-30 cursor-not-allowed"
                          )}
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Переместить вниз</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <IconButton
                          icon={<Pencil size={16} />}
                          variant="ghost"
                          size="sm"
                          onClick={() => handleStartEdit(group)}
                          disabled={isEditingId === group.id}
                          className="p-1"
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Редактировать имя</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <IconButton
                          icon={
                            remove.isPending &&
                            remove.variables === group.id ? (
                              <div className="animate-spin h-4 w-4 border-2 border-red-600 rounded-full border-t-transparent" />
                            ) : (
                              <Trash2 size={16} />
                            )
                          }
                          variant="ghost"
                          size="sm"
                          className="p-1 text-red-600 hover:bg-red-100"
                          onClick={() => handleDeleteGroup(group)}
                          disabled={remove.isPending}
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Удалить группу</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </Modal>
  );
}
