import { ColDef, Grid<PERSON><PERSON> } from "ag-grid-community";

/**
 * Utility functions for automatic focus when adding new elements
 */

/**
 * Finds the first visible and editable column for auto-focus
 * @param gridApi - AgGrid API instance
 * @param columnDefs - Column definitions
 * @param userRole - Current user role
 * @returns The field name of the first editable column, or null if none found
 */
export function findFirstEditableColumn(
  gridApi: GridApi | null,
  columnDefs: ColDef[],
  userRole: string | null
): string | null {
  if (!gridApi || userRole === "reader") {
    return null;
  }

  // Get currently displayed columns in their current order
  const displayedColumns = gridApi.getAllDisplayedColumns();

  if (!displayedColumns || displayedColumns.length === 0) {
    return null;
  }

  // Find the first editable column among displayed columns
  for (const column of displayedColumns) {
    const colId = column.getColId();

    // Skip checkbox selection column and other system columns
    if (!colId || colId.startsWith("ag-Grid-")) {
      continue;
    }

    // Find the column definition
    const colDef = columnDefs.find((def) => def.field === colId);

    if (!colDef) {
      continue;
    }

    // Skip special non-editable columns
    if (
      colId === "is_hidden" ||
      colId === "product_images" ||
      colId === "created_at"
    ) {
      continue;
    }

    // Check if column is editable
    const isEditable =
      typeof colDef.editable === "function"
        ? true // Assume editable if it's a function (we can't easily evaluate without a real row)
        : colDef.editable;

    if (isEditable) {
      return colId;
    }
  }

  return null;
}

/**
 * Triggers auto-focus on the first editable cell of a newly created product
 * @param gridApi - AgGrid API instance
 * @param productId - ID of the newly created product
 * @param columnDefs - Column definitions
 * @param userRole - Current user role
 * @param delay - Delay before focusing (default: 500ms)
 * @param maxRetries - Maximum number of retries to find the row (default: 10)
 */
export function autoFocusNewProduct(
  gridApi: GridApi | null,
  productId: string,
  columnDefs: ColDef[],
  userRole: string | null,
  delay: number = 500,
  maxRetries: number = 10
): void {
  if (!gridApi || userRole === "reader") {
    return;
  }

  let retryCount = 0;

  const attemptFocus = () => {
    const rowNode = gridApi.getRowNode(productId);

    if (!rowNode && retryCount < maxRetries) {
      retryCount++;
      setTimeout(attemptFocus, 200);
      return;
    }

    if (!rowNode) {
      return;
    }

    // Ensure the row is visible
    gridApi.ensureNodeVisible(rowNode, "middle");

    // Find the first editable column
    const firstEditableField = findFirstEditableColumn(
      gridApi,
      columnDefs,
      userRole
    );

    if (!firstEditableField) {
      return;
    }

    // Start editing the first editable cell
    gridApi.startEditingCell({
      rowIndex: rowNode.rowIndex as number,
      colKey: firstEditableField,
    });
  };

  setTimeout(attemptFocus, delay);
}

/**
 * Triggers auto-focus on a newly created group header
 * @param gridApi - AgGrid API instance
 * @param groupId - ID of the newly created group
 * @param delay - Delay before focusing (default: 500ms)
 * @param maxRetries - Maximum number of retries to find the row (default: 10)
 */
export function autoFocusNewGroup(
  gridApi: GridApi | null,
  groupId: string,
  delay: number = 500,
  maxRetries: number = 10
): void {
  if (!gridApi) {
    return;
  }

  let retryCount = 0;

  const attemptFocus = () => {
    const groupNode = gridApi.getRowNode(`group-${groupId}`);

    if (!groupNode && retryCount < maxRetries) {
      retryCount++;
      setTimeout(attemptFocus, 200);
      return;
    }

    if (!groupNode) {
      return;
    }

    // Ensure the group is visible
    gridApi.ensureNodeVisible(groupNode, "middle");

    // Approach 1: Use context signaling
    const currentContext = gridApi.getGridOption("context") || {};
    const newContext = {
      ...currentContext,
      autoEditGroupId: groupId,
      autoEditTimestamp: Date.now(),
    };

    gridApi.setGridOption("context", newContext);

    // Approach 2: Try to trigger a refresh of the row to force re-render
    setTimeout(() => {
      gridApi.refreshCells({ rowNodes: [groupNode], force: true });
    }, 100);

    // Approach 3: Dispatch a custom event as fallback
    setTimeout(() => {
      const event = new CustomEvent("autoEditGroup", {
        detail: { groupId, timestamp: Date.now() },
      });
      window.dispatchEvent(event);
    }, 200);
  };

  setTimeout(attemptFocus, delay);
}
