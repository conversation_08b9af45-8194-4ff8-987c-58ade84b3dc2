import { useCallback, useState } from "react";
import { GridReadyEvent } from "ag-grid-community";
import type { Database } from "../../../types/supabase";

type Product = Database["public"]["Tables"]["products"]["Row"];

export function useProductGrid() {
  const [gridApi, setGridApi] = useState<GridReadyEvent["api"] | null>(null);
  const [selectedRows, setSelectedRows] = useState<Product[]>([]);

  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
  };

  const onSelectionChanged = useCallback(() => {
    const selectedNodes = gridApi?.getSelectedNodes();
    const selectedData =
      selectedNodes?.map((node) => node.data as Product) ?? [];
    setSelectedRows(selectedData);
  }, [gridApi]);

  return {
    gridApi,
    selectedRows,
    onGridReady,
    onSelectionChanged,
  };
}
