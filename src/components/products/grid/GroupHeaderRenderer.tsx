import React, { useState, useRef, useEffect } from "react";
import { ICellRendererParams } from "ag-grid-community";
import { Edit2, Trash2 } from "lucide-react";
import { useProductGroupMutations } from "../../../hooks/useProductGroups";
import { useAuth } from "../../../lib/auth";

// Тип данных для строки заголовка группы
export interface GroupHeaderRow {
  isGroupHeader: true;
  groupId: string;
  groupName: string;
  totalProducts?: number; // Опционально: количество продуктов в группе
}

interface GroupHeaderProps extends ICellRendererParams {
  data: GroupHeaderRow;
}

export const GroupHeaderRenderer = (props: GroupHeaderProps) => {
  const { data, api: gridApi, node, context } = props;
  const { userRole } = useAuth();
  // Use effective user role from context if available, otherwise use actual user role
  const effectiveUserRole = context?.effectiveUserRole || userRole;
  const { update: updateGroup, remove: removeGroup } =
    useProductGroupMutations();

  const [isEditing, setIsEditing] = useState(false);
  const [currentName, setCurrentName] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  // Устанавливаем название в состояние при инициализации
  useEffect(() => {
    if (data && data.groupName) {
      setCurrentName(data.groupName);
    }
  }, [data]);

  // Фокусировка при входе в режим редактирования
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // Автоматическое редактирование для новых групп
  useEffect(() => {
    if (
      context?.autoEditGroupId === data?.groupId &&
      context?.autoEditTimestamp &&
      effectiveUserRole !== "reader"
    ) {
      // Проверяем, что сигнал свежий (не старше 5 секунд)
      const timeDiff = Date.now() - context.autoEditTimestamp;

      if (timeDiff < 5000) {
        setIsEditing(true);
      }
    }
  }, [
    context?.autoEditGroupId,
    context?.autoEditTimestamp,
    data?.groupId,
    effectiveUserRole,
  ]);

  // Fallback: Listen for custom events
  useEffect(() => {
    const handleAutoEditEvent = (event: CustomEvent) => {
      const { groupId: eventGroupId, timestamp } = event.detail;

      if (eventGroupId === data?.groupId && effectiveUserRole !== "reader") {
        const timeDiff = Date.now() - timestamp;
        if (timeDiff < 5000) {
          setIsEditing(true);
        }
      }
    };

    window.addEventListener(
      "autoEditGroup",
      handleAutoEditEvent as EventListener
    );

    return () => {
      window.removeEventListener(
        "autoEditGroup",
        handleAutoEditEvent as EventListener
      );
    };
  }, [data?.groupId, effectiveUserRole]);

  if (!data || !data.isGroupHeader) {
    return null;
  }

  const startEditing = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Предотвращаем редактирование для читателей
    if (effectiveUserRole === "reader") {
      return;
    }
    // Предотвращаем редактирование, если уже редактируется другая строка/ячейка
    if (gridApi?.getEditingCells().length > 0) {
      return;
    }
    setIsEditing(true);
  };

  const saveChanges = async () => {
    setIsEditing(false);
    const newName = currentName.trim();

    if (newName && newName !== data.groupName) {
      try {
        await updateGroup.mutateAsync({
          id: data.groupId,
          updates: { name: newName },
        });

        // Опционально: обновляем данные в узле AG Grid локально для мгновенного отображения
        if (node?.data) {
          node.setData({ ...node.data, groupName: newName });
        }
      } catch (error) {
        // Возвращаем старое имя при ошибке
        setCurrentName(data.groupName);
        console.error("Ошибка при обновлении имени группы:", error);
      }
    } else {
      // Если имя не изменилось или пустое, восстанавливаем оригинальное
      setCurrentName(data.groupName);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      saveChanges();
    } else if (event.key === "Escape") {
      setCurrentName(data.groupName); // Отмена - восстанавливаем старое имя
      setIsEditing(false);
    }
  };

  const handleBlur = () => {
    // Небольшая задержка, чтобы Enter успел сработать раньше blur
    setTimeout(saveChanges, 100);
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (data.groupId === "ungrouped") {
      return; // Не позволяем удалять специальную группу "Без группы"
    }

    // Запрашиваем подтверждение
    if (
      window.confirm(
        `Удалить группу "${data.groupName}"? Продукты будут перемещены в "Без группы".`
      )
    ) {
      try {
        await removeGroup.mutateAsync(data.groupId);
        // Обновление произойдет через invalidateQueries в мутации
      } catch (error) {
        console.error("Ошибка при удалении группы:", error);
      }
    }
  };

  return (
    <div className="group-header-row">
      <div className="flex items-center justify-between px-2 py-0.5 bg-gray-100 w-full font-medium text-gray-700 border-y border-gray-300 hover:bg-gray-200 transition-colors cursor-grab active:cursor-grabbing h-full">
        <div className="flex items-center">
          {isEditing ? (
            <input
              ref={inputRef}
              type="text"
              value={currentName}
              onChange={(e) => setCurrentName(e.target.value)}
              onKeyDown={handleKeyDown}
              onBlur={handleBlur}
              className="px-2 py-0.5 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          ) : (
            <span
              className={`text-sm ${
                effectiveUserRole !== "reader"
                  ? "cursor-pointer hover:text-blue-600"
                  : ""
              }`}
              onDoubleClick={
                effectiveUserRole !== "reader" ? startEditing : undefined
              }
              title={
                effectiveUserRole !== "reader"
                  ? "Двойной клик для редактирования"
                  : undefined
              }
            >
              {data.groupName}
            </span>
          )}

          {data.totalProducts !== undefined && (
            <span className="ml-1 px-1.5 py-0 bg-gray-200 rounded-full text-xs text-gray-700">
              {data.totalProducts}
            </span>
          )}
        </div>

        {effectiveUserRole !== "reader" && (
          <div className="flex items-center gap-1 opacity-50 hover:opacity-100 transition-opacity h-full">
            <button
              onClick={startEditing}
              className="p-0.5 hover:bg-gray-200 rounded-full"
              title="Редактировать группу"
            >
              <Edit2 size={14} />
            </button>
            <button
              onClick={handleDelete}
              className="p-0.5 hover:bg-gray-200 rounded-full text-red-600"
              title="Удалить группу"
              disabled={data.groupId === "ungrouped"}
              style={{ opacity: data.groupId === "ungrouped" ? 0.3 : 1 }}
            >
              <Trash2 size={14} />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
