import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  forwardRef,
  useImperativeHandle,
} from "react";
import { createPortal } from "react-dom";
import { ICellEditorParams } from "ag-grid-community";
import {
  toggleFormatting,
  parseFormattedText,
  createFormatTag,
  type TextSegment,
  getFormattingAtPosition,
} from "../../../utils/textFormatting";
import type { FormatType } from "../../../utils/textFormatting";
import { Bold, Type } from "lucide-react";
import { Button } from "@/components/ui/Button";

interface RichTextCellEditorProps extends ICellEditorParams {
  value: string;
}

export interface RichTextCellEditorRef {
  getValue: () => string;
  isCancelBeforeStart?: () => boolean;
  isCancelAfterEnd?: () => boolean;
}

export const RichTextCellEditor = forwardRef<
  RichTextCellEditorRef,
  RichTextCellEditorProps
>(({ value: initialValue, stopEditing }, ref) => {
  const [formattedValue, setFormattedValue] = useState<string>(
    initialValue || ""
  );
  const editorRef = useRef<HTMLDivElement>(null);
  const [showToolbar, setShowToolbar] = useState(false);
  const [toolbarPosition, setToolbarPosition] = useState({ x: 0, y: 0 });
  const [currentSelection, setCurrentSelection] = useState({
    start: 0,
    end: 0,
  });
  const [isFormatting, setIsFormatting] = useState(false);

  useImperativeHandle(ref, () => ({
    getValue: () => formattedValue,
    isCancelBeforeStart: () => false,
    isCancelAfterEnd: () => false,
  }));

  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.focus();
      // Select all text
      const range = document.createRange();
      range.selectNodeContents(editorRef.current);
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  }, []);

  // Convert formatted text to HTML for contenteditable
  const formatToHtml = useCallback((text: string): string => {
    const segments = parseFormattedText(text);
    return segments
      .map((segment: TextSegment) => {
        const html = segment.text.replace(/</g, "&lt;").replace(/>/g, "&gt;");

        if (segment.bold && segment.red) {
          return `<span style="font-weight: bold; color: #dc2626;">${html}</span>`;
        } else if (segment.bold) {
          return `<span style="font-weight: bold;">${html}</span>`;
        } else if (segment.red) {
          return `<span style="color: #dc2626;">${html}</span>`;
        }
        return html;
      })
      .join("");
  }, []);

  // Update the contenteditable div with formatted HTML
  const updateEditorContent = useCallback(
    (text: string) => {
      if (editorRef.current) {
        const html = formatToHtml(text);
        editorRef.current.innerHTML = html;
      }
    },
    [formatToHtml]
  );

  useEffect(() => {
    updateEditorContent(formattedValue);
  }, [formattedValue, updateEditorContent]);

  const getSelection = useCallback((): { start: number; end: number } => {
    if (!editorRef.current) return { start: 0, end: 0 };

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return { start: 0, end: 0 };

    const range = selection.getRangeAt(0);
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(editorRef.current);
    preCaretRange.setEnd(range.startContainer, range.startOffset);
    const start = preCaretRange.toString().length;

    preCaretRange.setEnd(range.endContainer, range.endOffset);
    const end = preCaretRange.toString().length;

    return { start, end };
  }, []);

  const updateToolbarVisibility = useCallback(() => {
    if (!editorRef.current || isFormatting) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      if (!isFormatting) {
        setShowToolbar(false);
      }
      return;
    }

    const range = selection.getRangeAt(0);
    const hasSelection = !range.collapsed;

    if (
      hasSelection &&
      editorRef.current.contains(range.commonAncestorContainer)
    ) {
      const rect = range.getBoundingClientRect();

      const x = rect.left + rect.width / 2;
      const y = rect.top - 10;

      setToolbarPosition({ x, y });

      const selectionData = getSelection();
      setCurrentSelection(selectionData);
      setShowToolbar(true);
    } else if (!isFormatting) {
      setShowToolbar(false);
    }
  }, [getSelection, isFormatting]);

  useEffect(() => {
    const handleSelectionChange = () => {
      updateToolbarVisibility();
    };

    document.addEventListener("selectionchange", handleSelectionChange);
    return () => {
      document.removeEventListener("selectionchange", handleSelectionChange);
    };
  }, [updateToolbarVisibility]);

  const setSelection = useCallback((start: number, end: number) => {
    if (!editorRef.current) return;

    const setSelectionRecursive = (
      node: Node,
      startPos: number,
      endPos: number
    ): { done: boolean; offset: number } => {
      let offset = 0;

      if (node.nodeType === Node.TEXT_NODE) {
        const textLength = node.textContent?.length || 0;
        if (offset + textLength >= startPos) {
          const range = document.createRange();
          const selection = window.getSelection();

          range.setStart(node, Math.min(startPos - offset, textLength));
          if (startPos === endPos) {
            range.setEnd(node, Math.min(startPos - offset, textLength));
          } else if (offset + textLength >= endPos) {
            range.setEnd(node, Math.min(endPos - offset, textLength));
          } else {
            // Continue to find end position
            let currentNode = node;
            let currentOffset = offset + textLength;

            while (currentNode) {
              const nextNode =
                currentNode.nextSibling || currentNode.parentNode?.nextSibling;
              if (!nextNode) break;

              if (nextNode.nodeType === Node.TEXT_NODE) {
                const nextLength = nextNode.textContent?.length || 0;
                if (currentOffset + nextLength >= endPos) {
                  range.setEnd(nextNode, endPos - currentOffset);
                  break;
                }
                currentOffset += nextLength;
              }
              currentNode = nextNode as Node;
            }
          }

          if (selection) {
            selection.removeAllRanges();
            selection.addRange(range);
          }
          return { done: true, offset: offset + textLength };
        }
        return { done: false, offset: offset + textLength };
      }

      for (let i = 0; i < node.childNodes.length; i++) {
        const child = node.childNodes[i];
        const result = setSelectionRecursive(
          child,
          startPos - offset,
          endPos - offset
        );
        if (result.done) return result;
        offset += result.offset;
      }

      return { done: false, offset };
    };

    setTimeout(() => {
      if (editorRef.current) {
        setSelectionRecursive(editorRef.current, start, end);
      }
    }, 0);
  }, []);

  const handleToggleFormatting = useCallback(
    (formatType: FormatType) => {
      const selection = getSelection();

      if (selection.start === selection.end) {
        return;
      }

      // Используем новую функцию toggleFormatting
      const newFormattedValue = toggleFormatting(
        formattedValue,
        selection.start,
        selection.end,
        formatType
      );

      setFormattedValue(newFormattedValue);

      setTimeout(() => {
        setSelection(selection.start, selection.end);
      }, 0);
    },
    [formattedValue, getSelection, setSelection]
  );

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLDivElement>) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
          case "b":
            event.preventDefault();
            handleToggleFormatting("bold");
            break;
          case "r":
            event.preventDefault();
            handleToggleFormatting("red");
            break;
        }
      }

      if (event.key === "Enter") {
        event.preventDefault();
        stopEditing();
      }

      if (event.key === "Escape") {
        event.preventDefault();
        setFormattedValue(initialValue || "");
        stopEditing();
      }
    },
    [handleToggleFormatting, stopEditing, initialValue]
  );

  // Convert HTML back to formatted text
  const htmlToFormat = useCallback(
    (
      node: Node,
      currentFormat: { bold?: boolean; red?: boolean } = {}
    ): string => {
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent || "";
        if (currentFormat.bold || currentFormat.red) {
          return createFormatTag(text, currentFormat);
        }
        return text;
      }

      if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as HTMLElement;
        const newFormat = { ...currentFormat };

        // Check for formatting from styles
        const style = element.style;
        if (style.fontWeight === "bold") {
          newFormat.bold = true;
        }
        if (style.color === "rgb(220, 38, 38)" || style.color === "#dc2626") {
          newFormat.red = true;
        }

        // Process children
        let result = "";
        for (const child of Array.from(node.childNodes)) {
          result += htmlToFormat(child, newFormat);
        }
        return result;
      }

      return "";
    },
    []
  );

  const handleInput = useCallback(() => {
    if (!editorRef.current) return;

    // Save cursor position
    const selection = getSelection();

    // Convert HTML back to formatted text
    const newFormattedValue = htmlToFormat(editorRef.current);
    setFormattedValue(newFormattedValue);

    // Restore cursor position after React re-renders
    setTimeout(() => {
      setSelection(selection.start, selection.end);
    }, 0);
  }, [getSelection, setSelection, htmlToFormat]);

  const handleMouseUp = useCallback(() => {
    setTimeout(() => {
      updateToolbarVisibility();
    }, 10);
  }, [updateToolbarVisibility]);

  const handleKeyUp = useCallback(() => {
    setTimeout(() => {
      updateToolbarVisibility();
    }, 10);
  }, [updateToolbarVisibility]);

  const handleBlur = useCallback(() => {
    stopEditing();
  }, [stopEditing]);

  const handlePaste = useCallback((e: React.ClipboardEvent<HTMLDivElement>) => {
    e.preventDefault();
    const text = e.clipboardData.getData("text/plain");
    document.execCommand("insertText", false, text);
  }, []);

  const handleToolbarFormatting = useCallback(
    (formatType: FormatType, event: React.MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();

      if (currentSelection.start === currentSelection.end) {
        return;
      }

      setIsFormatting(true);

      const newFormattedValue = toggleFormatting(
        formattedValue,
        currentSelection.start,
        currentSelection.end,
        formatType
      );

      setFormattedValue(newFormattedValue);

      setTimeout(() => {
        if (editorRef.current) {
          editorRef.current.focus();
          setSelection(currentSelection.start, currentSelection.end);

          setTimeout(() => {
            setIsFormatting(false);
            setShowToolbar(true);
            setCurrentSelection(currentSelection);
          }, 50);
        }
      }, 0);
    },
    [formattedValue, currentSelection, setSelection]
  );

  const getActiveFormats = useCallback(() => {
    if (currentSelection.start === currentSelection.end) {
      return { bold: false, red: false };
    }

    const formats = getFormattingAtPosition(
      formattedValue,
      currentSelection.start,
      currentSelection.end
    );

    return {
      bold: !!formats.bold,
      red: !!formats.red,
    };
  }, [formattedValue, currentSelection]);

  const activeFormats = getActiveFormats();

  const FormattingToolbar = () => {
    if (!showToolbar) return null;

    const toolbar = (
      <div
        style={{
          position: "fixed",
          left: toolbarPosition.x - 50,
          top: toolbarPosition.y - 50,
          zIndex: 99999,
          pointerEvents: "auto",
        }}
        className="bg-white border border-gray-300 rounded-md p-1 shadow-lg"
        onMouseDown={(e) => e.preventDefault()}
      >
        <div className="flex gap-1">
          <Button
            variant={activeFormats.bold ? "default" : "ghost"}
            size="sm"
            className={`h-8 w-8 p-0 ${
              activeFormats.bold
                ? "bg-slate-200 hover:bg-slate-300 text-slate-800 border border-slate-300"
                : "text-gray-700 hover:text-gray-900 hover:bg-gray-100"
            }`}
            onClick={(e) => handleToolbarFormatting("bold", e)}
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            variant={activeFormats.red ? "default" : "ghost"}
            size="sm"
            className={`h-8 w-8 p-0 ${
              activeFormats.red
                ? "bg-red-50 hover:bg-red-100 text-red-700 border border-red-200"
                : "text-gray-700 hover:text-gray-900 hover:bg-gray-100"
            }`}
            onClick={(e) => handleToolbarFormatting("red", e)}
          >
            <Type className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      </div>
    );

    return createPortal(toolbar, document.body);
  };

  return (
    <>
      <div
        ref={editorRef}
        contentEditable
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        onKeyUp={handleKeyUp}
        onBlur={handleBlur}
        onPaste={handlePaste}
        onMouseUp={handleMouseUp}
        className="w-full h-full px-2 py-1 border-0 outline-none bg-white text-sm"
        style={{
          fontSize: "13px",
          lineHeight: "1.2",
          minHeight: "100%",
          whiteSpace: "pre-wrap",
        }}
      />
      <FormattingToolbar />
    </>
  );
});

RichTextCellEditor.displayName = "RichTextCellEditor";

export default RichTextCellEditor;
