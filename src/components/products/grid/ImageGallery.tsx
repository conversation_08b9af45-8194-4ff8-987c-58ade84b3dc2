import { FolderItem } from "../../../hooks/useImageFolder";
import { X } from "lucide-react";
import { Button } from "@/components/ui/Button";

interface ImageGalleryProps {
  folderContents: FolderItem[];
  onImageClick: (item: FolderItem, index: number) => void;
  onDeleteImage: (item: FolderItem, e: React.MouseEvent) => void;
  isLoading: boolean;
  isDeleting: string | null;
}

export const ImageGallery = ({
  folderContents,
  onImageClick,
  onDeleteImage,
  isLoading,
  isDeleting,
}: ImageGalleryProps) => {
  if (isLoading) {
    return (
      <div style={{ textAlign: "center", padding: "20px" }}>
        Загрузка содержимого папки...
      </div>
    );
  }

  if (folderContents.length === 0) {
    return (
      <div style={{ textAlign: "center", padding: "10px" }}>
        Изображения не найдены
      </div>
    );
  }

  return (
    <div>
      <h3 style={{ marginBottom: "10px" }}>Изображения товара:</h3>
      <div style={{ display: "flex", flexWrap: "wrap", gap: "10px" }}>
        {folderContents.map((item, index) => (
          <div
            key={item.name}
            onClick={() => onImageClick(item, index)}
            style={{
              width: "150px",
              height: "150px",
              border: "1px solid #ccc",
              borderRadius: "4px",
              overflow: "hidden",
              cursor: "pointer",
              position: "relative",
            }}
          >
            <div
              style={{
                width: "100%",
                height: "120px",
                backgroundColor: "#f0f0f0",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                position: "relative",
              }}
            >
              {item.preview ? (
                <img
                  src={item.preview}
                  alt={item.name}
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                  }}
                  onError={(e) => {
                    // Скрываем изображение при ошибке загрузки
                    e.currentTarget.style.display = "none";
                    // Показываем fallback текст
                    const fallback = e.currentTarget
                      .nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = "flex";
                  }}
                  onLoad={(e) => {
                    // Скрываем fallback текст при успешной загрузке
                    const fallback = e.currentTarget
                      .nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = "none";
                  }}
                />
              ) : null}
              <div
                style={{
                  position: "absolute",
                  top: "50%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                  color: "#666",
                  fontSize: "12px",
                  textAlign: "center",
                  display: item.preview ? "none" : "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: "4px",
                }}
              >
                <span>Превью недоступно</span>
                <span style={{ fontSize: "10px", opacity: 0.7 }}>
                  Попробуйте обновить
                </span>
              </div>
            </div>
            <div
              style={{
                padding: "5px",
                fontSize: "12px",
                textOverflow: "ellipsis",
                overflow: "hidden",
                whiteSpace: "nowrap",
              }}
            >
              {item.name}
            </div>
            <Button
              variant="destructive"
              size="sm"
              onClick={(e) => onDeleteImage(item, e)}
              disabled={isDeleting === item.name}
              className="absolute top-1 right-1 w-6 h-6 p-0"
            >
              {isDeleting === item.name ? "..." : <X size={14} />}
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};
