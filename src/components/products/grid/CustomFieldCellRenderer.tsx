import { ICellRendererParams } from "ag-grid-community";

// Расширяем ICellRendererParams для включения наших кастомных параметров
interface CustomFieldCellRendererParams extends ICellRendererParams {
  colDef: ICellRendererParams["colDef"] & {
    cellRendererParams?: {
      prefix?: string | null;
      postfix?: string | null;
      dataType?: "text" | "number";
    };
  };
}

export const CustomFieldCellRenderer = (
  params: CustomFieldCellRendererParams
) => {
  const { data, colDef } = params;
  if (!data || !colDef || !colDef.field) return null;

  // Проверяем, что поле имеет формат "custom_fields.field_name"
  const fieldPath = colDef.field.split(".");
  if (fieldPath.length !== 2 || fieldPath[0] !== "custom_fields") {
    console.warn(
      `[CustomFieldCellRenderer] Поле ${colDef.field} не соответствует ожидаемому формату "custom_fields.X", но рендерер был вызван.`
    );
    const directValue = data[colDef.field];
    return directValue !== undefined && directValue !== null
      ? String(directValue)
      : "";
  }

  const fieldName = fieldPath[1];

  // Проверяем наличие объекта custom_fields и данных в нем
  if (!data.custom_fields) {
    return "";
  }

  let customFieldsObject = data.custom_fields;
  // Проверяем тип custom_fields - должен быть объект
  if (typeof customFieldsObject === "string") {
    try {
      customFieldsObject = JSON.parse(customFieldsObject);
    } catch (e) {
      console.error(`[ERROR] Не удалось преобразовать custom_fields:`, e);
      return "";
    }
  }

  if (typeof customFieldsObject !== "object" || customFieldsObject === null) {
    return "";
  }

  const customFieldValue = (customFieldsObject as Record<string, unknown>)[
    fieldName
  ];

  if (customFieldValue === undefined || customFieldValue === null) {
    return "";
  }

  // --- ИЗМЕНЕНИЕ: Получаем prefix и postfix из cellRendererParams ---
  const prefix = params.colDef.cellRendererParams?.prefix || "";
  const postfix = params.colDef.cellRendererParams?.postfix || "";
  const dataType = params.colDef.cellRendererParams?.dataType;
  // --------------------------------------------------------------

  let valueStr = String(customFieldValue);

  // --- ИЗМЕНЕНИЕ: Применяем форматирование для числовых полей, включая денежный формат ---
  if (dataType === "number") {
    // Если постфикс "₽" (или другой символ валюты), считаем это денежным значением и форматируем
    if (postfix === "₽") {
      const numValue = Number(customFieldValue);
      if (!isNaN(numValue)) {
        valueStr = numValue.toFixed(2);
      }
    }
  }
  // ---------------------------------------------------------------------------------

  return `${prefix} ${valueStr} ${postfix}`;
};
