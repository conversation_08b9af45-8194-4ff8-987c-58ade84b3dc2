import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, act } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import {
  RichTextCellEditor,
  type RichTextCellEditorRef,
} from "../RichTextCellEditor";
import { createRef } from "react";

describe("RichTextCellEditor", () => {
  const mockStopEditing = vi.fn();

  beforeEach(() => {
    mockStopEditing.mockClear();
  });

  const defaultProps = {
    value: "Hello world",
    stopEditing: mockStopEditing,
    // Mock other ICellEditorParams properties as needed
    node: {} as any,
    data: {} as any,
    colDef: {} as any,
    column: {} as any,
    api: {} as any,
    columnApi: {} as any,
    context: {} as any,
    onKeyDown: {} as any,
    charPress: null,
    rowIndex: 0,
    cellStartedEdit: false,
    parseValue: vi.fn(),
    formatValue: vi.fn(),
  };

  it("should render input with initial value", () => {
    render(<RichTextCellEditor {...defaultProps} />);

    const input = screen.getByDisplayValue("Hello world");
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute("type", "text");
  });

  it("should render input with empty value for null initial value", () => {
    render(<RichTextCellEditor {...defaultProps} value={null as any} />);

    const input = screen.getByRole("textbox");
    expect(input).toBeInTheDocument();
    expect(input).toHaveValue("");
  });

  it("should render input with empty value for undefined initial value", () => {
    render(<RichTextCellEditor {...defaultProps} value={undefined as any} />);

    const input = screen.getByRole("textbox");
    expect(input).toBeInTheDocument();
    expect(input).toHaveValue("");
  });

  it("should extract plain text from formatted initial value", () => {
    render(
      <RichTextCellEditor
        {...defaultProps}
        value='Hello <format bold="true">world</format>'
      />
    );

    const input = screen.getByDisplayValue("Hello world");
    expect(input).toBeInTheDocument();
  });

  it("should focus and select text on mount", async () => {
    render(<RichTextCellEditor {...defaultProps} />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveFocus();

    // Check if text is selected (this is harder to test directly)
    expect(input.selectionStart).toBe(0);
    expect(input.selectionEnd).toBe(11); // Length of "Hello world"
  });

  it("should handle text input changes", async () => {
    const user = userEvent.setup();
    render(<RichTextCellEditor {...defaultProps} />);

    const input = screen.getByRole("textbox");

    await user.clear(input);
    await user.type(input, "New text");

    expect(input).toHaveValue("New text");
  });

  it("should apply bold formatting with Ctrl+B", async () => {
    const user = userEvent.setup();
    const ref = createRef<RichTextCellEditorRef>();
    render(<RichTextCellEditor {...defaultProps} ref={ref} />);

    const input = screen.getByRole("textbox");

    // Select "world" (positions 6-11)
    input.setSelectionRange(6, 11);

    await user.keyboard("{Control>}b{/Control}");

    // Check that the formatted value includes bold formatting
    expect(ref.current?.getValue()).toBe(
      'Hello <format bold="true">world</format>'
    );
  });

  it("should apply red formatting with Ctrl+R", async () => {
    const user = userEvent.setup();
    const ref = createRef<RichTextCellEditorRef>();
    render(<RichTextCellEditor {...defaultProps} ref={ref} />);

    const input = screen.getByRole("textbox");

    // Select "world" (positions 6-11)
    input.setSelectionRange(6, 11);

    await user.keyboard("{Control>}r{/Control}");

    expect(ref.current?.getValue()).toBe(
      'Hello <format red="true">world</format>'
    );
  });

  it("should apply bold formatting with Cmd+B on Mac", async () => {
    const user = userEvent.setup();
    const ref = createRef<RichTextCellEditorRef>();
    render(<RichTextCellEditor {...defaultProps} ref={ref} />);

    const input = screen.getByRole("textbox");

    input.setSelectionRange(6, 11);

    await user.keyboard("{Meta>}b{/Meta}");

    expect(ref.current?.getValue()).toBe(
      'Hello <format bold="true">world</format>'
    );
  });

  it("should apply red formatting with Cmd+R on Mac", async () => {
    const user = userEvent.setup();
    const ref = createRef<RichTextCellEditorRef>();
    render(<RichTextCellEditor {...defaultProps} ref={ref} />);

    const input = screen.getByRole("textbox");

    input.setSelectionRange(6, 11);

    await user.keyboard("{Meta>}r{/Meta}");

    expect(ref.current?.getValue()).toBe(
      'Hello <format red="true">world</format>'
    );
  });

  it("should toggle formatting off when applied to already formatted text", async () => {
    const user = userEvent.setup();
    const ref = createRef<RichTextCellEditorRef>();
    render(
      <RichTextCellEditor
        {...defaultProps}
        value='Hello <format bold="true">world</format>'
        ref={ref}
      />
    );

    const input = screen.getByRole("textbox");

    // Select "world" which is already bold
    input.setSelectionRange(6, 11);

    await user.keyboard("{Control>}b{/Control}");

    // Should remove bold formatting
    expect(ref.current?.getValue()).toBe("Hello world");
  });

  it("should not apply formatting when no text is selected", async () => {
    const user = userEvent.setup();
    const ref = createRef<RichTextCellEditorRef>();
    render(<RichTextCellEditor {...defaultProps} ref={ref} />);

    const input = screen.getByRole("textbox");

    // Set cursor position without selection
    input.setSelectionRange(6, 6);

    await user.keyboard("{Control>}b{/Control}");

    // Should not change the text
    expect(ref.current?.getValue()).toBe("Hello world");
  });

  it("should stop editing on Enter key", async () => {
    const user = userEvent.setup();
    render(<RichTextCellEditor {...defaultProps} />);

    const input = screen.getByRole("textbox");

    await user.type(input, "{Enter}");

    expect(mockStopEditing).toHaveBeenCalledTimes(1);
  });

  it("should cancel editing on Escape key", async () => {
    const user = userEvent.setup();
    const ref = createRef<RichTextCellEditorRef>();
    render(
      <RichTextCellEditor {...defaultProps} value="Original text" ref={ref} />
    );

    const input = screen.getByRole("textbox");

    // Change the text
    await user.clear(input);
    await user.type(input, "Modified text");

    // Press Escape
    await user.keyboard("{Escape}");

    // Should revert to original value
    expect(ref.current?.getValue()).toBe("Original text");
    expect(mockStopEditing).toHaveBeenCalledTimes(1);
  });

  it("should stop editing on blur", async () => {
    const user = userEvent.setup();
    render(<RichTextCellEditor {...defaultProps} />);

    const input = screen.getByRole("textbox");

    await user.click(document.body); // Click outside to blur

    expect(mockStopEditing).toHaveBeenCalledTimes(1);
  });

  it("should combine multiple formatting types", async () => {
    const user = userEvent.setup();
    const ref = createRef<RichTextCellEditorRef>();
    render(<RichTextCellEditor {...defaultProps} ref={ref} />);

    const input = screen.getByRole("textbox");

    // Select "world"
    input.setSelectionRange(6, 11);

    // Apply bold
    await user.keyboard("{Control>}b{/Control}");

    // Keep selection and apply red
    input.setSelectionRange(6, 11);
    await user.keyboard("{Control>}r{/Control}");

    expect(ref.current?.getValue()).toBe(
      'Hello <format bold="true" red="true">world</format>'
    );
  });

  it("should preserve selection after formatting", async () => {
    const user = userEvent.setup();
    render(<RichTextCellEditor {...defaultProps} />);

    const input = screen.getByRole("textbox");

    // Select "world"
    input.setSelectionRange(6, 11);

    await user.keyboard("{Control>}b{/Control}");

    // Selection should be preserved
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 10)); // Wait for selection to be restored
    });

    expect(input.selectionStart).toBe(6);
    expect(input.selectionEnd).toBe(11);
  });

  it("should handle partial word selection", async () => {
    const user = userEvent.setup();
    const ref = createRef<RichTextCellEditorRef>();
    render(<RichTextCellEditor {...defaultProps} ref={ref} />);

    const input = screen.getByRole("textbox");

    // Select "wor" from "world"
    input.setSelectionRange(6, 9);

    await user.keyboard("{Control>}b{/Control}");

    expect(ref.current?.getValue()).toBe(
      'Hello <format bold="true">wor</format>ld'
    );
  });

  it("should provide correct ref methods", () => {
    const ref = createRef<RichTextCellEditorRef>();
    render(
      <RichTextCellEditor {...defaultProps} value="Test value" ref={ref} />
    );

    expect(ref.current?.getValue()).toBe("Test value");
    expect(ref.current?.isCancelBeforeStart?.()).toBe(false);
    expect(ref.current?.isCancelAfterEnd?.()).toBe(false);
  });

  it("should handle complex text editing with formatting", async () => {
    const user = userEvent.setup();
    const ref = createRef<RichTextCellEditorRef>();
    render(
      <RichTextCellEditor
        {...defaultProps}
        value="Start middle end"
        ref={ref}
      />
    );

    const input = screen.getByRole("textbox");

    // Format "middle"
    input.setSelectionRange(6, 12);
    await user.keyboard("{Control>}b{/Control}");

    // Format "end"
    input.setSelectionRange(13, 16);
    await user.keyboard("{Control>}r{/Control}");

    expect(ref.current?.getValue()).toBe(
      'Start <format bold="true">middle</format> <format red="true">end</format>'
    );
  });
});
