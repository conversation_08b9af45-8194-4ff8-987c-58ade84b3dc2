import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { RichTextCellRenderer } from '../RichTextCellRenderer';

describe('RichTextCellRenderer', () => {
  it('should render empty string for null value', () => {
    render(<RichTextCellRenderer value={null} />);
    expect(screen.getByText('')).toBeInTheDocument();
  });

  it('should render empty string for undefined value', () => {
    render(<RichTextCellRenderer value={undefined} />);
    expect(screen.getByText('')).toBeInTheDocument();
  });

  it('should render plain text without formatting', () => {
    render(<RichTextCellRenderer value="Hello world" />);
    expect(screen.getByText('Hello world')).toBeInTheDocument();
  });

  it('should render non-string values as strings', () => {
    render(<RichTextCellRenderer value={123 as any} />);
    expect(screen.getByText('123')).toBeInTheDocument();
  });

  it('should render bold text with correct styling', () => {
    render(<RichTextCellRenderer value='Hello <format bold="true">world</format>' />);
    
    expect(screen.getByText('Hello ')).toBeInTheDocument();
    
    const boldElement = screen.getByText('world');
    expect(boldElement).toBeInTheDocument();
    expect(boldElement).toHaveStyle({ fontWeight: 'bold' });
  });

  it('should render red text with correct styling', () => {
    render(<RichTextCellRenderer value='Hello <format red="true">world</format>' />);
    
    expect(screen.getByText('Hello ')).toBeInTheDocument();
    
    const redElement = screen.getByText('world');
    expect(redElement).toBeInTheDocument();
    expect(redElement).toHaveStyle({ color: '#dc2626' });
  });

  it('should render combined bold and red formatting', () => {
    render(<RichTextCellRenderer value='Hello <format bold="true" red="true">world</format>' />);
    
    expect(screen.getByText('Hello ')).toBeInTheDocument();
    
    const formattedElement = screen.getByText('world');
    expect(formattedElement).toBeInTheDocument();
    expect(formattedElement).toHaveStyle({ 
      fontWeight: 'bold',
      color: '#dc2626'
    });
  });

  it('should render multiple formatted segments', () => {
    render(<RichTextCellRenderer value='Hello <format bold="true">bold</format> and <format red="true">red</format> text' />);
    
    expect(screen.getByText('Hello ')).toBeInTheDocument();
    expect(screen.getByText(' and ')).toBeInTheDocument();
    expect(screen.getByText(' text')).toBeInTheDocument();
    
    const boldElement = screen.getByText('bold');
    expect(boldElement).toHaveStyle({ fontWeight: 'bold' });
    
    const redElement = screen.getByText('red');
    expect(redElement).toHaveStyle({ color: '#dc2626' });
  });

  it('should render adjacent formatted segments', () => {
    render(<RichTextCellRenderer value='<format bold="true">Bold</format><format red="true">Red</format>' />);
    
    const boldElement = screen.getByText('Bold');
    expect(boldElement).toHaveStyle({ fontWeight: 'bold' });
    
    const redElement = screen.getByText('Red');
    expect(redElement).toHaveStyle({ color: '#dc2626' });
  });

  it('should handle empty formatted segments', () => {
    render(<RichTextCellRenderer value='Hello <format bold="true"></format> world' />);
    
    expect(screen.getByText('Hello ')).toBeInTheDocument();
    expect(screen.getByText(' world')).toBeInTheDocument();
  });

  it('should fallback to original text if parsing fails', () => {
    render(<RichTextCellRenderer value='Malformed <format>text' />);
    expect(screen.getByText('Malformed <format>text')).toBeInTheDocument();
  });

  it('should handle complex mixed formatting', () => {
    const complexText = 'Start <format bold="true">bold only</format> middle <format red="true">red only</format> and <format bold="true" red="true">both</format> end';
    render(<RichTextCellRenderer value={complexText} />);
    
    expect(screen.getByText('Start ')).toBeInTheDocument();
    expect(screen.getByText(' middle ')).toBeInTheDocument();
    expect(screen.getByText(' and ')).toBeInTheDocument();
    expect(screen.getByText(' end')).toBeInTheDocument();
    
    const boldOnlyElement = screen.getByText('bold only');
    expect(boldOnlyElement).toHaveStyle({ fontWeight: 'bold' });
    expect(boldOnlyElement).not.toHaveStyle({ color: '#dc2626' });
    
    const redOnlyElement = screen.getByText('red only');
    expect(redOnlyElement).toHaveStyle({ color: '#dc2626' });
    expect(redOnlyElement).not.toHaveStyle({ fontWeight: 'bold' });
    
    const bothElement = screen.getByText('both');
    expect(bothElement).toHaveStyle({ 
      fontWeight: 'bold',
      color: '#dc2626'
    });
  });

  it('should render with correct HTML structure', () => {
    const { container } = render(<RichTextCellRenderer value='Hello <format bold="true">world</format>' />);
    
    const rootSpan = container.firstChild as HTMLElement;
    expect(rootSpan.tagName).toBe('SPAN');
    
    const childSpans = rootSpan.querySelectorAll('span');
    expect(childSpans).toHaveLength(2); // One for "Hello " and one for "world"
  });

  it('should handle very long text with multiple formatting', () => {
    const longText = 'This is a very long text with <format bold="true">multiple</format> different <format red="true">formatting</format> options and <format bold="true" red="true">combinations</format> to test performance and rendering.';
    render(<RichTextCellRenderer value={longText} />);
    
    expect(screen.getByText('This is a very long text with ')).toBeInTheDocument();
    expect(screen.getByText('multiple')).toHaveStyle({ fontWeight: 'bold' });
    expect(screen.getByText('formatting')).toHaveStyle({ color: '#dc2626' });
    expect(screen.getByText('combinations')).toHaveStyle({ 
      fontWeight: 'bold',
      color: '#dc2626'
    });
  });
});
