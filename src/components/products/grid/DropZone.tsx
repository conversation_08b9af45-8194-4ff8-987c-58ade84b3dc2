import { useState, useCallback } from "react";
import { Input } from "../../ui/Input";

interface DropZoneProps {
  onFilesSelected: (files: File[]) => Promise<void>;
  isLoading: boolean;
  productId: string;
}

export const DropZone = ({
  onFilesSelected,
  isLoading,
  productId,
}: DropZoneProps) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    async (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);
      const files = Array.from(e.dataTransfer.files);
      await onFilesSelected(files);
    },
    [onFilesSelected]
  );

  const handleFileSelect = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      await onFilesSelected(files);
      // Очищаем input после загрузки
      if (e.target) {
        e.target.value = "";
      }
    },
    [onFilesSelected]
  );

  return (
    <div
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      style={{
        border: `2px dashed ${isDragging ? "#2196f3" : "#ccc"}`,
        borderRadius: "4px",
        padding: "20px",
        textAlign: "center",
        backgroundColor: isDragging ? "rgba(33, 150, 243, 0.1)" : "#fafafa",
        cursor: "pointer",
        transition: "all 0.3s ease",
      }}
    >
      <Input
        type="file"
        multiple
        accept="image/*"
        onChange={handleFileSelect}
        style={{ display: "none" }}
        id={`fileInput-modal-${productId}`}
      />
      <label
        htmlFor={`fileInput-modal-${productId}`}
        style={{ cursor: "pointer" }}
      >
        {isLoading
          ? "Загрузка..."
          : "Перетащите изображения сюда или кликните для выбора"}
      </label>
    </div>
  );
};
