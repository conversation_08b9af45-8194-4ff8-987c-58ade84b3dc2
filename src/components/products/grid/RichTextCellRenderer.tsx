import React from "react";
import { ICellRendererParams } from "ag-grid-community";
import {
  parseFormattedText,
  hasFormatting,
} from "../../../utils/textFormatting";
import type { TextSegment } from "../../../utils/textFormatting";

interface RichTextCellRendererParams extends ICellRendererParams {
  value: string;
}

export const RichTextCellRenderer: React.FC<RichTextCellRendererParams> = ({
  value,
}) => {
  if (!value || typeof value !== "string") {
    return <span>{value || ""}</span>;
  }

  if (!hasFormatting(value)) {
    return <span>{value}</span>;
  }

  const segments = parseFormattedText(value);

  if (segments.length === 0) {
    return <span>{value}</span>;
  }

  return (
    <span>
      {segments.map((segment: TextSegment, index: number) => {
        let className = "";
        const styles: React.CSSProperties = {};

        if (segment.bold) {
          styles.fontWeight = "bold";
        }

        if (segment.red) {
          styles.color = "#dc2626"; // red-600
        }

        if (segment.bold || segment.red) {
          return (
            <span key={index} style={styles}>
              {segment.text}
            </span>
          );
        }

        return <span key={index}>{segment.text}</span>;
      })}
    </span>
  );
};

export default RichTextCellRenderer;
