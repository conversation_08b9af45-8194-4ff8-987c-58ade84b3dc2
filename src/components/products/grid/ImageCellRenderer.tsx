import { useState, useCallback, useRef, useEffect } from "react";
import { ICellRendererParams } from "ag-grid-community";
import type { Database } from "../../../types/supabase";
import {
  uploadFilesAndPublish,
  deleteFile,
  getUploadUrl,
  uploadFile,
  getPublicFolderContents,
} from "../../../services/yandexDisk";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import { Button } from "@/components/ui/Button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useProductMutations } from "@/hooks/useProducts";
import {
  ImageIcon,
  Upload,
  Trash2,
  ArrowLeft,
  AlertCircle,
  ExternalLink,
  Copy,
} from "lucide-react";
import { supabase } from "@/lib/supabase";
import { useImageFolder, type FolderItem } from "@/hooks/useImageFolder";
import { toast } from "@/hooks/use-toast";
import { ImageGallery } from "./ImageGallery";

type Product = Database["public"]["Tables"]["products"]["Row"];

// Ключи для localStorage
const DIALOG_STATE_KEY = "image_dialog_state";
const CURRENT_PRODUCT_KEY = "image_dialog_product_id";

export const ImageCellRenderer = (props: ICellRendererParams<Product>) => {
  const { data } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [publicUrl, setPublicUrl] = useState<string | null>(null);
  const [folderPath, setFolderPath] = useState<string | null>(null);
  const [folderName, setFolderName] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isDeletingAll, setIsDeletingAll] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Новые состояния для управления видом
  const [currentView, setCurrentView] = useState<"gallery" | "carousel">(
    "gallery"
  );
  const [activeCarouselIndex, setActiveCarouselIndex] = useState(0);
  const [carouselApi, setCarouselApi] = useState<CarouselApi>();
  const [galleryScrollPosition, setGalleryScrollPosition] = useState(0);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const additionalFileInputRef = useRef<HTMLInputElement>(null);
  const galleryContainerRef = useRef<HTMLDivElement>(null);

  const { updateImages } = useProductMutations();

  // Используем хук для работы с изображениями
  const {
    folderContents,
    isLoadingContents,
    isLoadingDirectUrls,
    error: imageFolderError,
    fetchFolderContents,
    ensureAllDirectUrlsLoaded,
  } = useImageFolder();

  // Добавляем флаг для предотвращения закрытия диалога при обновлении данных
  const isOperationInProgress = useRef(false);

  // Проверяем наличие изображений в базе данных
  const hasImages = data?.id && publicUrl;

  // Инициализация данных при монтировании компонента
  useEffect(() => {
    // Вызываем инициализацию данных при первом рендере
    if (data?.id) {
      initializeData();
    }
  }, [data?.id]); // Зависимость от ID продукта, чтобы перезагружать данные при изменении продукта

  // Объединяем ошибки из разных источников
  useEffect(() => {
    if (imageFolderError) {
      setError(imageFolderError);
    }
  }, [imageFolderError]);

  // Теперь добавляем эффект для проверки сохраненного состояния диалога
  useEffect(() => {
    const savedDialogState = localStorage.getItem(DIALOG_STATE_KEY);
    const savedProductId = localStorage.getItem(CURRENT_PRODUCT_KEY);

    // Открываем диалог, только если он был открыт для текущего продукта
    if (savedDialogState === "open" && savedProductId === String(data?.id)) {
      setIsOpen(true);
      if (publicUrl) {
        fetchFolderContents(publicUrl);
      }
    }
  }, [data?.id, fetchFolderContents, publicUrl]);

  // Получаем и сохраняем информацию об изображениях
  const saveImagesToDb = useCallback(
    async (publicUrl: string, targetFolder: string) => {
      if (!data?.id) return;

      try {
        isOperationInProgress.current = true;

        // Убедимся, что диалог остается открытым
        localStorage.setItem(DIALOG_STATE_KEY, "open");
        if (data?.id) {
          localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
        }

        const items = await getPublicFolderContents(publicUrl);

        // Фильтруем только файлы изображений
        const imageItems = items
          .filter(
            (item) =>
              item.type === "file" &&
              item.name.match(/\.(jpeg|jpg|png|gif|webp)$/i)
          )
          .map((item, index) => ({
            product_id: data.id,
            yandex_disk_path: `/${targetFolder}/${item.name}`,
            public_url: publicUrl,
            sort_order: index,
            is_main: index === 0, // Первое изображение - главное
          }));

        // Сохраняем в базе данных
        await updateImages.mutate({
          productId: data.id,
          images: imageItems,
        });

        // Повторно убедимся, что диалог остается открытым после мутации
        setIsOpen(true);

        isOperationInProgress.current = false;
        return items;
      } catch (err) {
        isOperationInProgress.current = false;
        console.error("Ошибка при сохранении изображений в БД:", err);
        setError(
          err instanceof Error
            ? err.message
            : "Произошла ошибка при сохранении изображений"
        );
        return null;
      }
    },
    [data?.id, updateImages]
  );

  // Загрузка изображений при первой загрузке
  const handleUploadImages = useCallback(
    async (files: File[]) => {
      if (!data?.id) {
        setError("ID товара не найден");
        return;
      }

      // Проверяем, что все файлы - изображения
      const isAllImages = files.every((file) => file.type.startsWith("image/"));
      if (!isAllImages) {
        setError("Пожалуйста, загружайте только изображения");
        return;
      }

      setIsLoading(true);
      setError(null);
      isOperationInProgress.current = true;

      // Сохраняем состояние диалога
      localStorage.setItem(DIALOG_STATE_KEY, "open");
      if (data?.id) {
        localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
      }

      try {
        // Создаем имя папки на основе ID товара и времени
        const timestamp = new Date().getTime();
        const targetFolder = `product_${data.id}_${timestamp}`;
        const path = `/${targetFolder}`;

        // Загружаем файлы
        const url = await uploadFilesAndPublish(files, targetFolder);
        setPublicUrl(url);
        setFolderPath(path);
        setFolderName(targetFolder);

        // Сохраняем информацию в базе данных
        const items = await saveImagesToDb(url, targetFolder);

        // Получаем содержимое папки
        if (items) {
          // Добавляем небольшую задержку перед обновлением содержимого папки
          // чтобы дать Яндекс.Диску время на обработку файла
          setTimeout(async () => {
            await fetchFolderContents(url);
          }, 2000); // 2 секунды задержки
        }

        // После всех операций явно устанавливаем состояние диалога
        setIsOpen(true);

        setSuccessMessage("Изображения успешно загружены");
        setTimeout(() => setSuccessMessage(null), 3000);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Произошла ошибка при загрузке"
        );
      } finally {
        setIsLoading(false);
        isOperationInProgress.current = false;
      }
    },
    [data?.id, saveImagesToDb]
  );

  // Инициализация данных при первом открытии
  const initializeData = useCallback(async () => {
    if (!data?.id) return;

    try {
      // Получаем изображения для продукта
      const { data: productImages, error } = await supabase
        .from("product_images")
        .select("*")
        .eq("product_id", data.id)
        .order("sort_order", { ascending: true });

      if (error) {
        console.error("Ошибка при получении изображений продукта:", error);
        return;
      }

      if (productImages && productImages.length > 0) {
        // Берем информацию из первого изображения
        const firstImage = productImages[0];
        setPublicUrl(firstImage.public_url);

        // Извлекаем folderPath из yandex_disk_path
        const pathMatch = firstImage.yandex_disk_path.match(/^\/([^/]+)/);
        if (pathMatch && pathMatch[1]) {
          const folder = pathMatch[1];
          setFolderName(folder);
          setFolderPath(`/${folder}`);
          console.log("Установлен путь к папке:", `/${folder}`);
        }
      }
    } catch (err) {
      console.error("Ошибка при инициализации данных:", err);
    }
  }, [data?.id]);

  // Догрузка дополнительных файлов
  const handleUploadMore = useCallback(
    async (files: File[]) => {
      if (files.length === 0 || !folderName || !publicUrl || !data?.id) {
        console.error("Не хватает данных для догрузки файлов:", {
          filesCount: files.length,
          folderName,
          publicUrl,
          productId: data?.id,
          folderPath,
        });
        return;
      }

      // Проверяем, что все файлы - изображения
      const isAllImages = files.every((file) => file.type.startsWith("image/"));
      if (!isAllImages) {
        setError("Пожалуйста, загружайте только изображения");
        return;
      }

      setIsUploading(true);
      setError(null);
      setSuccessMessage(null);
      isOperationInProgress.current = true;

      // Сохраняем состояние диалога
      localStorage.setItem(DIALOG_STATE_KEY, "open");
      if (data?.id) {
        localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
      }

      try {
        console.log("Начинаем догрузку файлов в папку:", folderName);
        // Загружаем файлы по одному в существующую папку
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          setUploadProgress(
            `Загрузка ${i + 1} из ${files.length}: ${file.name}`
          );

          const filePath = `/${folderName}/${file.name}`;
          console.log("Загружаем файл по пути:", filePath);
          const uploadUrl = await getUploadUrl(filePath);
          await uploadFile(uploadUrl, file);
        }

        setUploadProgress(null);
        setSuccessMessage(
          `Дополнительные файлы успешно загружены (${files.length} шт.)`
        );

        // Обновляем информацию в базе данных
        await saveImagesToDb(publicUrl, folderName);

        // Добавляем небольшую задержку перед обновлением содержимого папки
        // чтобы дать Яндекс.Диску время на обработку файла
        setTimeout(async () => {
          await fetchFolderContents(publicUrl);
        }, 2000); // 2 секунды задержки

        // После всех операций явно устанавливаем состояние диалога
        setIsOpen(true);

        // Скрываем сообщение через 3 секунды
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        console.error("Ошибка при догрузке файлов:", err);
        setError(
          err instanceof Error
            ? `Ошибка загрузки дополнительных файлов: ${err.message}`
            : "Произошла ошибка при загрузке дополнительных файлов"
        );
        setUploadProgress(null);
      } finally {
        setIsUploading(false);
        isOperationInProgress.current = false;
      }
    },
    [
      folderName,
      publicUrl,
      data?.id,
      fetchFolderContents,
      saveImagesToDb,
      folderPath,
    ]
  );

  // Удаление всех файлов
  const handleDeleteAllImages = useCallback(async () => {
    if (!folderPath || folderContents.length === 0 || !data?.id) {
      console.error("Не хватает данных для удаления всех файлов:", {
        folderPath,
        filesCount: folderContents.length,
        productId: data?.id,
      });
      return;
    }

    if (
      window.confirm(
        `Вы действительно хотите удалить все ${folderContents.length} файлов?`
      )
    ) {
      setIsDeletingAll(true);
      setError(null);
      setSuccessMessage(null);
      isOperationInProgress.current = true;

      // Сохраняем состояние диалога
      localStorage.setItem(DIALOG_STATE_KEY, "open");
      if (data?.id) {
        localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
      }

      try {
        // Удаляем файлы последовательно
        for (const item of folderContents) {
          const filePath = `${folderPath}/${item.name}`;
          console.log("Удаляем файл:", filePath);
          await deleteFile(filePath);
        }

        // Список файлов очищается автоматически после перезагрузки

        // Удаляем информацию о изображениях из базы данных
        await updateImages.mutate({
          productId: data.id,
          images: [],
        });

        // Очищаем состояния
        setPublicUrl(null);
        setFolderPath(null);
        setFolderName(null);

        // После очистки всех изображений сбрасываем состояние диалога,
        // так как больше нет данных для отображения
        localStorage.removeItem(DIALOG_STATE_KEY);
        localStorage.removeItem(CURRENT_PRODUCT_KEY);

        setSuccessMessage(`Все файлы успешно удалены`);

        // Скрываем сообщение через 3 секунды
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        console.error("Ошибка при удалении всех файлов:", err);
        setError(
          err instanceof Error
            ? `Ошибка удаления файлов: ${err.message}`
            : "Произошла ошибка при удалении файлов"
        );
      } finally {
        setIsDeletingAll(false);
        isOperationInProgress.current = false;
      }
    }
  }, [folderPath, folderContents, data?.id, updateImages]);

  // Обработка клика на изображение в галерее
  const handleImageClick = useCallback(
    async (item: FolderItem, index: number) => {
      if (!publicUrl) {
        setError("Ссылка на публичную папку недоступна");
        return;
      }

      // Сохраняем позицию прокрутки галереи
      if (galleryContainerRef.current) {
        setGalleryScrollPosition(galleryContainerRef.current.scrollTop);
      }

      // Устанавливаем индекс активного изображения
      setActiveCarouselIndex(index);

      // Загружаем все directUrl если они еще не загружены
      await ensureAllDirectUrlsLoaded(publicUrl);

      // Переключаемся на вид карусели
      setCurrentView("carousel");
    },
    [publicUrl, ensureAllDirectUrlsLoaded]
  );

  // Обработка нажатий клавиш для карусели
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (currentView !== "carousel" || !carouselApi) return;

      if (event.key === "ArrowLeft") {
        event.preventDefault();
        carouselApi.scrollPrev();
      } else if (event.key === "ArrowRight") {
        event.preventDefault();
        carouselApi.scrollNext();
      } else if (event.key === "Escape") {
        event.preventDefault();
        setCurrentView("gallery");
      }
    };

    if (currentView === "carousel") {
      document.addEventListener("keydown", handleKeyDown);
      return () => {
        document.removeEventListener("keydown", handleKeyDown);
      };
    }
  }, [currentView, carouselApi]);

  // Синхронизация активного индекса с API карусели
  useEffect(() => {
    if (!carouselApi) return;

    carouselApi.scrollTo(activeCarouselIndex);
  }, [carouselApi, activeCarouselIndex]);

  // Восстановление позиции прокрутки при возврате к галерее
  useEffect(() => {
    if (
      currentView === "gallery" &&
      galleryContainerRef.current &&
      galleryScrollPosition > 0
    ) {
      const timer = setTimeout(() => {
        if (galleryContainerRef.current) {
          galleryContainerRef.current.scrollTop = galleryScrollPosition;
        }
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [currentView, galleryScrollPosition]);

  // Удаление отдельного файла
  const handleDeleteImage = useCallback(
    async (item: FolderItem, e?: React.MouseEvent) => {
      if (e) {
        e.stopPropagation(); // Предотвращаем открытие изображения при клике на кнопку удаления
      }

      if (!folderPath) {
        console.error("Путь к папке не найден при удалении изображения");
        setError("Путь к папке не найден");
        return;
      }

      // Формируем полный путь к файлу
      const filePath = `${folderPath}/${item.name}`;
      console.log("Попытка удалить файл по пути:", filePath);

      if (
        window.confirm(`Вы действительно хотите удалить файл ${item.name}?`)
      ) {
        setIsDeleting(item.name);
        setError(null);
        setSuccessMessage(null);
        isOperationInProgress.current = true;

        // Сохраняем состояние диалога
        localStorage.setItem(DIALOG_STATE_KEY, "open");
        if (data?.id) {
          localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
        }

        try {
          await deleteFile(filePath);

          // После удаления перезагружаем содержимое папки
          if (publicUrl) {
            await fetchFolderContents(publicUrl);
          }

          // Обновляем информацию в базе данных
          if (publicUrl && folderName && data?.id) {
            await saveImagesToDb(publicUrl, folderName);
          }

          setSuccessMessage(`Файл ${item.name} успешно удален`);

          // После всех операций явно устанавливаем состояние диалога
          setIsOpen(true);

          // Если удаляли в режиме карусели и остались изображения, возвращаемся к галерее
          if (currentView === "carousel" && folderContents.length <= 1) {
            setCurrentView("gallery");
          }

          // Скрываем сообщение через 3 секунды
          setTimeout(() => {
            setSuccessMessage(null);
          }, 3000);
        } catch (err) {
          console.error("Ошибка при удалении файла:", err);
          setError(
            err instanceof Error
              ? `Ошибка удаления файла: ${err.message}`
              : "Произошла ошибка при удалении файла"
          );
        } finally {
          setIsDeleting(null);
          isOperationInProgress.current = false;
        }
      }
    },
    [
      folderPath,
      publicUrl,
      folderName,
      data?.id,
      saveImagesToDb,
      fetchFolderContents,
      currentView,
      folderContents.length,
    ]
  );

  // Обработка перетаскивания файлов
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length === 0) return;

      if (publicUrl && folderName) {
        console.log("Догружаем файлы в существующую папку:", folderName);
        // Если папка уже создана, догружаем файлы
        handleUploadMore(files);
      } else {
        console.log("Создаем новую папку для файлов");
        // Если папки нет, создаем новую
        handleUploadImages(files);
      }
    },
    [publicUrl, folderName, handleUploadMore, handleUploadImages]
  );

  // Обработка выбора файлов через input
  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      if (files.length === 0) return;

      if (publicUrl && folderName) {
        console.log("Input: Догружаем файлы в существующую папку:", folderName);
        // Если папка уже создана, догружаем файлы
        handleUploadMore(files);
      } else {
        console.log("Input: Создаем новую папку для файлов");
        // Если папки нет, создаем новую
        handleUploadImages(files);
      }

      // Очищаем input
      if (e.target) {
        e.target.value = "";
      }
    },
    [publicUrl, folderName, handleUploadMore, handleUploadImages]
  );

  // Открытие диалога
  const openDialog = useCallback(() => {
    setIsOpen(true);

    // Если есть publicUrl, загружаем содержимое папки
    if (publicUrl) {
      fetchFolderContents(publicUrl);
    }
  }, [publicUrl, fetchFolderContents]);

  // Обработка клика на элемент выбора файла
  const handleSelectFilesClick = useCallback(() => {
    if (publicUrl) {
      console.log("Открываем диалог выбора файлов для догрузки");
      additionalFileInputRef.current?.click();
    } else {
      console.log("Открываем диалог выбора файлов для первой загрузки");
      fileInputRef.current?.click();
    }
  }, [publicUrl]);

  // Модифицируем handleOpenChange чтобы сохранять состояние
  const handleOpenChange = useCallback(
    (open: boolean) => {
      // Не закрываем диалог если операция в процессе
      if (!open && isOperationInProgress.current) {
        return;
      }

      setIsOpen(open);

      // Сохраняем состояние в localStorage
      if (open) {
        localStorage.setItem(DIALOG_STATE_KEY, "open");
        if (data?.id) {
          localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
        }
        initializeData();
      } else {
        localStorage.removeItem(DIALOG_STATE_KEY);
        localStorage.removeItem(CURRENT_PRODUCT_KEY);
      }
    },
    [initializeData, data?.id]
  );

  // Рендер ячейки
  return (
    <>
      <div
        onClick={openDialog}
        className="w-full h-full flex items-center justify-center cursor-pointer hover:bg-gray-50"
      >
        {hasImages ? (
          <div className="flex items-center gap-2">
            <ImageIcon size={18} />
            <span>Посмотреть</span>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Upload size={18} />
            <span>Загрузить</span>
          </div>
        )}
      </div>

      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        <DialogContent className="seamless-modal-content max-w-4xl h-[80vh] flex flex-col fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]">
          {currentView === "carousel" ? (
            // Режим карусели - заменяет весь контент диалога
            <>
              <DialogHeader className="flex-shrink-0">
                <div className="flex justify-center items-center relative">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentView("gallery")}
                    className="flex items-center gap-2 absolute left-0"
                  >
                    <ArrowLeft size={16} />
                    Назад к галерее
                  </Button>
                  <DialogTitle>Просмотр изображений</DialogTitle>
                </div>
              </DialogHeader>

              <div className="flex-1">
                {isLoadingDirectUrls ? (
                  <div className="flex items-center justify-center p-8">
                    <div className="text-center">
                      <svg
                        className="animate-spin h-8 w-8 text-blue-500 mx-auto mb-2"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      <p className="text-blue-500">Загрузка изображений...</p>
                    </div>
                  </div>
                ) : (
                  <div className="relative h-full flex items-center justify-center">
                    <Carousel
                      className="w-full max-w-3xl mx-auto"
                      setApi={setCarouselApi}
                      opts={{
                        startIndex: activeCarouselIndex,
                      }}
                    >
                      <CarouselContent>
                        {folderContents.map((item) => (
                          <CarouselItem key={item.name}>
                            <div className="p-1 relative">
                              <div className="flex aspect-square items-center justify-center p-6">
                                {item.directUrl ? (
                                  <img
                                    src={item.directUrl}
                                    alt={item.name}
                                    className="max-w-full max-h-[50vh] object-contain"
                                  />
                                ) : item.directUrlError ? (
                                  <div className="flex flex-col items-center gap-4 text-gray-500">
                                    <AlertCircle size={48} />
                                    <p>Не удалось загрузить изображение</p>
                                    <p className="text-sm">{item.name}</p>
                                  </div>
                                ) : (
                                  <div className="flex items-center gap-2">
                                    <svg
                                      className="animate-spin h-5 w-5 text-blue-500"
                                      xmlns="http://www.w3.org/2000/svg"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                    >
                                      <circle
                                        className="opacity-25"
                                        cx="12"
                                        cy="12"
                                        r="10"
                                        stroke="currentColor"
                                        strokeWidth="4"
                                      ></circle>
                                      <path
                                        className="opacity-75"
                                        fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                      ></path>
                                    </svg>
                                    <span>Загрузка...</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </CarouselItem>
                        ))}
                      </CarouselContent>
                      <CarouselPrevious />
                      <CarouselNext />
                    </Carousel>
                  </div>
                )}
              </div>
            </>
          ) : (
            // Режим галереи - обычное управление изображениями
            <>
              <DialogHeader className="flex-shrink-0">
                <DialogTitle>Управление изображениями товара</DialogTitle>
              </DialogHeader>

              <div className="flex-1 overflow-auto" ref={galleryContainerRef}>
                <div className="space-y-4 p-1">
                  {/* Область перетаскивания */}
                  <div
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    onClick={handleSelectFilesClick}
                    className={`
                    border-2 border-dashed rounded-md p-6 text-center cursor-pointer transition-colors
                    ${
                      isDragging
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-300 bg-gray-50"
                    }
                    ${isLoading ? "opacity-70 pointer-events-none" : ""}
                  `}
                  >
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleFileSelect}
                      ref={fileInputRef}
                      className="hidden"
                    />
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleFileSelect}
                      ref={additionalFileInputRef}
                      className="hidden"
                    />

                    {isLoading ? (
                      <div className="text-sm text-gray-500">Загрузка...</div>
                    ) : publicUrl ? (
                      <div className="text-sm text-gray-500">
                        Перетащите изображения сюда или кликните для догрузки
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500">
                        Перетащите изображения сюда или кликните для выбора
                      </div>
                    )}
                  </div>

                  {/* Сообщения */}
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  {successMessage && (
                    <Alert>
                      <AlertDescription>{successMessage}</AlertDescription>
                    </Alert>
                  )}

                  {uploadProgress && (
                    <div className="text-sm text-blue-500 p-2 bg-blue-50 rounded-md">
                      {uploadProgress}
                    </div>
                  )}

                  {/* Индикаторы загрузки */}
                  {isUploading && !uploadProgress && (
                    <div className="text-sm text-blue-500 p-2 bg-blue-50 rounded-md flex items-center gap-2">
                      <svg
                        className="animate-spin h-4 w-4 text-blue-500"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Загрузка дополнительных файлов...
                    </div>
                  )}

                  {isLoadingContents && (
                    <div className="text-sm text-blue-500 p-2 bg-blue-50 rounded-md flex items-center gap-2">
                      <svg
                        className="animate-spin h-4 w-4 text-blue-500"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Загрузка содержимого папки...
                    </div>
                  )}

                  {publicUrl && (
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 mt-3 p-3 bg-gray-50 rounded-md">
                      <div className="flex flex-wrap gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            window.open(
                              publicUrl,
                              "_blank",
                              "noopener,noreferrer"
                            )
                          }
                          className="flex items-center"
                        >
                          <ExternalLink className="h-4 w-4 mr-1.5" />
                          Перейти на Яндекс.Диск
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={async () => {
                            if (!publicUrl) return;
                            try {
                              await navigator.clipboard.writeText(publicUrl);
                              toast({
                                title: "Успех",
                                description:
                                  "Ссылка скопирована в буфер обмена.",
                              });
                            } catch (err) {
                              console.error("Ошибка копирования ссылки: ", err);
                              toast({
                                title: "Ошибка",
                                description: "Не удалось скопировать ссылку.",
                                variant: "destructive",
                              });
                            }
                          }}
                          className="flex items-center"
                        >
                          <Copy className="h-4 w-4 mr-1.5" />
                          Копировать ссылку
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Галерея изображений */}
                  {folderContents.length > 0 && (
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="text-lg font-medium">
                          Загруженные изображения:
                        </h3>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={handleDeleteAllImages}
                          disabled={isDeletingAll}
                          className="flex items-center gap-1"
                        >
                          <Trash2 size={14} />
                          {isDeletingAll ? "Удаление..." : "Удалить все"}
                        </Button>
                      </div>
                      <ImageGallery
                        key={`gallery-${folderContents.length}-${Date.now()}`}
                        folderContents={folderContents}
                        onImageClick={handleImageClick}
                        onDeleteImage={handleDeleteImage}
                        isLoading={isLoadingContents}
                        isDeleting={isDeleting}
                      />
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};
