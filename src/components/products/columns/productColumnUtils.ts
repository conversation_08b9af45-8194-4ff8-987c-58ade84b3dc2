// Список стандартных полей таблицы products
export const STANDARD_FIELDS = [
  "id", // Техническое поле, обычно не показываем
  "name",
  "weight",
  "price",
  "validity_period",
  "is_hidden",
  "created_at",
  "updated_at", // Техническое поле, обычно не показываем
  "product_images",
  "group_id",
  "order_in_group", // Техническое поле, обычно не показываем
  "custom_fields", // Само поле JSONB, его тоже не показываем как отдельный столбец настроек
];

// Известные пользовательские поля (можно оставить пустым, если генерация имен надежна)
export const KNOWN_CUSTOM_FIELDS: string[] = []; // Пример: ["test_field"];

/**
 * Проверка является ли поле пользовательским
 * (Логика остается прежней, но опирается на обновленный STANDARD_FIELDS)
 */
export function isCustomField(columnName: string): boolean {
  // Исключаем стандартные поля и само поле custom_fields
  if (STANDARD_FIELDS.includes(columnName) || columnName === "custom_fields") {
    return false;
  }
  // Все остальное считаем пользовательским
  return true;
}

/**
 * Получение пути для поля (для пользовательских полей преобразует к формату custom_fields.name)
 */
export function getFieldPath(columnName: string): string {
  if (isCustomField(columnName)) {
    // Убедимся, что имя поля не содержит уже префикс
    const actualFieldName = columnName.startsWith("custom_field.")
      ? columnName.split(".")[1]
      : columnName;
    return `custom_fields.${actualFieldName}`;
  }
  return columnName;
}

/**
 * Получение имени пользовательского поля из пути
 */
export function getCustomFieldName(fieldPath: string): string | null {
  const parts = fieldPath.split(".");
  if (parts.length === 2 && parts[0] === "custom_fields") {
    return parts[1];
  }
  // Если пришло имя без префикса и оно пользовательское, возвращаем его
  if (isCustomField(fieldPath)) {
    return fieldPath;
  }
  return null;
}

/**
 * Проверяет и преобразует пользовательские поля если необходимо
 * (Логика остается прежней)
 */
export function ensureCustomFieldsObject(
  customFields: unknown
): Record<string, unknown> {
  if (!customFields) {
    return {} as Record<string, unknown>;
  }

  if (typeof customFields === "string") {
    try {
      return JSON.parse(customFields);
    } catch (e) {
      console.error(
        `[ERROR] Не удалось преобразовать строку custom_fields:`,
        e
      );
      return {} as Record<string, unknown>;
    }
  }

  if (typeof customFields === "object" && customFields !== null) {
    return customFields as Record<string, unknown>;
  }

  return {} as Record<string, unknown>;
}
