import { use<PERSON>allback, useMemo } from "react";
import { ColDef } from "ag-grid-community";
import { useColumnSettings } from "../../../hooks/useColumnSettings";
import { isCustomField, getFieldPath } from "./productColumnUtils";
import {
  ImageCellRenderer,
  CustomFieldCellRenderer,
  VisibilityStatusRenderer,
  RichTextCellRenderer,
  RichTextCellEditor,
} from "../grid";
import {
  useProductGroups,
  ProductGroup,
} from "../../../hooks/useProductGroups";

import type { Database } from "../../../types/supabase";

type ColumnSettings = Database["public"]["Tables"]["column_settings"]["Row"];

interface UseProductColumnsProps {
  userRole: string | null;
  previewColumnSettings?: ColumnSettings[] | null;
  previewGroupsData?: ProductGroup[] | null;
  isPreviewMode?: boolean;
}

export function useProductColumns({
  userRole,
  previewColumnSettings,
  previewGroupsData,
  isPreviewMode = false,
}: UseProductColumnsProps) {
  // Only fetch live data if we're not in preview mode
  const { data: liveColumnSettingsData } = useColumnSettings();
  const { data: liveGroupsData } = useProductGroups();

  // Use preview data if available, otherwise use live data
  const columnSettingsData = previewColumnSettings
    ? { data: previewColumnSettings }
    : liveColumnSettingsData;

  const groupsData = previewGroupsData
    ? { data: previewGroupsData }
    : liveGroupsData;

  // Debug logging to track column settings changes
  console.log("[useProductColumns] Mode:", isPreviewMode ? "PREVIEW" : "LIVE");
  console.log(
    "[useProductColumns] Column settings count:",
    columnSettingsData?.data?.length || 0
  ); // Получаем данные о группах

  // Создаем Map из групп для быстрого доступа
  const groupsMap = useMemo(() => {
    if (!groupsData?.data) return new Map<string, ProductGroup>();
    return new Map(groupsData.data.map((group) => [group.id, group]));
  }, [groupsData?.data]);

  // Функция для создания определения столбца на основе настроек
  const createColumnDef = useCallback(
    (column: ColumnSettings): ColDef => {
      const isNumberField = column.data_type === "number";
      const isColumnCustomField = isCustomField(column.name);
      const fieldPath = getFieldPath(column.name);

      const { prefix, postfix, name: columnName, display_name } = column;

      const colDef: ColDef = {
        field: fieldPath,
        headerName: display_name,
        editable: userRole !== "reader",
        cellEditorParams: isColumnCustomField
          ? {
              isCustomField: true,
              dataType: isNumberField ? "number" : "text",
            }
          : undefined,
        filter: isNumberField ? "agNumberColumnFilter" : "agTextColumnFilter",
      };

      // Общие настройки для числовых полей (редактор, парсер)
      if (isNumberField) {
        colDef.cellEditor = "agNumberCellEditor";
        colDef.valueParser = (params) => {
          const parsedValue = parseFloat(params.newValue);
          return isNaN(parsedValue) ? null : parsedValue;
        };
        colDef.cellEditorPopup = false;
        colDef.cellEditorPopupPosition = "under";
      }

      // Применение valueFormatter ТОЛЬКО для стандартных числовых полей
      if (isNumberField && !isColumnCustomField) {
        colDef.valueFormatter = (params) => {
          if (params.value === null || params.value === undefined) return "";
          const currentPrefix = prefix || "";
          const currentPostfix = postfix || "";
          let valueStr = String(params.value);
          if (currentPostfix === "₽") {
            const numValue = Number(params.value);
            if (!isNaN(numValue)) {
              valueStr = numValue.toFixed(2);
            }
          }
          return `${currentPrefix} ${valueStr} ${currentPostfix}`;
        };
      }

      // Настройки для стандартных НЕчисловых полей или специфичные рендереры
      if (!isColumnCustomField) {
        switch (columnName) {
          case "is_hidden":
            colDef.filter = "agSetColumnFilter";
            colDef.cellRenderer = VisibilityStatusRenderer;
            break;
          case "product_images":
            colDef.filter = false;
            colDef.sortable = false;
            colDef.cellRenderer = ImageCellRenderer;
            break;
          case "created_at":
            colDef.filter = "agDateColumnFilter";
            colDef.valueFormatter = (params) =>
              new Date(params.value).toLocaleDateString();
            break;
          case "group_id":
            colDef.headerName = "Группа";
            colDef.cellEditor = "agSelectCellEditor";
            colDef.cellEditorParams = {
              values: groupsData?.data?.map((g) => g.id) || [],
            };
            colDef.valueFormatter = (params) => {
              const groupId = params.value;
              if (!groupId) return "Без группы";
              const group = groupsMap.get(groupId);
              return group ? group.name : "Без группы";
            };
            break;
          case "name":
            // Поле "name" поддерживает форматирование
            colDef.cellRenderer = RichTextCellRenderer;
            colDef.cellEditor = RichTextCellEditor;
            break;
        }
      } else {
        // Для пользовательских полей
        if (isNumberField) {
          colDef.cellRenderer = CustomFieldCellRenderer;
          colDef.cellRendererParams = {
            prefix: prefix,
            postfix: postfix,
            dataType: "number",
          };
        } else {
          // Текстовые кастомные поля поддерживают форматирование
          colDef.cellRenderer = RichTextCellRenderer;
          colDef.cellEditor = RichTextCellEditor;
        }
      }

      return colDef;
    },
    [groupsData?.data, groupsMap, userRole]
  );

  // Генерация столбцов на основе настроек
  const columnDefs = useMemo<ColDef[]>(() => {
    // Force recalculation when switching between preview and live modes
    const modeKey = isPreviewMode ? "preview" : "live";
    console.log(
      `[useProductColumns] Recalculating columns for mode: ${modeKey}, count: ${
        columnSettingsData?.data?.length || 0
      }`
    );
    // Первый столбец с селектором всегда присутствует
    const baseColumns: ColDef[] = [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 40,
        pinned: "left",
        resizable: false,
        sortable: false,
        filter: false,
        cellStyle: {
          padding: "0",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        },
      },
    ];

    // Если настройки столбцов загружены, используем их
    if (columnSettingsData?.data && columnSettingsData.data.length > 0) {
      const customColumns = columnSettingsData.data
        .filter((column) => column.is_visible)
        .sort((a, b) => a.order_index - b.order_index)
        .map((column) => createColumnDef(column));

      return [...baseColumns, ...customColumns];
    }

    // Иначе возвращаем стандартные столбцы
    return [
      ...baseColumns,
      {
        field: "name",
        headerName: "Наименование",
        editable: userRole !== "reader",
        filter: "agTextColumnFilter",
        cellRenderer: RichTextCellRenderer,
        cellEditor: RichTextCellEditor,
      },
      {
        field: "group_id",
        headerName: "Группа",
        editable: userRole !== "reader",
        filter: "agTextColumnFilter",
        cellEditor: "agSelectCellEditor",
        cellEditorParams: {
          values: groupsData?.data?.map((g) => g.id) || [],
        },
        valueFormatter: (params) => {
          const groupId = params.value;
          if (!groupId) return "Без группы";
          const group = groupsMap.get(groupId);
          return group ? group.name : "Без группы";
        },
      },
      {
        field: "weight",
        headerName: "Упаковка",
        editable: userRole !== "reader",
        filter: "agNumberColumnFilter",
      },
      {
        field: "validity_period",
        headerName: "Сроки реализации",
        editable: userRole !== "reader",
        filter: "agNumberColumnFilter",
      },
      {
        field: "price",
        headerName: "Цена",
        editable: userRole !== "reader",
        filter: "agNumberColumnFilter",
      },
      {
        field: "is_hidden",
        headerName: "Статус",
        filter: "agSetColumnFilter",
        cellRenderer: VisibilityStatusRenderer,
      },
      {
        field: "product_images",
        headerName: "Изображения",
        filter: false,
        sortable: false,
        cellRenderer: ImageCellRenderer,
      },
      {
        field: "created_at",
        headerName: "Дата добавления",
        filter: "agDateColumnFilter",
        valueFormatter: (params) => new Date(params.value).toLocaleDateString(),
      },
    ];
  }, [
    columnSettingsData?.data,
    createColumnDef,
    groupsData?.data,
    groupsMap,
    userRole,
    isPreviewMode,
  ]);

  const defaultColDef = useMemo(
    () => ({
      sortable: true,
      resizable: true,
      cellStyle: {
        padding: "2px 8px",
        lineHeight: "1.2",
        fontSize: "13px",
        display: "flex",
        alignItems: "center",
      },
      headerClass: "compact-header",
    }),
    []
  );

  return {
    columnDefs,
    defaultColDef,
    createColumnDef,
  };
}
