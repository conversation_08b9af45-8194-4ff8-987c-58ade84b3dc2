import { useCallback, useEffect, useRef } from "react";
import { GridApi } from "ag-grid-community";

export interface AutoSizingOptions {
  skipHeader?: boolean;
  columnIds?: string[];
}

export function useAutoSizing(gridApi: GridApi | null) {
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const columnDebounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitializedRef = useRef(false);

  const getResizableColumnIds = useCallback(() => {
    if (!gridApi) return [];

    const allColumns = gridApi.getColumns();
    if (!allColumns) return [];

    // Исключаем столбцы с чекбоксами и другие служебные столбцы
    return allColumns
      .filter((column) => {
        const colDef = column.getColDef();
        // Исключаем столбцы с чекбоксами
        if (colDef.checkboxSelection || colDef.headerCheckboxSelection) {
          return false;
        }
        // Исключаем столбцы без поля field
        if (!colDef.field) {
          return false;
        }
        // Исключаем столбцы с resizable: false
        if (colDef.resizable === false) {
          return false;
        }
        return true;
      })
      .map((column) => column.getColId());
  }, [gridApi]);

  const autoSizeAllColumns = useCallback(
    (options: AutoSizingOptions = {}) => {
      if (!gridApi) return;

      try {
        // Используем только изменяемые столбцы
        const resizableColumnIds = getResizableColumnIds();
        if (resizableColumnIds.length > 0) {
          gridApi.autoSizeColumns(resizableColumnIds, options.skipHeader);
        }
      } catch (error) {
        console.error("Error auto-sizing all columns:", error);
      }
    },
    [gridApi, getResizableColumnIds]
  );

  const autoSizeColumns = useCallback(
    (columnIds: string[], options: AutoSizingOptions = {}) => {
      if (!gridApi || !columnIds.length) return;

      try {
        gridApi.autoSizeColumns(columnIds, options.skipHeader);
      } catch (error) {
        console.error("Error auto-sizing specific columns:", error);
      }
    },
    [gridApi]
  );

  const debouncedAutoSize = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      autoSizeAllColumns({ skipHeader: false });
    }, 150);
  }, [autoSizeAllColumns]);

  const debouncedColumnAutoSize = useCallback(
    (columnId: string) => {
      if (columnDebounceTimeoutRef.current) {
        clearTimeout(columnDebounceTimeoutRef.current);
      }

      columnDebounceTimeoutRef.current = setTimeout(() => {
        autoSizeColumns([columnId], { skipHeader: false });
      }, 100);
    },
    [autoSizeColumns]
  );

  const triggerAutoSize = useCallback(() => {
    if (!gridApi || !isInitializedRef.current) return;
    debouncedAutoSize();
  }, [gridApi, debouncedAutoSize]);

  const triggerColumnAutoSize = useCallback(
    (columnId: string) => {
      if (!gridApi || !isInitializedRef.current || !columnId) return;

      // Проверяем, является ли столбец изменяемым
      const resizableColumnIds = getResizableColumnIds();
      if (!resizableColumnIds.includes(columnId)) {
        console.log(
          `[AutoSizing] Skipping auto-size for non-resizable column: ${columnId}`
        );
        return;
      }

      debouncedColumnAutoSize(columnId);
    },
    [gridApi, debouncedColumnAutoSize, getResizableColumnIds]
  );

  const initializeAutoSizing = useCallback(() => {
    if (!gridApi || isInitializedRef.current) return;

    setTimeout(() => {
      autoSizeAllColumns({ skipHeader: false });
      isInitializedRef.current = true;
    }, 100);
  }, [gridApi, autoSizeAllColumns]);

  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (columnDebounceTimeoutRef.current) {
        clearTimeout(columnDebounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    autoSizeAllColumns,
    autoSizeColumns,
    triggerAutoSize,
    triggerColumnAutoSize,
    initializeAutoSizing,
  };
}
