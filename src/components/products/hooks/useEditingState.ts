import { useState, useCallback } from "react";
import {
  CellEditingStartedEvent,
  CellEditingStoppedEvent,
} from "ag-grid-community";

export interface EditingState {
  isCellEditing: boolean;
  isGroupEditing: boolean;
  editingCellId: string | null;
  editingGroupId: string | null;
}

export function useEditingState() {
  const [editingState, setEditingState] = useState<EditingState>({
    isCellEditing: false,
    isGroupEditing: false,
    editingCellId: null,
    editingGroupId: null,
  });

  const isAnyEditing =
    editingState.isCellEditing || editingState.isGroupEditing;

  const onCellEditingStarted = useCallback((event: CellEditingStartedEvent) => {
    const cellId = `${event.node.id}-${event.column.getColId()}`;
    console.log("[EditingState] Cell editing started:", cellId);
    setEditingState((prev) => ({
      ...prev,
      isCellEditing: true,
      editingCellId: cellId,
    }));
  }, []);

  const onCellEditingStopped = useCallback((event: CellEditingStoppedEvent) => {
    console.log("[EditingState] Cell editing stopped");
    setEditingState((prev) => ({
      ...prev,
      isCellEditing: false,
      editingCellId: null,
    }));
  }, []);

  const setGroupEditing = useCallback(
    (groupId: string | null, isEditing: boolean) => {
      console.log("[EditingState] Group editing changed:", {
        groupId,
        isEditing,
      });
      setEditingState((prev) => ({
        ...prev,
        isGroupEditing: isEditing,
        editingGroupId: isEditing ? groupId : null,
      }));
    },
    []
  );

  return {
    editingState,
    isAnyEditing,
    onCellEditingStarted,
    onCellEditingStopped,
    setGroupEditing,
  };
}
