import { cn } from "@/lib/utils";
import React, { HTMLAttributes } from "react";

export type DividerOrientation = "horizontal" | "vertical";
export type DividerVariant = "solid" | "dashed" | "dotted";

export interface DividerProps extends HTMLAttributes<HTMLDivElement> {
  orientation?: DividerOrientation;
  variant?: DividerVariant;
  className?: string;
  label?: string;
}

export const Divider = React.forwardRef<HTMLDivElement, DividerProps>(
  (
    {
      orientation = "horizontal",
      variant = "solid",
      className = "",
      label,
      ...props
    },
    ref
  ) => {
    // Базовые стили для разделителя
    const baseStyles = {
      horizontal: "w-full my-4",
      vertical: "h-full mx-2",
    };

    // Стили для разных вариантов
    const variantStyles = {
      solid: "border-solid",
      dashed: "border-dashed",
      dotted: "border-dotted",
    };

    // Стили для ориентации
    const orientationStyles = {
      horizontal: "border-t border-gray-200",
      vertical: "border-l border-gray-200 h-full",
    };

    // Собираем все стили вместе
    const dividerStyles = cn(
      baseStyles[orientation],
      variantStyles[variant],
      orientationStyles[orientation],
      className
    );

    // Если есть метка, то отображаем разделитель с меткой
    if (label && orientation === "horizontal") {
      return (
        <div className="relative" ref={ref} {...props}>
          <div className="absolute inset-0 flex items-center">
            <div className={dividerStyles}></div>
          </div>
          <div className="relative flex justify-center">
            <span className="bg-white px-2 text-sm text-gray-500">{label}</span>
          </div>
        </div>
      );
    }

    return <div ref={ref} className={dividerStyles} {...props} />;
  }
);

Divider.displayName = "Divider";
