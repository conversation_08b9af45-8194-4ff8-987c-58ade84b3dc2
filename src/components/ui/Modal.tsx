import React, { ReactNode, useEffect } from "react";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  size?: "sm" | "md" | "lg" | "xl" | "full";
  closeOnEsc?: boolean;
  closeOnOverlayClick?: boolean;
  className?: string;
  headerClassName?: string;
  footer?: ReactNode;
  footerClassName?: string;
  bodyClassName?: string;
  showCloseButton?: boolean;
}

export function Modal({
  isOpen,
  onClose,
  title,
  children,
  size = "md",
  closeOnEsc = true,
  closeOnOverlayClick = true,
  className = "",
  headerClassName = "",
  footer,
  footerClassName = "",
  bodyClassName = "",
  showCloseButton = true,
}: ModalProps) {
  // Обработка нажатия клавиши Escape
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (closeOnEsc && event.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscKey);
      // Блокируем прокрутку body при открытом модальном окне
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey);
      // Восстанавливаем прокрутку при закрытии
      document.body.style.overflow = "";
    };
  }, [isOpen, onClose, closeOnEsc]);

  // Если модальное окно закрыто, не рендерим ничего
  if (!isOpen) return null;

  // Размеры модального окна
  const sizeClasses = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    full: "max-w-full h-full",
  };

  // Обработчик клика по оверлею
  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
      onClick={handleOverlayClick}
    >
      <div
        className={cn(
          "bg-white rounded-lg shadow-xl w-full flex flex-col",
          "max-h-[90vh]",
          sizeClasses[size],
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Заголовок модального окна */}
        {(title || showCloseButton) && (
          <div
            className={cn(
              "flex-shrink-0 flex justify-between items-center p-4 border-b border-gray-200",
              headerClassName
            )}
          >
            {title && (
              <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
            )}
            {showCloseButton && (
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
                aria-label="Закрыть"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
        )}

        {/* Основное содержимое (растягивается и прокручивается) */}
        <div className={cn("flex-grow overflow-y-auto", bodyClassName)}>
          {children}
        </div>

        {/* Футер (не сжимается, если есть) */}
        {footer && (
          <div
            className={cn(
              "flex-shrink-0 p-4 border-t border-gray-200",
              footerClassName
            )}
          >
            {footer}
          </div>
        )}
      </div>
    </div>
  );
}
