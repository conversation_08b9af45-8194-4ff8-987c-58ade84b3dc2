import React, { ButtonHTMLAttributes, ReactNode } from "react";
import { cn } from "@/lib/utils";

export type IconButtonVariant =
  | "primary"
  | "secondary"
  | "danger"
  | "success"
  | "outline"
  | "ghost";
export type IconButtonSize = "sm" | "md" | "lg";

export interface IconButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement> {
  icon: ReactNode;
  variant?: IconButtonVariant;
  size?: IconButtonSize;
  isLoading?: boolean;
  tooltip?: string;
  className?: string;
}

export const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  (
    {
      icon,
      variant = "secondary",
      size = "md",
      isLoading = false,
      tooltip,
      className = "",
      disabled,
      ...props
    },
    ref
  ) => {
    // Базовые стили для всех кнопок
    const baseStyles =
      "inline-flex items-center justify-center rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors";

    // Стили для разных вариантов
    const variantStyles = {
      primary:
        "bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500 border border-transparent",
      secondary:
        "bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500 border border-gray-300",
      danger:
        "bg-white text-red-700 hover:bg-red-50 focus:ring-red-500 border border-red-300",
      success:
        "bg-white text-green-700 hover:bg-green-50 focus:ring-green-500 border border-green-300",
      outline:
        "bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500 border border-gray-300",
      ghost:
        "bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500 border-none",
    };

    // Стили для разных размеров
    const sizeStyles = {
      sm: "p-1",
      md: "p-2",
      lg: "p-3",
    };

    // Стили для состояния disabled
    const disabledStyles = "opacity-50 cursor-not-allowed";

    // Собираем все стили вместе
    const buttonStyles = cn(
      baseStyles,
      variantStyles[variant],
      sizeStyles[size],
      isLoading || disabled ? disabledStyles : "",
      className
    );

    return (
      <button
        ref={ref}
        className={buttonStyles}
        disabled={isLoading || disabled}
        title={tooltip}
        {...props}
      >
        {isLoading ? (
          <svg
            className="animate-spin h-5 w-5 text-current"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        ) : (
          icon
        )}
      </button>
    );
  }
);

IconButton.displayName = "IconButton";
