import { describe, it, expect, beforeEach } from "vitest";
import {
  hasFormatting,
  parseFormattedText,
  createFormatTag,
  getPlainText,
  getFormattingAtPosition,
  toggleFormatting,
  isTextFormatted,
  clearParseCache,
  getParseCacheSize,
  type TextSegment,
  type FormatAttributes,
  type FormatType,
} from "../textFormatting";

describe("textFormatting utilities", () => {
  beforeEach(() => {
    clearParseCache();
  });

  describe("hasFormatting", () => {
    it("should return false for plain text", () => {
      expect(hasFormatting("Hello world")).toBe(false);
      expect(hasFormatting("")).toBe(false);
      expect(hasFormatting("Simple text without formatting")).toBe(false);
    });

    it("should return true for text with bold formatting", () => {
      expect(hasFormatting('<format bold="true">Hello</format>')).toBe(true);
    });

    it("should return true for text with red formatting", () => {
      expect(hasFormatting('<format red="true">Hello</format>')).toBe(true);
    });

    it("should return true for text with combined formatting", () => {
      expect(
        hasFormatting('<format bold="true" red="true">Hello</format>')
      ).toBe(true);
    });

    it("should return true for text with multiple format tags", () => {
      expect(
        hasFormatting(
          'Hello <format bold="true">world</format> and <format red="true">text</format>'
        )
      ).toBe(true);
    });

    it("should handle malformed tags gracefully", () => {
      expect(hasFormatting("<format>incomplete")).toBe(false);
      expect(hasFormatting('format bold="true">missing opening</format>')).toBe(
        false
      );
    });
  });

  describe("parseFormattedText", () => {
    it("should return empty array for empty input", () => {
      expect(parseFormattedText("")).toEqual([]);
      expect(parseFormattedText(null as any)).toEqual([]);
      expect(parseFormattedText(undefined as any)).toEqual([]);
    });

    it("should return single segment for plain text", () => {
      const result = parseFormattedText("Hello world");
      expect(result).toEqual([{ text: "Hello world" }]);
    });

    it("should parse bold formatting correctly", () => {
      const result = parseFormattedText(
        'Hello <format bold="true">world</format>'
      );
      expect(result).toEqual([
        { text: "Hello " },
        { text: "world", bold: true },
      ]);
    });

    it("should parse red formatting correctly", () => {
      const result = parseFormattedText(
        'Hello <format red="true">world</format>'
      );
      expect(result).toEqual([
        { text: "Hello " },
        { text: "world", red: true },
      ]);
    });

    it("should parse combined formatting correctly", () => {
      const result = parseFormattedText(
        'Hello <format bold="true" red="true">world</format>'
      );
      expect(result).toEqual([
        { text: "Hello " },
        { text: "world", bold: true, red: true },
      ]);
    });

    it("should parse multiple format tags", () => {
      const result = parseFormattedText(
        'Hello <format bold="true">bold</format> and <format red="true">red</format> text'
      );
      expect(result).toEqual([
        { text: "Hello " },
        { text: "bold", bold: true },
        { text: " and " },
        { text: "red", red: true },
        { text: " text" },
      ]);
    });

    it("should handle adjacent format tags", () => {
      const result = parseFormattedText(
        '<format bold="true">Bold</format><format red="true">Red</format>'
      );
      expect(result).toEqual([
        { text: "Bold", bold: true },
        { text: "Red", red: true },
      ]);
    });

    it("should handle empty format tags", () => {
      const result = parseFormattedText(
        'Hello <format bold="true"></format> world'
      );
      expect(result).toEqual([
        { text: "Hello " },
        { text: "", bold: true },
        { text: " world" },
      ]);
    });

    it("should use cache for repeated parsing", () => {
      const text = 'Hello <format bold="true">world</format>';

      parseFormattedText(text);
      expect(getParseCacheSize()).toBe(1);

      const result1 = parseFormattedText(text);
      const result2 = parseFormattedText(text);

      expect(result1).toEqual(result2);
      expect(getParseCacheSize()).toBe(1);
    });

    it("should limit cache size", () => {
      for (let i = 0; i < 150; i++) {
        parseFormattedText(`Text ${i} <format bold="true">formatted</format>`);
      }

      expect(getParseCacheSize()).toBeLessThanOrEqual(100);
    });
  });

  describe("createFormatTag", () => {
    it("should return plain text when no attributes provided", () => {
      expect(createFormatTag("hello", {})).toBe("hello");
    });

    it("should create bold format tag", () => {
      expect(createFormatTag("hello", { bold: true })).toBe(
        '<format bold="true">hello</format>'
      );
    });

    it("should create red format tag", () => {
      expect(createFormatTag("hello", { red: true })).toBe(
        '<format red="true">hello</format>'
      );
    });

    it("should create combined format tag", () => {
      expect(createFormatTag("hello", { bold: true, red: true })).toBe(
        '<format bold="true" red="true">hello</format>'
      );
    });

    it("should handle empty text", () => {
      expect(createFormatTag("", { bold: true })).toBe(
        '<format bold="true"></format>'
      );
    });

    it("should ignore false attributes", () => {
      expect(createFormatTag("hello", { bold: false, red: true })).toBe(
        '<format red="true">hello</format>'
      );
    });
  });

  describe("getPlainText", () => {
    it("should return plain text unchanged", () => {
      expect(getPlainText("Hello world")).toBe("Hello world");
      expect(getPlainText("")).toBe("");
    });

    it("should remove single format tag", () => {
      expect(getPlainText('Hello <format bold="true">world</format>')).toBe(
        "Hello world"
      );
    });

    it("should remove multiple format tags", () => {
      expect(
        getPlainText(
          'Hello <format bold="true">bold</format> and <format red="true">red</format> text'
        )
      ).toBe("Hello bold and red text");
    });

    it("should remove combined format tags", () => {
      expect(
        getPlainText('Hello <format bold="true" red="true">world</format>')
      ).toBe("Hello world");
    });

    it("should handle adjacent format tags", () => {
      expect(
        getPlainText(
          '<format bold="true">Bold</format><format red="true">Red</format>'
        )
      ).toBe("BoldRed");
    });

    it("should handle empty format tags", () => {
      expect(getPlainText('Hello <format bold="true"></format> world')).toBe(
        "Hello  world"
      );
    });
  });

  describe("getFormattingAtPosition", () => {
    it("should return empty attributes for plain text", () => {
      expect(getFormattingAtPosition("Hello world", 0, 5)).toEqual({});
      expect(getFormattingAtPosition("Hello world", 6, 11)).toEqual({});
    });

    it("should return bold attribute for bold text", () => {
      const text = 'Hello <format bold="true">world</format>';
      expect(getFormattingAtPosition(text, 6, 11)).toEqual({ bold: true });
    });

    it("should return red attribute for red text", () => {
      const text = 'Hello <format red="true">world</format>';
      expect(getFormattingAtPosition(text, 6, 11)).toEqual({ red: true });
    });

    it("should return combined attributes for combined formatting", () => {
      const text = 'Hello <format bold="true" red="true">world</format>';
      expect(getFormattingAtPosition(text, 6, 11)).toEqual({
        bold: true,
        red: true,
      });
    });

    it("should handle partial selection within formatted text", () => {
      const text = 'Hello <format bold="true">world</format>';
      expect(getFormattingAtPosition(text, 7, 9)).toEqual({ bold: true });
    });

    it("should handle selection spanning multiple segments", () => {
      const text =
        'Hello <format bold="true">bold</format> and <format red="true">red</format>';
      // Plain text: "Hello bold and red"
      // Selection 6-18 covers "bold and red" which includes both bold and red segments
      expect(getFormattingAtPosition(text, 6, 18)).toEqual({
        bold: true,
        red: true,
      });
      // Selection 6-15 covers "bold and " which only includes bold segment
      expect(getFormattingAtPosition(text, 6, 15)).toEqual({
        bold: true,
      });
    });

    it("should handle selection at segment boundaries", () => {
      const text = 'Hello <format bold="true">world</format> text';
      expect(getFormattingAtPosition(text, 5, 6)).toEqual({});
      expect(getFormattingAtPosition(text, 6, 7)).toEqual({ bold: true });
      expect(getFormattingAtPosition(text, 10, 12)).toEqual({ bold: true });
    });

    it("should handle empty selection", () => {
      const text = 'Hello <format bold="true">world</format>';
      expect(getFormattingAtPosition(text, 7, 7)).toEqual({});
    });

    it("should handle out-of-bounds positions", () => {
      const text = 'Hello <format bold="true">world</format>';
      expect(getFormattingAtPosition(text, 20, 25)).toEqual({});
      expect(getFormattingAtPosition(text, -5, 0)).toEqual({});
    });
  });

  describe("isTextFormatted", () => {
    it("should return false for plain text", () => {
      expect(isTextFormatted("Hello world", 0, 5, "bold")).toBe(false);
      expect(isTextFormatted("Hello world", 0, 5, "red")).toBe(false);
    });

    it("should return true for bold text when checking bold", () => {
      const text = 'Hello <format bold="true">world</format>';
      expect(isTextFormatted(text, 6, 11, "bold")).toBe(true);
    });

    it("should return false for bold text when checking red", () => {
      const text = 'Hello <format bold="true">world</format>';
      expect(isTextFormatted(text, 6, 11, "red")).toBe(false);
    });

    it("should return true for red text when checking red", () => {
      const text = 'Hello <format red="true">world</format>';
      expect(isTextFormatted(text, 6, 11, "red")).toBe(true);
    });

    it("should return true for combined formatting", () => {
      const text = 'Hello <format bold="true" red="true">world</format>';
      expect(isTextFormatted(text, 6, 11, "bold")).toBe(true);
      expect(isTextFormatted(text, 6, 11, "red")).toBe(true);
    });
  });

  describe("toggleFormatting", () => {
    it("should add bold formatting to plain text", () => {
      const result = toggleFormatting("Hello world", 6, 11, "bold");
      expect(result).toBe('Hello <format bold="true">world</format>');
    });

    it("should add red formatting to plain text", () => {
      const result = toggleFormatting("Hello world", 6, 11, "red");
      expect(result).toBe('Hello <format red="true">world</format>');
    });

    it("should remove bold formatting from bold text", () => {
      const text = 'Hello <format bold="true">world</format>';
      const result = toggleFormatting(text, 6, 11, "bold");
      expect(result).toBe("Hello world");
    });

    it("should remove red formatting from red text", () => {
      const text = 'Hello <format red="true">world</format>';
      const result = toggleFormatting(text, 6, 11, "red");
      expect(result).toBe("Hello world");
    });

    it("should add red to bold text (combined formatting)", () => {
      const text = 'Hello <format bold="true">world</format>';
      const result = toggleFormatting(text, 6, 11, "red");
      expect(result).toBe(
        'Hello <format bold="true" red="true">world</format>'
      );
    });

    it("should add bold to red text (combined formatting)", () => {
      const text = 'Hello <format red="true">world</format>';
      const result = toggleFormatting(text, 6, 11, "bold");
      expect(result).toBe(
        'Hello <format bold="true" red="true">world</format>'
      );
    });

    it("should remove bold from combined formatting, keeping red", () => {
      const text = 'Hello <format bold="true" red="true">world</format>';
      const result = toggleFormatting(text, 6, 11, "bold");
      expect(result).toBe('Hello <format red="true">world</format>');
    });

    it("should remove red from combined formatting, keeping bold", () => {
      const text = 'Hello <format bold="true" red="true">world</format>';
      const result = toggleFormatting(text, 6, 11, "red");
      expect(result).toBe('Hello <format bold="true">world</format>');
    });

    it("should handle empty selection by returning original text", () => {
      const text = "Hello world";
      const result = toggleFormatting(text, 5, 5, "bold");
      expect(result).toBe("Hello world");
    });

    it("should handle partial word selection", () => {
      const result = toggleFormatting("Hello world", 6, 8, "bold");
      expect(result).toBe('Hello <format bold="true">wo</format>rld');
    });

    it("should handle selection spanning multiple words", () => {
      const result = toggleFormatting("Hello beautiful world", 6, 15, "bold");
      expect(result).toBe('Hello <format bold="true">beautiful</format> world');
    });

    it("should preserve formatting outside selection", () => {
      const text = 'Hello <format bold="true">bold</format> and normal text';
      const result = toggleFormatting(text, 15, 21, "red");
      expect(result).toBe(
        'Hello <format bold="true">bold</format> and <format red="true">normal</format> text'
      );
    });

    it("should handle complex scenario: remove red from mixed formatting without affecting other segments", () => {
      // Scenario: выделяю три слова красным, выделяю одно красное слово жирным, убираю красное выделение со всех трех слов
      let text = "word1 word2 word3";

      // 1. Выделяем три слова красным (позиции 0-17)
      text = toggleFormatting(text, 0, 17, "red");
      expect(text).toBe('<format red="true">word1 word2 word3</format>');

      // 2. Выделяем одно красное слово жирным (word2, позиции 6-11)
      text = toggleFormatting(text, 6, 11, "bold");
      expect(text).toBe(
        '<format red="true">word1 </format><format bold="true" red="true">word2</format><format red="true"> word3</format>'
      );

      // 3. Убираем красное выделение со всех трех слов (позиции 0-17)
      text = toggleFormatting(text, 0, 17, "red");
      expect(text).toBe('word1 <format bold="true">word2</format> word3');

      // Проверяем, что только word2 остается жирным
      expect(getFormattingAtPosition(text, 0, 5)).toEqual({}); // word1
      expect(getFormattingAtPosition(text, 6, 11)).toEqual({ bold: true }); // word2
      expect(getFormattingAtPosition(text, 12, 17)).toEqual({}); // word3
    });
  });

  describe("cache management", () => {
    it("should clear cache", () => {
      parseFormattedText('Hello <format bold="true">world</format>');
      expect(getParseCacheSize()).toBe(1);

      clearParseCache();
      expect(getParseCacheSize()).toBe(0);
    });

    it("should track cache size correctly", () => {
      expect(getParseCacheSize()).toBe(0);

      parseFormattedText("Text 1");
      expect(getParseCacheSize()).toBe(1);

      parseFormattedText("Text 2");
      expect(getParseCacheSize()).toBe(2);

      parseFormattedText("Text 1"); // Should use cache, not increase size
      expect(getParseCacheSize()).toBe(2);
    });
  });
});
