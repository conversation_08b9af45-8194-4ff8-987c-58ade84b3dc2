import { describe, it, expect, beforeEach } from 'vitest';
import {
  hasFormatting,
  parseFormattedText,
  createFormatTag,
  getPlainText,
  getFormattingAtPosition,
  toggleFormatting,
  isTextFormatted,
  clearParseCache,
  type TextSegment,
  type FormatAttributes,
} from '../textFormatting';

describe('Text Formatting Edge Cases', () => {
  beforeEach(() => {
    clearParseCache();
  });

  describe('Malformed and edge case inputs', () => {
    it('should handle malformed format tags', () => {
      expect(hasFormatting('<format>incomplete')).toBe(false);
      expect(hasFormatting('format bold="true">missing opening</format>')).toBe(false);
      expect(hasFormatting('<format bold="true">missing closing')).toBe(false);
      expect(hasFormatting('<format bold=true>missing quotes</format>')).toBe(false);
    });

    it('should handle nested format tags gracefully', () => {
      const nestedText = '<format bold="true">Outer <format red="true">inner</format> text</format>';
      
      // Should not crash, but behavior may vary
      expect(() => parseFormattedText(nestedText)).not.toThrow();
      expect(() => getPlainText(nestedText)).not.toThrow();
    });

    it('should handle overlapping format tags', () => {
      const overlappingText = '<format bold="true">Start <format red="true">overlap</format> end</format>';
      
      expect(() => parseFormattedText(overlappingText)).not.toThrow();
      expect(() => getPlainText(overlappingText)).not.toThrow();
    });

    it('should handle format tags with extra attributes', () => {
      const extraAttrsText = '<format bold="true" red="true" unknown="value">text</format>';
      
      const segments = parseFormattedText(extraAttrsText);
      expect(segments).toHaveLength(1);
      expect(segments[0]).toEqual({
        text: 'text',
        bold: true,
        red: true,
      });
    });

    it('should handle format tags with different attribute orders', () => {
      const text1 = '<format bold="true" red="true">text</format>';
      const text2 = '<format red="true" bold="true">text</format>';
      
      const segments1 = parseFormattedText(text1);
      const segments2 = parseFormattedText(text2);
      
      expect(segments1).toEqual(segments2);
    });

    it('should handle format tags with whitespace in attributes', () => {
      const whitespaceText = '<format bold = "true" red= "true" >text</format>';
      
      // May or may not parse correctly depending on implementation
      expect(() => parseFormattedText(whitespaceText)).not.toThrow();
    });

    it('should handle very long text content', () => {
      const longText = 'A'.repeat(10000);
      const formattedLongText = `<format bold="true">${longText}</format>`;
      
      expect(() => parseFormattedText(formattedLongText)).not.toThrow();
      expect(getPlainText(formattedLongText)).toBe(longText);
    });

    it('should handle many format tags', () => {
      let manyTagsText = '';
      for (let i = 0; i < 100; i++) {
        manyTagsText += `Text ${i} <format bold="true">bold ${i}</format> `;
      }
      
      expect(() => parseFormattedText(manyTagsText)).not.toThrow();
      expect(() => getPlainText(manyTagsText)).not.toThrow();
    });

    it('should handle empty string inputs', () => {
      expect(hasFormatting('')).toBe(false);
      expect(parseFormattedText('')).toEqual([]);
      expect(getPlainText('')).toBe('');
      expect(getFormattingAtPosition('', 0, 0)).toEqual({});
      expect(toggleFormatting('', 0, 0, 'bold')).toBe('');
      expect(isTextFormatted('', 0, 0, 'bold')).toBe(false);
    });

    it('should handle null and undefined inputs gracefully', () => {
      expect(() => hasFormatting(null as any)).not.toThrow();
      expect(() => hasFormatting(undefined as any)).not.toThrow();
      expect(() => parseFormattedText(null as any)).not.toThrow();
      expect(() => parseFormattedText(undefined as any)).not.toThrow();
      expect(() => getPlainText(null as any)).not.toThrow();
      expect(() => getPlainText(undefined as any)).not.toThrow();
    });

    it('should handle special characters in text content', () => {
      const specialChars = 'Special: @#$%^&*()[]{}|\\:";\'<>?,./`~';
      const formattedSpecial = `<format bold="true">${specialChars}</format>`;
      
      const segments = parseFormattedText(formattedSpecial);
      expect(segments).toHaveLength(1);
      expect(segments[0].text).toBe(specialChars);
      expect(getPlainText(formattedSpecial)).toBe(specialChars);
    });

    it('should handle Unicode characters', () => {
      const unicodeText = 'Unicode: 🎉 Привет мир こんにちは 你好';
      const formattedUnicode = `<format bold="true">${unicodeText}</format>`;
      
      const segments = parseFormattedText(formattedUnicode);
      expect(segments).toHaveLength(1);
      expect(segments[0].text).toBe(unicodeText);
      expect(getPlainText(formattedUnicode)).toBe(unicodeText);
    });

    it('should handle newlines and tabs in text content', () => {
      const textWithNewlines = 'Line 1\nLine 2\tTabbed';
      const formattedWithNewlines = `<format bold="true">${textWithNewlines}</format>`;
      
      const segments = parseFormattedText(formattedWithNewlines);
      expect(segments).toHaveLength(1);
      expect(segments[0].text).toBe(textWithNewlines);
      expect(getPlainText(formattedWithNewlines)).toBe(textWithNewlines);
    });
  });

  describe('Position handling edge cases', () => {
    it('should handle negative positions', () => {
      const text = 'Hello <format bold="true">world</format>';
      
      expect(getFormattingAtPosition(text, -5, -1)).toEqual({});
      expect(toggleFormatting(text, -5, -1, 'bold')).toBe(text);
      expect(isTextFormatted(text, -5, -1, 'bold')).toBe(false);
    });

    it('should handle positions beyond text length', () => {
      const text = 'Hello <format bold="true">world</format>';
      const plainLength = getPlainText(text).length;
      
      expect(getFormattingAtPosition(text, plainLength + 5, plainLength + 10)).toEqual({});
      expect(toggleFormatting(text, plainLength + 5, plainLength + 10, 'bold')).toBe(text);
      expect(isTextFormatted(text, plainLength + 5, plainLength + 10, 'bold')).toBe(false);
    });

    it('should handle reversed positions (end < start)', () => {
      const text = 'Hello <format bold="true">world</format>';
      
      expect(getFormattingAtPosition(text, 10, 5)).toEqual({});
      expect(toggleFormatting(text, 10, 5, 'bold')).toBe(text);
      expect(isTextFormatted(text, 10, 5, 'bold')).toBe(false);
    });

    it('should handle zero-length selections at various positions', () => {
      const text = 'Hello <format bold="true">world</format>';
      
      for (let i = 0; i <= getPlainText(text).length; i++) {
        expect(getFormattingAtPosition(text, i, i)).toEqual({});
        expect(toggleFormatting(text, i, i, 'bold')).toBe(text);
        expect(isTextFormatted(text, i, i, 'bold')).toBe(false);
      }
    });

    it('should handle positions at format tag boundaries', () => {
      const text = 'Hello <format bold="true">world</format> end';
      // Plain text: "Hello world end"
      // Positions: 0-5: "Hello", 5-6: " ", 6-11: "world", 11-12: " ", 12-15: "end"
      
      // Position just before formatted text
      expect(getFormattingAtPosition(text, 5, 6)).toEqual({});
      
      // Position at start of formatted text
      expect(getFormattingAtPosition(text, 6, 7)).toEqual({ bold: true });
      
      // Position at end of formatted text
      expect(getFormattingAtPosition(text, 10, 11)).toEqual({ bold: true });
      
      // Position just after formatted text
      expect(getFormattingAtPosition(text, 11, 12)).toEqual({});
    });
  });

  describe('Complex formatting scenarios', () => {
    it('should handle alternating formatting', () => {
      const text = '<format bold="true">A</format>B<format red="true">C</format>D<format bold="true" red="true">E</format>';
      
      const segments = parseFormattedText(text);
      expect(segments).toHaveLength(5);
      expect(segments[0]).toEqual({ text: 'A', bold: true });
      expect(segments[1]).toEqual({ text: 'B' });
      expect(segments[2]).toEqual({ text: 'C', red: true });
      expect(segments[3]).toEqual({ text: 'D' });
      expect(segments[4]).toEqual({ text: 'E', bold: true, red: true });
    });

    it('should handle formatting that spans entire text', () => {
      const text = '<format bold="true">Entire text is bold</format>';
      
      const segments = parseFormattedText(text);
      expect(segments).toHaveLength(1);
      expect(segments[0]).toEqual({ text: 'Entire text is bold', bold: true });
      
      expect(getPlainText(text)).toBe('Entire text is bold');
    });

    it('should handle multiple consecutive format tags', () => {
      const text = '<format bold="true">Bold1</format><format bold="true">Bold2</format><format red="true">Red</format>';
      
      const segments = parseFormattedText(text);
      expect(segments).toHaveLength(3);
      expect(segments[0]).toEqual({ text: 'Bold1', bold: true });
      expect(segments[1]).toEqual({ text: 'Bold2', bold: true });
      expect(segments[2]).toEqual({ text: 'Red', red: true });
    });

    it('should handle format tags with identical content', () => {
      const text = '<format bold="true">Same</format> text <format bold="true">Same</format>';
      
      const segments = parseFormattedText(text);
      expect(segments).toHaveLength(3);
      expect(segments[0]).toEqual({ text: 'Same', bold: true });
      expect(segments[1]).toEqual({ text: ' text ' });
      expect(segments[2]).toEqual({ text: 'Same', bold: true });
    });
  });

  describe('Performance edge cases', () => {
    it('should handle cache overflow gracefully', () => {
      // Fill cache beyond limit
      for (let i = 0; i < 150; i++) {
        parseFormattedText(`Text ${i} <format bold="true">formatted</format>`);
      }
      
      // Should still work and not exceed cache limit
      expect(() => parseFormattedText('New text <format red="true">red</format>')).not.toThrow();
    });

    it('should handle repeated parsing of same text efficiently', () => {
      const text = 'Repeated <format bold="true">text</format> for caching';
      
      // First parse
      const start1 = performance.now();
      const result1 = parseFormattedText(text);
      const time1 = performance.now() - start1;
      
      // Second parse (should use cache)
      const start2 = performance.now();
      const result2 = parseFormattedText(text);
      const time2 = performance.now() - start2;
      
      expect(result1).toEqual(result2);
      // Second parse should be faster (cached), but this is hard to test reliably
      expect(time2).toBeLessThanOrEqual(time1 + 1); // Allow some margin for test variability
    });
  });

  describe('Attribute edge cases', () => {
    it('should handle false attribute values', () => {
      const attributes: FormatAttributes = { bold: false, red: false };
      expect(createFormatTag('text', attributes)).toBe('text');
    });

    it('should handle mixed true/false attributes', () => {
      const attributes: FormatAttributes = { bold: true, red: false };
      expect(createFormatTag('text', attributes)).toBe('<format bold="true">text</format>');
    });

    it('should handle undefined attributes', () => {
      const attributes: FormatAttributes = { bold: undefined, red: true };
      expect(createFormatTag('text', attributes)).toBe('<format red="true">text</format>');
    });

    it('should handle empty attributes object', () => {
      expect(createFormatTag('text', {})).toBe('text');
    });
  });
});
