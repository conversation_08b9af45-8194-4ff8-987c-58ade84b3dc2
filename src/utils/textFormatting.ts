/**
 * Represents a segment of text with optional formatting attributes
 */
export interface TextSegment {
  /** The text content */
  text: string;
  /** Whether the text should be bold */
  bold?: boolean;
  /** Whether the text should be red */
  red?: boolean;
}

/**
 * Formatting attributes that can be applied to text
 */
export interface FormatAttributes {
  /** Bold formatting */
  bold?: boolean;
  /** Red color formatting */
  red?: boolean;
}

/**
 * Available formatting types
 */
export type FormatType = "bold" | "red";

/**
 * Regular expression for matching format tags
 */
const FORMAT_TAG_REGEX = /<format([^>]*)>(.*?)<\/format>/g;

/**
 * Cache for parsed text segments to improve performance
 */
const parseCache = new Map<string, TextSegment[]>();

/**
 * Maximum cache size to prevent memory leaks
 */
const MAX_CACHE_SIZE = 100;

/**
 * Checks if text contains any formatting tags
 * @param text - The text to check
 * @returns True if text contains formatting, false otherwise
 * @example
 * hasFormatting('Hello world') // false
 * hasFormatting('Hello <format bold="true">world</format>') // true
 */
export const hasFormatting = (text: string): boolean => {
  return /<format[^>]*>.*?<\/format>/.test(text);
};

/**
 * Parses attributes from a format tag attribute string
 * @param attributeString - The attribute string from the format tag
 * @returns Parsed formatting attributes
 */
const parseAttributes = (attributeString: string): FormatAttributes => {
  const attributes: FormatAttributes = {};

  if (attributeString.includes('bold="true"')) {
    attributes.bold = true;
  }
  if (attributeString.includes('red="true"')) {
    attributes.red = true;
  }

  return attributes;
};

/**
 * Parses formatted text into segments with their formatting attributes
 * Uses caching for improved performance on repeated parsing of the same text
 * @param text - The formatted text to parse
 * @returns Array of text segments with formatting information
 * @example
 * parseFormattedText('Hello <format bold="true">world</format>')
 * // Returns: [{ text: 'Hello ' }, { text: 'world', bold: true }]
 */
export const parseFormattedText = (text: string): TextSegment[] => {
  if (!text) return [];

  // Check cache first
  if (parseCache.has(text)) {
    return parseCache.get(text)!;
  }

  const segments: TextSegment[] = [];
  let currentIndex = 0;

  // Reset regex state
  FORMAT_TAG_REGEX.lastIndex = 0;

  let match;
  while ((match = FORMAT_TAG_REGEX.exec(text)) !== null) {
    // Add plain text before the tag
    if (currentIndex < match.index) {
      const plainText = text.slice(currentIndex, match.index);
      if (plainText) {
        segments.push({ text: plainText });
      }
    }

    // Parse attributes and create segment
    const attributeString = match[1];
    const content = match[2];
    const attributes = parseAttributes(attributeString);

    segments.push({
      text: content,
      ...attributes,
    });

    currentIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (currentIndex < text.length) {
    const remainingText = text.slice(currentIndex);
    if (remainingText) {
      segments.push({ text: remainingText });
    }
  }

  // If no formatting found, return the whole text as plain
  if (segments.length === 0 && text) {
    segments.push({ text });
  }

  // Cache the result (with size limit)
  if (parseCache.size >= MAX_CACHE_SIZE) {
    // Remove oldest entry (first key)
    const firstKey = parseCache.keys().next().value;
    if (firstKey) {
      parseCache.delete(firstKey);
    }
  }
  parseCache.set(text, segments);

  return segments;
};

/**
 * Creates a format tag with the specified attributes
 * @param text - The text to wrap in the format tag
 * @param attributes - The formatting attributes to apply
 * @returns The text wrapped in a format tag, or plain text if no attributes
 * @example
 * createFormatTag('hello', { bold: true }) // '<format bold="true">hello</format>'
 * createFormatTag('hello', { bold: true, red: true }) // '<format bold="true" red="true">hello</format>'
 */
export const createFormatTag = (
  text: string,
  attributes: FormatAttributes
): string => {
  const attrs: string[] = [];

  if (attributes.bold) attrs.push('bold="true"');
  if (attributes.red) attrs.push('red="true"');

  if (attrs.length === 0) return text;

  return `<format ${attrs.join(" ")}>${text}</format>`;
};

/**
 * Extracts plain text from formatted text by removing all format tags
 * @param formattedText - The formatted text
 * @returns Plain text without any formatting tags
 * @example
 * getPlainText('Hello <format bold="true">world</format>') // 'Hello world'
 */
export const getPlainText = (formattedText: string): string => {
  return formattedText.replace(/<format[^>]*>(.*?)<\/format>/g, "$1");
};

/**
 * Gets the formatting attributes for text at a specific position range
 * @param formattedText - The formatted text to analyze
 * @param plainStartPos - Start position in plain text coordinates
 * @param plainEndPos - End position in plain text coordinates
 * @returns The formatting attributes present in the specified range
 * @example
 * getFormattingAtPosition('Hello <format bold="true">world</format>', 6, 11)
 * // Returns: { bold: true }
 */
export const getFormattingAtPosition = (
  formattedText: string,
  plainStartPos: number,
  plainEndPos: number
): FormatAttributes => {
  // Handle empty selection - return empty attributes
  if (plainStartPos === plainEndPos) {
    return {};
  }

  const segments = parseFormattedText(formattedText);
  let currentPlainPos = 0;

  const attributes: FormatAttributes = {};

  for (const segment of segments) {
    const segmentStart = currentPlainPos;
    const segmentEnd = currentPlainPos + segment.text.length;

    // Check if the selection overlaps with this segment
    const hasOverlap =
      (plainStartPos >= segmentStart && plainStartPos < segmentEnd) ||
      (plainEndPos > segmentStart && plainEndPos <= segmentEnd) ||
      (plainStartPos <= segmentStart && plainEndPos >= segmentEnd);

    if (hasOverlap) {
      if (segment.bold) attributes.bold = true;
      if (segment.red) attributes.red = true;
    }

    currentPlainPos += segment.text.length;
  }

  return attributes;
};

/**
 * Toggles formatting for a specific text range while preserving other formatting
 * @param formattedText - The formatted text to modify
 * @param plainStartPos - Start position in plain text coordinates
 * @param plainEndPos - End position in plain text coordinates
 * @param formatType - The type of formatting to toggle
 * @returns The modified formatted text
 * @example
 * // Toggle bold on "world" in "Hello world"
 * toggleFormatting('Hello world', 6, 11, 'bold')
 * // Returns: 'Hello <format bold="true">world</format>'
 *
 * // Toggle red on already bold text
 * toggleFormatting('Hello <format bold="true">world</format>', 6, 11, 'red')
 * // Returns: 'Hello <format bold="true" red="true">world</format>'
 */
export const toggleFormatting = (
  formattedText: string,
  plainStartPos: number,
  plainEndPos: number,
  formatType: FormatType
): string => {
  // Early return for empty selection
  if (plainStartPos === plainEndPos) {
    return formattedText;
  }

  const segments = parseFormattedText(formattedText);
  let result = "";
  let currentPos = 0;

  for (const segment of segments) {
    const segmentStart = currentPos;
    const segmentEnd = currentPos + segment.text.length;

    if (segmentEnd <= plainStartPos) {
      // Segment is completely before the selection - keep as is
      if (segment.bold || segment.red) {
        result += createFormatTag(segment.text, {
          bold: segment.bold,
          red: segment.red,
        });
      } else {
        result += segment.text;
      }
    } else if (segmentStart >= plainEndPos) {
      // Segment is completely after the selection - keep as is
      if (segment.bold || segment.red) {
        result += createFormatTag(segment.text, {
          bold: segment.bold,
          red: segment.red,
        });
      } else {
        result += segment.text;
      }
    } else {
      // Segment overlaps with the selection
      // Handle the part before the selection
      if (segmentStart < plainStartPos) {
        const beforeText = segment.text.slice(0, plainStartPos - segmentStart);
        if (segment.bold || segment.red) {
          result += createFormatTag(beforeText, {
            bold: segment.bold,
            red: segment.red,
          });
        } else {
          result += beforeText;
        }
      }

      // Handle the overlapping part - toggle the specified format
      const overlapStart = Math.max(segmentStart, plainStartPos);
      const overlapEnd = Math.min(segmentEnd, plainEndPos);
      const overlapText = segment.text.slice(
        overlapStart - segmentStart,
        overlapEnd - segmentStart
      );

      if (overlapText) {
        const newAttributes: FormatAttributes = {
          bold: segment.bold,
          red: segment.red,
        };
        // Toggle the specified format type
        newAttributes[formatType] = !segment[formatType];

        if (newAttributes.bold || newAttributes.red) {
          result += createFormatTag(overlapText, newAttributes);
        } else {
          result += overlapText;
        }
      }

      // Handle the part after the selection
      if (segmentEnd > plainEndPos) {
        const afterText = segment.text.slice(plainEndPos - segmentStart);
        if (segment.bold || segment.red) {
          result += createFormatTag(afterText, {
            bold: segment.bold,
            red: segment.red,
          });
        } else {
          result += afterText;
        }
      }
    }

    currentPos += segment.text.length;
  }

  return result;
};

/**
 * Checks if text at a specific position has a particular formatting type
 * @param formattedText - The formatted text to check
 * @param plainStartPos - Start position in plain text coordinates
 * @param plainEndPos - End position in plain text coordinates
 * @param formatType - The formatting type to check for
 * @returns True if the text has the specified formatting, false otherwise
 * @example
 * isTextFormatted('Hello <format bold="true">world</format>', 6, 11, 'bold') // true
 * isTextFormatted('Hello <format bold="true">world</format>', 6, 11, 'red') // false
 */
export const isTextFormatted = (
  formattedText: string,
  plainStartPos: number,
  plainEndPos: number,
  formatType: FormatType
): boolean => {
  const attributes = getFormattingAtPosition(
    formattedText,
    plainStartPos,
    plainEndPos
  );
  return !!attributes[formatType];
};

/**
 * Clears the parsing cache to free up memory
 * Useful for long-running applications or when memory usage is a concern
 * @example
 * clearParseCache(); // Clears all cached parsing results
 */
export const clearParseCache = (): void => {
  parseCache.clear();
};

/**
 * Gets the current size of the parsing cache
 * @returns The number of cached parsing results
 * @example
 * getParseCacheSize(); // Returns number like 15
 */
export const getParseCacheSize = (): number => {
  return parseCache.size;
};
