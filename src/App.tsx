import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useEffect } from "react";
import { AuthProvider } from "./lib/auth";
import { LoginPage } from "./components/LoginPage";
import { ProtectedRoute } from "./components/ProtectedRoute";
import UploadTest from "./components/UploadTest";
import { initializeColumnSettings } from "./db/init";
import { ProductList } from "./components/products/ProductList";

const queryClient = new QueryClient();

function AppInitializer() {
  useEffect(() => {
    // Инициализируем базу данных при первой загрузке приложения
    initializeColumnSettings().catch(console.error);
  }, []);

  return null;
}

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <AuthProvider>
          <AppInitializer />
          <Toaster />
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route path="/upload-test" element={<UploadTest />} />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <ProductList />
                </ProtectedRoute>
              }
            />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </AuthProvider>
      </Router>
    </QueryClientProvider>
  );
}
