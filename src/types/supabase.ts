export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      products: {
        Row: {
          id: string;
          name: string;
          weight: number;
          price: number;
          validity_period: number;
          is_hidden: boolean;
          custom_fields: J<PERSON>;
          created_at: string;
          updated_at: string;
          group_id: string | null;
          order_in_group: number;
        };
        Insert: {
          id?: string;
          name: string;
          weight: number;
          price: number;
          validity_period: number;
          is_hidden?: boolean;
          custom_fields?: Json;
          created_at?: string;
          updated_at?: string;
          group_id?: string | null;
          order_in_group: number;
        };
        Update: {
          id?: string;
          name?: string;
          weight?: number;
          price?: number;
          validity_period?: number;
          is_hidden?: boolean;
          custom_fields?: Json;
          created_at?: string;
          updated_at?: string;
          group_id?: string | null;
          order_in_group?: number;
        };
      };
      product_groups: {
        Row: {
          id: string;
          name: string;
          order_index: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          order_index: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          order_index?: number;
          created_at?: string;
        };
      };
      product_images: {
        Row: {
          id: string;
          product_id: string;
          yandex_disk_path: string;
          public_url: string;
          sort_order: number;
          is_main: boolean;
          created_at: string;
        };
        Insert: {
          id?: string;
          product_id: string;
          yandex_disk_path: string;
          public_url: string;
          sort_order: number;
          is_main?: boolean;
          created_at?: string;
        };
        Update: {
          id?: string;
          product_id?: string;
          yandex_disk_path?: string;
          public_url?: string;
          sort_order?: number;
          is_main?: boolean;
          created_at?: string;
        };
      };
      column_settings: {
        Row: {
          id: string;
          name: string;
          display_name: string;
          order_index: number;
          is_visible: boolean;
          data_type: string;
          prefix: string | null;
          postfix: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          display_name: string;
          order_index: number;
          is_visible?: boolean;
          data_type: string;
          prefix?: string | null;
          postfix?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          display_name?: string;
          order_index?: number;
          is_visible?: boolean;
          data_type?: string;
          prefix?: string | null;
          postfix?: string | null;
          created_at?: string;
        };
      };
      version_history: {
        Row: {
          id: string;
          created_at: string;
          user_id: string;
          comment: string | null;
          snapshot_data: {
            products: Array<Database["public"]["Tables"]["products"]["Row"]>;
            product_groups: Array<
              Database["public"]["Tables"]["product_groups"]["Row"]
            >;
            column_settings: Array<
              Database["public"]["Tables"]["column_settings"]["Row"]
            >;
            product_images: Array<
              Database["public"]["Tables"]["product_images"]["Row"]
            >;
          };
        };
        Insert: {
          id?: string;
          created_at?: string;
          user_id: string;
          comment?: string | null;
          snapshot_data: {
            products: Array<Database["public"]["Tables"]["products"]["Row"]>;
            product_groups: Array<
              Database["public"]["Tables"]["product_groups"]["Row"]
            >;
            column_settings: Array<
              Database["public"]["Tables"]["column_settings"]["Row"]
            >;
            product_images: Array<
              Database["public"]["Tables"]["product_images"]["Row"]
            >;
          };
        };
        Update: {
          id?: string;
          created_at?: string;
          user_id?: string;
          comment?: string | null;
          snapshot_data?: {
            products: Array<Database["public"]["Tables"]["products"]["Row"]>;
            product_groups: Array<
              Database["public"]["Tables"]["product_groups"]["Row"]
            >;
            column_settings: Array<
              Database["public"]["Tables"]["column_settings"]["Row"]
            >;
            product_images: Array<
              Database["public"]["Tables"]["product_images"]["Row"]
            >;
          };
        };
      };
    };
  };
}
