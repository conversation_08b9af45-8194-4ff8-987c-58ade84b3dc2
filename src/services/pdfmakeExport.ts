import pdfMake from "pdfmake/build/pdfmake";
import "pdfmake/build/vfs_fonts";
import type { Database } from "../types/supabase";
import { TDocumentDefinitions, Content } from "pdfmake/interfaces";
import { PDFExportSettings } from "../components/products/export/pdfExportUtils";
import { GroupHeaderRow } from "../components/products/grid/GroupHeaderRenderer";
import { parseFormattedText, hasFormatting } from "../utils/textFormatting";
import type { TextSegment } from "../utils/textFormatting";

const VIRTUAL_ORDER_COLUMN_HEADER = "ЗАКАЗ";

// Добавляем поддержку кириллицы
pdfMake.fonts = {
  Roboto: {
    normal: "Roboto-Regular.ttf",
    bold: "Roboto-Medium.ttf",
    italics: "Roboto-Italic.ttf",
    bolditalics: "Roboto-MediumItalic.ttf",
  },
};

// Определяем типы локально или импортируем, если они вынесены
type Product = Database["public"]["Tables"]["products"]["Row"] & {
  product_images?: Array<Database["public"]["Tables"]["product_images"]["Row"]>;
};
type RowData = Product | GroupHeaderRow;

// Вспомогательная функция type guard для GroupHeaderRow
const isGroupHeaderRow = (item: RowData): item is GroupHeaderRow => {
  return "isGroupHeader" in item && item.isGroupHeader === true;
};

// Функция для создания форматированного контента для PDF
const createFormattedContent = (text: string, baseStyle: string): Content => {
  if (!hasFormatting(text)) {
    return { text, style: baseStyle };
  }

  const segments = parseFormattedText(text);

  const formattedText = segments.map((segment: TextSegment) => {
    const textPart: { text: string; bold?: boolean; color?: string } = {
      text: segment.text,
    };

    if (segment.bold) {
      textPart.bold = true;
    }

    if (segment.red) {
      textPart.color = "#dc2626";
    }

    return textPart;
  });

  return {
    text: formattedText,
    style: baseStyle,
  };
};

// --- ИЗМЕНЕНИЕ: Обновляем formatValue для использования prefix и postfix ---
const formatValue = (
  product: Product,
  field: string,
  prefix?: string | null,
  postfix?: string | null
): string => {
  // --- Проверяем наличие product перед доступом к полям ---
  if (!product) return "";

  let valueToFormat: unknown;
  let isPriceField = false;

  if (field.startsWith("custom_fields.")) {
    const fieldName = field.split(".")[1];
    const customFields = product.custom_fields as Record<string, unknown>;
    valueToFormat = customFields?.[fieldName];
  } else {
    valueToFormat = product[field as keyof Product];
  }

  if (
    field === "price" ||
    (field.startsWith("custom_fields.") && postfix === "₽")
  ) {
    isPriceField = true;
  }

  let formattedValue = "";

  if (valueToFormat === null || valueToFormat === undefined) {
    formattedValue = "";
  } else if (isPriceField) {
    const priceNum = Number(valueToFormat);
    formattedValue = !isNaN(priceNum)
      ? priceNum.toFixed(2)
      : String(valueToFormat);
  } else if (field === "is_hidden") {
    formattedValue = valueToFormat ? "Скрыт" : "Активен";
  } else if (field === "created_at" && typeof valueToFormat === "string") {
    try {
      formattedValue = new Date(valueToFormat).toLocaleDateString("ru-RU");
    } catch {
      formattedValue = String(valueToFormat);
    }
  } else if (field === "product_images") {
    formattedValue =
      product.product_images && product.product_images.length > 0
        ? "Есть"
        : "Нет";
  } else {
    formattedValue = String(valueToFormat);
  }

  if (prefix && prefix.trim() !== "" && formattedValue) {
    formattedValue = `${prefix.trim()} ${formattedValue}`;
  }
  if (postfix && postfix.trim() !== "" && formattedValue) {
    formattedValue = `${formattedValue} ${postfix.trim()}`;
  }

  return formattedValue.trim(); // Убираем лишние пробелы, если значения нет, но есть префикс/постфикс
};

// Функция для создания URL продукта (остается без изменений)
const getProductUrl = (product: Product): string => {
  // Если у продукта есть изображения, используем URL первого изображения
  if (product.product_images && product.product_images.length > 0) {
    const mainImage =
      product.product_images.find((img) => img.is_main) ||
      product.product_images[0];
    if (mainImage && mainImage.public_url) {
      return mainImage.public_url;
    }
  }
  // Если у продукта нет изображений, возвращаем заглушку
  return `https://example.com/products/${product.id}`;
};

// --- ИЗМЕНЕНИЕ: Принимаем RowData[] и PDFExportSettings ---
export const exportToPDFMake = async (
  data: RowData[],
  settings: PDFExportSettings,
  openInNewWindow = false
): Promise<void> => {
  try {
    // Выбираем только выбранные колонки
    const selectedColumns = settings.columns.filter((col) => col.selected);
    const numberOfColumnsForPDF = selectedColumns.length + 1;

    // Создаем заголовки таблицы
    const tableHeaders = [
      ...selectedColumns.map((col) => {
        const isNumberColumn =
          col.dataType === "number" ||
          ["price", "weight", "validity_period"].includes(col.field);

        return {
          text: col.headerName,
          style: "tableHeaderCell",
          alignment: isNumberColumn ? "right" : "left",
        };
      }),
      {
        text: VIRTUAL_ORDER_COLUMN_HEADER,
        style: "tableHeaderCell",
        alignment: "center",
      },
    ];

    // Функция для расчета ширины текста в PDF с учетом особенностей шрифта Roboto
    const calculateTextWidth = (text: string): number => {
      if (!text) return 0;

      let charCount = 0;

      for (const char of text) {
        if (/[WMwmШЩЖЫЮ@]/.test(char)) {
          charCount += 1.3; // Очень широкие символы
        } else if (
          /[ABCDEFGHIJKLNOPQRSTUVXYZАБВГДЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ#%&0-9]/.test(
            char
          )
        ) {
          charCount += 1.1; // Широкие символы
        } else if (/[iltjf.,;:!|'"]/.test(char)) {
          charCount += 0.7; // Узкие символы
        } else {
          charCount += 1; // Обычные символы
        }
      }

      return Math.ceil(charCount);
    };

    // --- ИЗМЕНЕНИЕ: Подготавливаем данные таблицы с учетом групп ---
    const prepareTableData = () => {
      // Используем any[] для совместимости с pdfMake - который не полностью типизирован
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const tableRows: any[][] = []; // Массив строк таблицы
      const nonEmptyGroupIds = new Set<string>();

      // 1. Определяем, какие группы содержат продукты, если группировка включена
      if (settings.includeGroups) {
        data.forEach((item) => {
          // Проверяем, что это продукт (не заголовок группы) и у него есть group_id
          if (!isGroupHeaderRow(item) && item.group_id) {
            nonEmptyGroupIds.add(item.group_id);
          }
        });
      }

      // 2. Формируем строки таблицы
      data.forEach((item) => {
        if (isGroupHeaderRow(item)) {
          // Если текущий элемент - это заголовок группы
          if (settings.includeGroups) {
            let shouldAddGroupHeader = false;
            if (item.groupId === "ungrouped") {
              // Заголовок "Без группы" добавляется всегда, если он присутствует в `data`.
              // Логика в `useProducts` гарантирует, что он там только если есть продукты без группы.
              shouldAddGroupHeader = true;
            } else if (nonEmptyGroupIds.has(item.groupId)) {
              // Для остальных (именованных) групп - добавляем заголовок, только если группа не пустая.
              shouldAddGroupHeader = true;
            }

            if (shouldAddGroupHeader) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              const groupHeaderCell: any = {
                text: item.groupName,
                colSpan: numberOfColumnsForPDF,
                style: "groupHeaderStyle",
                alignment: "left",
              };
              // Создаем строку заголовка группы
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              const groupRow: any[] = [groupHeaderCell];
              // Добавляем пустые ячейки для остальных колонок
              for (let i = 1; i < numberOfColumnsForPDF; i++) {
                groupRow.push({ text: "" });
              }
              tableRows.push(groupRow);
            }
          }
          // Если группировка отключена (settings.includeGroups === false), заголовки групп не добавляются.
        } else {
          // Если текущий элемент - это продукт
          const product = item as Product; // Мы знаем, что это продукт, так как isGroupHeaderRow(item) === false
          let shouldAddProduct = false;

          if (!settings.includeGroups) {
            // Если группировка отключена, добавляем все продукты
            shouldAddProduct = true;
          } else {
            // Если группировка включена
            if (product.group_id === null) {
              // Продукт без группы - добавляем (заголовок "Без группы" уже обработан выше)
              shouldAddProduct = true;
            } else if (nonEmptyGroupIds.has(product.group_id)) {
              // Продукт принадлежит непустой группе - добавляем
              shouldAddProduct = true;
            }
            // Если продукт принадлежит пустой группе (которой нет в nonEmptyGroupIds), он не добавляется.
          }

          if (shouldAddProduct) {
            const rowCells = selectedColumns.map((col) => {
              const isNumberCol =
                col.dataType === "number" ||
                ["price", "weight", "validity_period"].includes(col.field);
              const cellStyle = isNumberCol
                ? "tableCellNumber"
                : "tableCellText";

              const formattedValue = formatValue(
                product,
                col.field,
                col.prefix,
                col.postfix
              );

              let cellContent: Content;

              if (col.field === "name") {
                if (
                  product.product_images &&
                  product.product_images.length > 0
                ) {
                  cellContent = createFormattedContent(
                    formattedValue,
                    "productNameLink"
                  );
                  if (
                    typeof cellContent === "object" &&
                    "text" in cellContent
                  ) {
                    (cellContent as { text: unknown; link?: string }).link =
                      getProductUrl(product);
                  }
                } else {
                  cellContent = createFormattedContent(
                    formattedValue,
                    "tableCellText"
                  );
                }
              } else if (!isNumberCol) {
                // Для текстовых полей (включая кастомные) применяем форматирование
                cellContent = createFormattedContent(formattedValue, cellStyle);
              } else {
                // Для числовых полей форматирование не применяется
                cellContent = {
                  text: formattedValue,
                  style: cellStyle,
                };
              }

              return cellContent;
            });
            rowCells.push({
              text: "",
              style: "tableCellText",
              alignment: "center",
            });
            tableRows.push(rowCells);
          }
        }
      });

      return tableRows;
    };

    // Функция для расчета динамических ширин колонок на основе содержимого
    const calculateDynamicColumnWidths = () => {
      const columnWidths: (string | number)[] = [];
      const tableRows = prepareTableData();

      // Рассчитываем максимальную ширину для каждой колонки
      const maxWidths: number[] = [];

      // Инициализируем массив максимальных ширин
      for (let colIdx = 0; colIdx < selectedColumns.length + 1; colIdx++) {
        maxWidths[colIdx] = 0;
      }

      // Анализируем заголовки таблицы
      tableHeaders.forEach((header, colIdx) => {
        const headerText =
          typeof header === "object" && "text" in header
            ? String(header.text)
            : String(header);
        maxWidths[colIdx] = Math.max(
          maxWidths[colIdx],
          calculateTextWidth(headerText)
        );
      });

      // Анализируем данные в строках таблицы
      tableRows.forEach((row) => {
        if (Array.isArray(row)) {
          row.forEach((cell, colIdx) => {
            if (colIdx < maxWidths.length) {
              let cellText = "";

              if (cell && typeof cell === "object") {
                if ("text" in cell) {
                  if (Array.isArray(cell.text)) {
                    // Форматированный текст - извлекаем только текст
                    cellText = cell.text
                      .map((segment: { text: string } | string) =>
                        typeof segment === "object" && "text" in segment
                          ? segment.text
                          : String(segment)
                      )
                      .join("");
                  } else {
                    cellText = String(cell.text);
                  }
                }
              } else {
                cellText = String(cell || "");
              }

              // Удаляем HTML-теги для более точного расчета
              cellText = cellText.replace(/<[^>]*>/g, "");

              maxWidths[colIdx] = Math.max(
                maxWidths[colIdx],
                calculateTextWidth(cellText)
              );
            }
          });
        }
      });

      // Добавляем небольшие отступы к рассчитанным ширинам
      const adjustedWidths = maxWidths.map((width) => width + 2);

      // Рассчитываем общую ширину
      const totalWidth = adjustedWidths.reduce((sum, width) => sum + width, 0);

      // Улучшенный алгоритм распределения ширин колонок
      const basePercentages = adjustedWidths.map(
        (width) => (width / totalWidth) * 100
      );

      // Определяем минимальные и максимальные ограничения для каждой колонки
      const constraints = adjustedWidths.map((_, index) => {
        if (index < selectedColumns.length) {
          const col = selectedColumns[index];
          if (col.field === "name") {
            return { min: 30, max: 45 }; // Название продукта
          } else if (col.field === "price") {
            return { min: 12, max: 18 }; // Цена
          } else if (col.field === "weight") {
            return { min: 10, max: 16 }; // Вес
          } else if (col.field === "validity_period") {
            return { min: 12, max: 20 }; // Срок годности
          } else {
            return { min: 10, max: 22 }; // Остальные поля
          }
        } else {
          return { min: 8, max: 12 }; // Колонка "ЗАКАЗ"
        }
      });

      // Применяем ограничения и перераспределяем пространство
      let adjustedPercentages = basePercentages.map((percentage, index) => {
        const constraint = constraints[index];
        return Math.max(constraint.min, Math.min(constraint.max, percentage));
      });

      // Нормализуем, чтобы сумма была 100%
      let totalAdjusted = adjustedPercentages.reduce((sum, p) => sum + p, 0);

      if (totalAdjusted !== 100) {
        // Если общая сумма не 100%, пропорционально корректируем
        const factor = 100 / totalAdjusted;
        adjustedPercentages = adjustedPercentages.map((p) => p * factor);

        // Повторно применяем ограничения после нормализации
        adjustedPercentages = adjustedPercentages.map((percentage, index) => {
          const constraint = constraints[index];
          return Math.max(constraint.min, Math.min(constraint.max, percentage));
        });

        // Финальная нормализация
        totalAdjusted = adjustedPercentages.reduce((sum, p) => sum + p, 0);
        if (totalAdjusted !== 100) {
          const finalFactor = 100 / totalAdjusted;
          adjustedPercentages = adjustedPercentages.map((p) => p * finalFactor);
        }
      }

      // Добавляем небольшое сглаживание для более равномерного распределения
      const avgPercentage = 100 / adjustedPercentages.length;
      adjustedPercentages = adjustedPercentages.map((percentage, index) => {
        const constraint = constraints[index];
        const smoothed = percentage * 0.8 + avgPercentage * 0.2;
        return Math.max(constraint.min, Math.min(constraint.max, smoothed));
      });

      // Финальная нормализация после сглаживания
      const finalTotal = adjustedPercentages.reduce((sum, p) => sum + p, 0);
      if (finalTotal !== 100) {
        const finalFactor = 100 / finalTotal;
        adjustedPercentages = adjustedPercentages.map((p) => p * finalFactor);
      }

      adjustedPercentages.forEach((percentage) => {
        columnWidths.push(`${percentage.toFixed(1)}%`);
      });

      return columnWidths;
    };

    // --- КОНЕЦ ИЗМЕНЕНИЯ ---

    // Создаем пустой элемент для условных блоков
    const emptyElement: Content = { text: "" };

    // Функция для создания и скачивания PDF
    const generatePDF = () => {
      // Получаем данные таблицы
      const tableRows = prepareTableData();

      // Получаем динамические ширины колонок
      const dynamicColumnWidths = calculateDynamicColumnWidths();

      // Создаем содержимое документа
      const docDefinition: TDocumentDefinitions = {
        pageOrientation: "portrait", // Изменяем на портретную ориентацию
        pageSize: "A4",
        pageMargins: [8, 15, 8, 15], // Минимальные отступы для максимального использования пространства
        defaultStyle: {
          font: "Roboto",
          fontSize: 8, // Уменьшаем размер шрифта для портретной ориентации
          color: "#333333", // Стандартный темный цвет текста
        },
        content: [
          // Заголовок документа и дата на одной линии
          {
            columns: [
              {
                text: `Коммерческое предложение ${settings.header.companyName}`,
                style: "mainHeader",
                width: "*", // Занимает большую часть
              },
              {
                text: settings.header.date,
                style: "documentDate",
                alignment: "right",
                width: "auto", // Занимает сколько нужно
              },
            ],
            margin: [0, 0, 0, 18], // Уменьшаем отступ снизу после блока заголовка/даты
          },

          // Контактная информация в две колонки
          {
            columns: [
              // Левая колонка контактов
              {
                width: "*",
                stack: [
                  { text: "Офис:", style: "contactDetailHeader" },
                  { text: settings.header.office, style: "contactDetailText" },
                  {
                    text: `тел. ${settings.header.officePhone}`,
                    style: "contactDetailText",
                    margin: [0, -2, 0, 6],
                  }, // Уменьшаем отступ снизу

                  settings.header.managerName
                    ? { text: "Менеджер:", style: "contactDetailHeader" }
                    : emptyElement,
                  settings.header.managerName
                    ? {
                        text: settings.header.managerName,
                        style: "contactDetailText",
                      }
                    : emptyElement,
                ],
              },
              // Правая колонка контактов
              {
                width: "*",
                stack: [
                  { text: "Склад:", style: "contactDetailHeader" },
                  {
                    text: settings.header.warehouse,
                    style: "contactDetailText",
                  },
                  {
                    text: `тел. ${settings.header.warehousePhone}`,
                    style: "contactDetailText",
                    margin: [0, -2, 0, 6],
                  },

                  settings.header.managerContact && settings.header.managerName // Показываем контакт менеджера, только если есть имя
                    ? { text: "E-mail/телефон:", style: "contactDetailHeader" }
                    : emptyElement,
                  settings.header.managerContact && settings.header.managerName
                    ? {
                        text: settings.header.managerContact,
                        style: "contactDetailText",
                      }
                    : emptyElement,
                ],
              },
            ],
            columnGap: 15, // Уменьшаем пространство между колонками контактов
            margin: [0, 0, 0, 12], // Уменьшаем отступ снизу после блока контактов
          },

          // Информация о доставке
          {
            text: "Условия доставки:", // Добавляем заголовок для блока доставки
            style: "contactDetailHeader", // Используем тот же стиль, что и для "Офис:", "Склад:"
            margin: [0, 0, 0, 2],
          },
          {
            stack: settings.header.deliveryInfo.map((info) => ({
              text: info,
              style: "deliveryDetail",
            })),
            margin: [0, 0, 0, 18], // Уменьшаем отступ после информации о доставке
          },

          // Таблица продуктов
          {
            table: {
              headerRows: 1,
              widths: dynamicColumnWidths, // Используем динамические ширины колонок
              body: [
                tableHeaders, // Просто передаем массив tableHeaders, так как он уже содержит нужные стили и alignment
                ...tableRows, // tableRows генерируются в prepareTableData
              ],
            },
            layout: {
              hLineWidth: function (i) {
                // Линия только под заголовком таблицы
                if (i === 1) return 0.5; // Тонкая линия под заголовком
                return 0; // Нет других горизонтальных линий
              },
              vLineWidth: function () {
                return 0; // Нет вертикальных линий
              },
              hLineColor: function (i) {
                // Цвет линии под заголовком
                return i === 1 ? "#D1D5DB" : "#FFFFFF"; // Tailwind gray-300
              },
              paddingLeft: function (i, node) {
                // Для заголовков групп отступ слева 0, т.к. они на всю ширину
                const rowData =
                  i < node.table.body.length ? node.table.body[i][0] : null;
                if (
                  rowData &&
                  typeof rowData === "object" &&
                  "style" in rowData &&
                  rowData.style === "groupHeaderStyle"
                )
                  return 0;
                return 6; // Уменьшаем отступ для остальных ячеек
              },
              paddingRight: function (i, node) {
                const rowData =
                  i < node.table.body.length ? node.table.body[i][0] : null;
                if (
                  rowData &&
                  typeof rowData === "object" &&
                  "style" in rowData &&
                  rowData.style === "groupHeaderStyle"
                )
                  return 0;
                return 6;
              },
              paddingTop: function (i, node) {
                const rowData =
                  i < node.table.body.length ? node.table.body[i][0] : null;
                if (
                  rowData &&
                  typeof rowData === "object" &&
                  "style" in rowData &&
                  rowData.style === "groupHeaderStyle"
                )
                  return 6; // Уменьшаем отступ сверху для заголовка группы
                if (i === 0) return 4; // Уменьшаем отступ для заголовков таблицы
                return 3; // Уменьшаем отступ для строк данных
              },
              paddingBottom: function (i, node) {
                const rowData =
                  i < node.table.body.length ? node.table.body[i][0] : null;
                if (
                  rowData &&
                  typeof rowData === "object" &&
                  "style" in rowData &&
                  rowData.style === "groupHeaderStyle"
                )
                  return 6; // Уменьшаем отступ снизу для заголовка группы
                if (i === 0) return 4; // Уменьшаем отступ для заголовков таблицы
                return 3; // Уменьшаем отступ для строк данных
              },
              // Убираем заливку по умолчанию, она будет управляться стилями ячеек, если нужно
              // defaultBorder: false, // Это может убрать все границы, лучше управлять через hLineWidth/vLineWidth
            },
          },
        ],
        // --- Обновленный footer ---
        footer: function (currentPage, pageCount) {
          return {
            columns: [
              {
                text: settings.header.companyName,
                alignment: "left",
                style: "pageFooter",
              },
              {
                text: `Страница ${currentPage} из ${pageCount}`,
                alignment: "right",
                style: "pageFooter",
              },
            ],
            margin: [8, 8, 8, 0], // Минимальные отступы для футера [left, top, right, bottom]
          };
        },
        // --- Обновленные styles ---
        styles: {
          mainHeader: {
            fontSize: 16, // Уменьшаем размер заголовка для портретной ориентации
            bold: true,
            color: "#111827", // Tailwind gray-900
            margin: [0, 0, 0, 15], // Уменьшаем отступ снизу
          },
          documentDate: {
            // Стиль для даты документа (справа от заголовка)
            fontSize: 9, // Уменьшаем размер
            color: "#6B7280", // Tailwind gray-500
          },
          contactDetailHeader: {
            // Для заголовков "Офис:", "Склад:"
            fontSize: 8, // Уменьшаем размер
            bold: true,
            color: "#374151", // Tailwind gray-700
            margin: [0, 0, 0, 1],
          },
          contactDetailText: {
            // Для текста адресов и телефонов
            fontSize: 8, // Уменьшаем размер
            color: "#4B5563", // Tailwind gray-600
            margin: [0, 0, 0, 3], // Уменьшаем отступ снизу
          },
          deliveryDetail: {
            // Стиль для строк информации о доставке
            fontSize: 8, // Уменьшаем размер
            color: "#4B5563", // Tailwind gray-600
            margin: [0, 0, 0, 1], // Уменьшаем отступ между строками доставки
            background: "#FFFFE0", // Светло-желтый фон для выделения информации о доставке
          },
          tableHeaderCell: {
            // Стиль для заголовков колонок таблицы
            fontSize: 9, // Уменьшаем размер для портретной ориентации
            bold: true,
            color: "#1F2937", // Tailwind gray-800
          },
          tableCellText: {
            // Стиль для текстовых ячеек данных
            fontSize: 8, // Уменьшаем размер для портретной ориентации
            color: "#374151", // Tailwind gray-700
            alignment: "left",
          },
          tableCellNumber: {
            // Стиль для числовых ячеек данных
            fontSize: 8, // Уменьшаем размер для портретной ориентации
            color: "#374151", // Tailwind gray-700
            alignment: "right",
          },
          productNameLink: {
            // Стиль для наименования продукта (ссылка)
            fontSize: 8, // Уменьшаем размер для портретной ориентации
            color: "#2563EB", // Tailwind blue-600 (стандартный цвет ссылок)
            bold: false,
            alignment: "left",
          },
          groupHeaderStyle: {
            // Стиль для заголовков групп продуктов
            fontSize: 10, // Уменьшаем размер для портретной ориентации
            bold: true,
            color: "#111827", // Tailwind gray-900
            margin: [0, 0, 0, 0], // Управляется padding в layout
            alignment: "left",
          },
          pageFooter: {
            // Стиль для колонтитула
            fontSize: 7, // Уменьшаем размер для портретной ориентации
            color: "#9CA3AF", // Tailwind gray-400
          },
        }, // --- Конец styles ---
      }; // --- Конец docDefinition ---

      const pdfDoc = pdfMake.createPdf(docDefinition);
      if (openInNewWindow) {
        pdfDoc.open();
      } else {
        pdfDoc.download(
          `Прайс-лист_${new Date().toLocaleDateString("ru-RU")}.pdf`
        );
      }
    }; // --- Конец generatePDF ---

    // Запускаем генерацию PDF
    generatePDF();
  } catch (error) {
    console.error("Ошибка при создании PDF:", error);
    // Выбрасываем ошибку дальше, чтобы ее обработал useProductExport
    throw new Error(
      "Ошибка при генерации PDF: " +
        (error instanceof Error ? error.message : String(error))
    );
  }
};
