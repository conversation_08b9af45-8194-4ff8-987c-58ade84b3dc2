import { supabase } from "@/lib/supabase";

export interface AdminUserView {
  id: string;
  email?: string;
  role: string | null;
  created_at?: string;
  last_sign_in_at?: string | null;
}

export interface CreateUserPayload {
  email: string;
  password?: string;
  role: "reader" | "editor";
}

export interface SetUserRolePayload {
  userId: string;
  newRole: "reader" | "editor" | "admin";
}

export interface DeleteUserPayload {
  userId: string;
}

async function invokeEdgeFunction<T>(
  functionName: string,
  body?: unknown
): Promise<T> {
  const { data: sessionData, error: sessionError } =
    await supabase.auth.getSession();
  if (sessionError) {
    console.error("Session error in invokeEdgeFunction:", sessionError);
    throw new Error(sessionError.message || "Failed to get user session.");
  }
  if (!sessionData.session) {
    throw new Error("User not authenticated.");
  }
  const token = sessionData.session.access_token;

  const { data, error } = await supabase.functions.invoke(functionName, {
    body: JSON.stringify(body),
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (error) {
    const errorMessage =
      data?.error?.message ||
      error.message ||
      `Edge function ${functionName} call failed.`;
    console.error(
      `Error invoking Edge Function ${functionName}:`,
      errorMessage,
      { data, error }
    );
    throw new Error(errorMessage);
  }

  if (
    data &&
    data.error &&
    typeof data.error === "object" &&
    "message" in data.error
  ) {
    console.error(
      `Functional error from Edge Function ${functionName}:`,
      data.error.message,
      data
    );
    throw new Error(data.error.message as string);
  }

  return data as T;
}

export const userService = {
  async getUsers(): Promise<AdminUserView[]> {
    return invokeEdgeFunction<AdminUserView[]>("get-users-with-roles");
  },

  async createUser(payload: CreateUserPayload): Promise<AdminUserView> {
    return invokeEdgeFunction<AdminUserView>("create-user-with-role", payload);
  },

  async setUserRole(payload: SetUserRolePayload): Promise<AdminUserView> {
    return invokeEdgeFunction<AdminUserView>("set-user-role", payload);
  },

  async deleteUser(payload: DeleteUserPayload): Promise<{ message: string }> {
    return invokeEdgeFunction<{ message: string }>("delete-user", payload);
  },
};
