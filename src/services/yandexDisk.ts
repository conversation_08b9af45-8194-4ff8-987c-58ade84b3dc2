import axios, { AxiosError } from "axios";

const YANDEX_DISK_API = "https://cloud-api.yandex.net/v1/disk/resources";
const token = import.meta.env.VITE_YANDEX_DISK_TOKEN;

if (!token) {
  throw new Error("Missing Yandex.Disk token in environment variables");
}

interface UploadUrlResponse {
  href: string;
  method: string;
  templated: boolean;
}

interface PublicUrlResponse {
  public_url: string;
}

interface YandexDiskItem {
  name: string;
  path: string;
  preview?: string;
  public_key?: string;
  type: "dir" | "file";
  resource_id: string;
  public_url?: string;
}

interface YandexDiskPublicFolderResponse {
  _embedded: {
    items: YandexDiskItem[];
  };
  public_key: string;
  public_url: string;
}

/**
 * Получает URL для загрузки файла на Яндекс.Диск
 * @param path - путь, куда будет загружен файл на Яндекс.Диске
 */
export const getUploadUrl = async (path: string): Promise<string> => {
  try {
    const response = await axios.get<UploadUrlResponse>(
      `${YANDEX_DISK_API}/upload`,
      {
        headers: {
          Authorization: `OAuth ${token}`,
        },
        params: {
          path,
        },
      }
    );
    return response.data.href;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка получения URL для загрузки: ${error.message}`);
    }
    throw error;
  }
};

/**
 * Загружает файл на Яндекс.Диск
 * @param uploadUrl - URL для загрузки, полученный из getUploadUrl
 * @param file - файл для загрузки
 */
export const uploadFile = async (
  uploadUrl: string,
  file: File
): Promise<void> => {
  try {
    await axios.put(uploadUrl, file, {
      headers: {
        "Content-Type": file.type,
      },
    });
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка загрузки файла: ${error.message}`);
    }
    throw error;
  }
};

/**
 * Публикует папку на Яндекс.Диске
 * @param path - путь к папке на Яндекс.Диске
 */
export const publishFolder = async (path: string): Promise<void> => {
  try {
    await axios.put(`${YANDEX_DISK_API}/publish`, null, {
      headers: {
        Authorization: `OAuth ${token}`,
      },
      params: {
        path,
      },
    });
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка публикации папки: ${error.message}`);
    }
    throw error;
  }
};

/**
 * Получает публичную ссылку на опубликованный ресурс
 * @param path - путь к ресурсу на Яндекс.Диске
 */
export const getPublicUrl = async (path: string): Promise<string> => {
  try {
    const response = await axios.get<PublicUrlResponse>(YANDEX_DISK_API, {
      headers: {
        Authorization: `OAuth ${token}`,
      },
      params: {
        path,
        fields: "public_url",
      },
    });
    return response.data.public_url;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка получения публичной ссылки: ${error.message}`);
    }
    throw error;
  }
};

/**
 * Создает папку на Яндекс.Диске
 * @param path - путь к создаваемой папке
 */
export const createFolder = async (path: string): Promise<void> => {
  try {
    await axios.put(YANDEX_DISK_API, null, {
      headers: {
        Authorization: `OAuth ${token}`,
      },
      params: {
        path,
      },
    });
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка создания папки: ${error.message}`);
    }
    throw error;
  }
};

/**
 * Получает список файлов из публичной папки
 * @param publicUrl - публичная ссылка на папку
 * @returns список файлов в папке
 */
export const getPublicFolderContents = async (
  publicUrl: string
): Promise<YandexDiskItem[]> => {
  try {
    const response = await axios.get<YandexDiskPublicFolderResponse>(
      "https://cloud-api.yandex.net/v1/disk/public/resources",
      {
        headers: {
          Authorization: `OAuth ${token}`,
        },
        params: {
          public_key: publicUrl,
          fields:
            "_embedded.items.name,_embedded.items.path,_embedded.items.preview,_embedded.items.public_url,_embedded.items.type",
        },
      }
    );

    return response.data._embedded.items;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(
        `Ошибка получения содержимого публичной папки: ${error.message}`
      );
    }
    throw error;
  }
};

/**
 * Получает прямую ссылку на файл для просмотра из публичной ссылки
 * @param publicUrl - публичная ссылка на файл или на папку
 * @param fileName - имя файла (если publicUrl указывает на папку)
 * @returns прямая ссылка на файл для просмотра
 */
export const getDirectFileUrl = async (
  publicUrl: string,
  fileName?: string
): Promise<string> => {
  try {
    const params: Record<string, string> = {
      public_key: publicUrl,
    };

    // Если указано имя файла, добавляем его в параметр path
    if (fileName) {
      params.path = `/${fileName}`;
    }

    const response = await axios.get(
      "https://cloud-api.yandex.net/v1/disk/public/resources/download",
      {
        params,
      }
    );

    return response.data.href;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(
        `Ошибка получения прямой ссылки на файл: ${error.message}`
      );
    }
    throw error;
  }
};

/**
 * Загружает файлы в новую папку на Яндекс.Диске и публикует её
 * @param files - один или несколько файлов для загрузки
 * @param folderName - название папки (если не указано, будет сгенерировано автоматически)
 * @returns публичную ссылку на папку
 */
export const uploadFilesAndPublish = async (
  files: File | File[],
  folderName?: string
): Promise<string> => {
  try {
    // Преобразуем один файл в массив
    const fileArray = Array.isArray(files) ? files : [files];

    // Генерируем уникальное имя папки, если оно не указано
    const timestamp = new Date().getTime();
    const targetFolder = folderName || `uploads_${timestamp}`;
    const folderPath = `/${targetFolder}`;

    // Создаем папку
    await createFolder(folderPath);

    // Загружаем все файлы параллельно
    const uploadPromises = fileArray.map(async (file) => {
      const filePath = `${folderPath}/${file.name}`;
      const uploadUrl = await getUploadUrl(filePath);
      await uploadFile(uploadUrl, file);
    });

    await Promise.all(uploadPromises);

    // Публикуем папку
    await publishFolder(folderPath);

    // Получаем и возвращаем публичную ссылку
    return await getPublicUrl(folderPath);
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(
        `Ошибка загрузки файлов и публикации папки: ${error.message}`
      );
    }
    throw error;
  }
};

/**
 * Удаляет файл или папку на Яндекс.Диске
 * @param path - путь к удаляемому ресурсу
 * @param permanently - признак безвозвратного удаления (true - удалить без помещения в корзину)
 */
export const deleteFile = async (
  path: string,
  permanently: boolean = false
): Promise<void> => {
  try {
    await axios.delete(YANDEX_DISK_API, {
      headers: {
        Authorization: `OAuth ${token}`,
      },
      params: {
        path,
        permanently,
      },
    });
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка удаления ресурса: ${error.message}`);
    }
    throw error;
  }
};
