import { supabase } from "../lib/supabase";

// Функция для преобразования URL Яндекс.Диска в проксированный URL через Supabase Edge Function
export const proxyYandexUrl = async (originalUrl: string): Promise<string> => {
  if (!originalUrl) {
    return originalUrl;
  }

  // Проверяем, нужно ли проксировать URL (домены Яндекса)
  const needsProxy =
    originalUrl.includes("downloader.disk.yandex.ru") ||
    originalUrl.includes("getfile.yandex.net") ||
    originalUrl.includes("disk.yandex.ru") ||
    originalUrl.includes("yastatic.net");

  if (!needsProxy) {
    return originalUrl;
  }

  // Получаем сессию для авторизации
  const { data: sessionData, error: sessionError } =
    await supabase.auth.getSession();

  if (sessionError) {
    console.warn("Session error in proxyYandexUrl:", sessionError);
    return originalUrl;
  }

  if (!sessionData.session) {
    console.warn("User not authenticated, returning original URL");
    return originalUrl;
  }

  // Используем правильный URL для Edge Function
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  if (!supabaseUrl) {
    console.warn("VITE_SUPABASE_URL not found, returning original URL");
    return originalUrl;
  }

  const token = sessionData.session.access_token;
  const encodedUrl = encodeURIComponent(originalUrl);
  const proxyUrl = `${supabaseUrl}/functions/v1/proxy-yandex-image?url=${encodedUrl}`;

  try {
    // Делаем запрос к Edge Function с авторизацией
    const response = await fetch(proxyUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        apikey: import.meta.env.VITE_SUPABASE_ANON_KEY || "",
      },
    });

    if (!response.ok) {
      console.error(
        `Proxy request failed: ${response.status} ${response.statusText}`
      );
      const errorText = await response.text();
      console.error("Error response:", errorText);
      return originalUrl;
    }

    // Создаем blob URL для отображения изображения
    const blob = await response.blob();
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error("Error proxying image:", error);
    return originalUrl;
  }
};

export const imageProxyService = {
  proxyYandexUrl,
};
