import { describe, it, expect, vi, beforeEach } from 'vitest';
import { exportToExcel } from '../excelExport';
import type { RowData, PDFExportSettings } from '../../types/export';

// Mock ExcelJS
vi.mock('exceljs', () => {
  const mockWorksheet = {
    addWorksheet: vi.fn().mockReturnThis(),
    getCell: vi.fn().mockReturnValue({
      value: '',
      font: {},
      numFmt: '',
    }),
    mergeCells: vi.fn(),
    addRow: vi.fn().mockReturnValue({
      eachCell: vi.fn(),
    }),
    columns: [],
    xlsx: {
      writeBuffer: vi.fn().mockResolvedValue(new ArrayBuffer(0)),
    },
  };

  const mockWorkbook = {
    addWorksheet: vi.fn().mockReturnValue(mockWorksheet),
    xlsx: {
      writeBuffer: vi.fn().mockResolvedValue(new ArrayBuffer(0)),
    },
  };

  return {
    default: {
      Workbook: vi.fn().mockImplementation(() => mockWorkbook),
    },
  };
});

// Mock file-saver
vi.mock('file-saver', () => ({
  saveAs: vi.fn(),
}));

describe('Excel Export with Text Formatting', () => {
  const mockRowData: RowData[] = [
    {
      id: '1',
      name: 'Plain product name',
      price: 100,
      description: 'Simple description',
      custom_fields: {},
    },
    {
      id: '2',
      name: 'Product with <format bold="true">bold</format> name',
      price: 200,
      description: 'Description with <format red="true">red text</format>',
      custom_fields: {
        field1: 'Custom <format bold="true" red="true">formatted</format> field',
      },
    },
    {
      id: '3',
      name: 'Complex <format bold="true">bold</format> and <format red="true">red</format> formatting',
      price: 300,
      description: 'Multiple <format bold="true">bold words</format> and <format red="true">red words</format>',
      custom_fields: {},
    },
  ];

  const mockSettings: PDFExportSettings = {
    header: {
      companyName: 'Test Company',
      title: 'Test Export',
      subtitle: 'Test Subtitle',
    },
    columns: [
      {
        field: 'name',
        title: 'Name',
        selected: true,
        width: 200,
        prefix: '',
        postfix: '',
      },
      {
        field: 'price',
        title: 'Price',
        selected: true,
        width: 100,
        prefix: '',
        postfix: '₽',
      },
      {
        field: 'description',
        title: 'Description',
        selected: true,
        width: 300,
        prefix: '',
        postfix: '',
      },
    ],
    customFields: [
      {
        id: 'field1',
        name: 'Custom Field 1',
        type: 'text',
        selected: true,
        width: 150,
        prefix: '',
        postfix: '',
      },
    ],
    footer: {
      showDate: true,
      showPageNumbers: true,
      customText: '',
    },
    formatting: {
      fontSize: 10,
      fontFamily: 'Arial',
      showBorders: true,
      alternateRowColors: false,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should export data without throwing errors', async () => {
    await expect(exportToExcel(mockRowData, mockSettings)).resolves.not.toThrow();
  });

  it('should handle plain text without formatting', async () => {
    const plainData: RowData[] = [
      {
        id: '1',
        name: 'Simple product',
        price: 100,
        description: 'Simple description',
        custom_fields: {},
      },
    ];

    await expect(exportToExcel(plainData, mockSettings)).resolves.not.toThrow();
  });

  it('should handle empty data array', async () => {
    await expect(exportToExcel([], mockSettings)).resolves.not.toThrow();
  });

  it('should handle null and undefined values', async () => {
    const dataWithNulls: RowData[] = [
      {
        id: '1',
        name: null as any,
        price: undefined as any,
        description: '',
        custom_fields: {
          field1: null,
        },
      },
    ];

    await expect(exportToExcel(dataWithNulls, mockSettings)).resolves.not.toThrow();
  });

  it('should handle complex nested formatting', async () => {
    const complexData: RowData[] = [
      {
        id: '1',
        name: 'Start <format bold="true">bold <format red="true">and red</format> text</format> end',
        price: 100,
        description: 'Complex formatting test',
        custom_fields: {},
      },
    ];

    await expect(exportToExcel(complexData, mockSettings)).resolves.not.toThrow();
  });

  it('should handle malformed formatting tags gracefully', async () => {
    const malformedData: RowData[] = [
      {
        id: '1',
        name: 'Malformed <format bold="true">unclosed tag',
        price: 100,
        description: 'Missing <format>attributes</format>',
        custom_fields: {
          field1: '<format>incomplete',
        },
      },
    ];

    await expect(exportToExcel(malformedData, mockSettings)).resolves.not.toThrow();
  });

  it('should handle very long text with formatting', async () => {
    const longText = 'This is a very long text with multiple formatting options. '.repeat(10) +
      '<format bold="true">Bold section</format> and <format red="true">red section</format> ' +
      'and more text. '.repeat(5);

    const longTextData: RowData[] = [
      {
        id: '1',
        name: longText,
        price: 100,
        description: longText,
        custom_fields: {
          field1: longText,
        },
      },
    ];

    await expect(exportToExcel(longTextData, mockSettings)).resolves.not.toThrow();
  });

  it('should handle special characters in formatted text', async () => {
    const specialCharsData: RowData[] = [
      {
        id: '1',
        name: 'Special chars: <format bold="true">@#$%^&*()</format> and <format red="true">émojis 🎉</format>',
        price: 100,
        description: 'Unicode: <format bold="true">Привет мир</format> and <format red="true">こんにちは</format>',
        custom_fields: {},
      },
    ];

    await expect(exportToExcel(specialCharsData, mockSettings)).resolves.not.toThrow();
  });

  it('should handle empty formatted segments', async () => {
    const emptySegmentData: RowData[] = [
      {
        id: '1',
        name: 'Text with <format bold="true"></format> empty formatting',
        price: 100,
        description: 'Multiple <format bold="true"></format> empty <format red="true"></format> segments',
        custom_fields: {},
      },
    ];

    await expect(exportToExcel(emptySegmentData, mockSettings)).resolves.not.toThrow();
  });

  it('should handle mixed content types', async () => {
    const mixedData: RowData[] = [
      {
        id: '1',
        name: 'Product 1',
        price: 100.50,
        description: 'Number in text: <format bold="true">123</format>',
        custom_fields: {
          field1: 'Boolean: <format red="true">true</format>',
        },
      },
    ];

    await expect(exportToExcel(mixedData, mockSettings)).resolves.not.toThrow();
  });

  it('should handle all formatting combinations', async () => {
    const allFormatsData: RowData[] = [
      {
        id: '1',
        name: 'Only <format bold="true">bold</format>',
        price: 100,
        description: 'Only <format red="true">red</format>',
        custom_fields: {
          field1: 'Both <format bold="true" red="true">bold and red</format>',
        },
      },
    ];

    await expect(exportToExcel(allFormatsData, mockSettings)).resolves.not.toThrow();
  });

  it('should handle settings with no selected columns', async () => {
    const noColumnsSettings = {
      ...mockSettings,
      columns: mockSettings.columns.map(col => ({ ...col, selected: false })),
      customFields: [],
    };

    await expect(exportToExcel(mockRowData, noColumnsSettings)).resolves.not.toThrow();
  });

  it('should handle settings with no custom fields', async () => {
    const noCustomFieldsSettings = {
      ...mockSettings,
      customFields: [],
    };

    await expect(exportToExcel(mockRowData, noCustomFieldsSettings)).resolves.not.toThrow();
  });

  it('should handle price formatting with text formatting', async () => {
    const priceFormattingData: RowData[] = [
      {
        id: '1',
        name: 'Product with <format bold="true">formatted name</format>',
        price: 1234.56,
        description: 'Price should be formatted as currency',
        custom_fields: {},
      },
    ];

    await expect(exportToExcel(priceFormattingData, mockSettings)).resolves.not.toThrow();
  });

  it('should handle date formatting alongside text formatting', async () => {
    const dateFormattingData: RowData[] = [
      {
        id: '1',
        name: 'Product with <format bold="true">date</format>',
        price: 100,
        description: 'Created today',
        created_at: new Date().toISOString(),
        custom_fields: {},
      },
    ];

    await expect(exportToExcel(dateFormattingData, mockSettings)).resolves.not.toThrow();
  });
});
