import ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import type { Database } from "../types/supabase";
import { PDFExportSettings } from "../components/products/export/pdfExportUtils";
import { GroupHeaderRow } from "../components/products/grid/GroupHeaderRenderer";
import { parseFormattedText, hasFormatting } from "../utils/textFormatting";
import type { TextSegment } from "../utils/textFormatting";

type Product = Database["public"]["Tables"]["products"]["Row"] & {
  product_images?: Array<Database["public"]["Tables"]["product_images"]["Row"]>;
};
type RowData = Product | GroupHeaderRow;

const isGroupHeaderRow = (item: RowData): item is GroupHeaderRow => {
  return "isGroupHeader" in item && item.isGroupHeader === true;
};

const getProductUrl = (product: Product): string | null => {
  if (product.product_images && product.product_images.length > 0) {
    const mainImage =
      product.product_images.find((img) => img.is_main) ||
      product.product_images[0];
    if (mainImage && mainImage.public_url) {
      return mainImage.public_url;
    }
  }
  return null;
};

const formatValueExcel = (
  product: Product,
  field: string,
  prefix?: string | null,
  postfix?: string | null
): string | number | any => {
  if (!product) return "";

  let valueToFormat: unknown;
  let isPriceField = false;

  if (field.startsWith("custom_fields.")) {
    const fieldName = field.split(".")[1];
    const customFields = product.custom_fields as Record<string, unknown>;
    valueToFormat = customFields?.[fieldName];
  } else {
    valueToFormat = product[field as keyof Product];
  }

  if (
    field === "price" ||
    (field.startsWith("custom_fields.") && postfix === "₽")
  ) {
    isPriceField = true;
  }

  if (valueToFormat === null || valueToFormat === undefined) {
    return "";
  }

  if (isPriceField) {
    const priceNum = Number(valueToFormat);
    return !isNaN(priceNum) ? priceNum : String(valueToFormat);
  }

  let formattedValue = "";
  if (field === "is_hidden") {
    formattedValue = valueToFormat ? "Скрыт" : "Активен";
  } else if (field === "created_at" && typeof valueToFormat === "string") {
    try {
      formattedValue = new Date(valueToFormat).toLocaleDateString("ru-RU");
    } catch {
      formattedValue = String(valueToFormat);
    }
  } else if (field === "product_images") {
    formattedValue =
      product.product_images && product.product_images.length > 0
        ? "Есть"
        : "Нет";
  } else {
    formattedValue = String(valueToFormat);
  }

  if (prefix && prefix.trim() !== "" && formattedValue) {
    formattedValue = `${prefix.trim()} ${formattedValue}`;
  }
  if (postfix && postfix.trim() !== "" && formattedValue) {
    formattedValue = `${formattedValue} ${postfix.trim()}`;
  }

  const finalValue = formattedValue.trim();

  if (hasFormatting(finalValue)) {
    return createRichText(finalValue);
  }

  return finalValue;
};

const createRichText = (formattedText: string): any => {
  const segments = parseFormattedText(formattedText);

  const richTextRuns = segments.map((segment: TextSegment) => {
    const richTextRun: Record<string, any> = {
      text: segment.text,
    };

    if (segment.bold || segment.red) {
      richTextRun.font = {};

      if (segment.bold) {
        richTextRun.font.bold = true;
      }

      if (segment.red) {
        richTextRun.font.color = { argb: "FFDC2626" }; // red-600
      }
    }

    return richTextRun;
  });

  return { richText: richTextRuns };
};

export const exportToExcel = async (
  data: RowData[],
  settings: PDFExportSettings
): Promise<void> => {
  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet("Прайс-лист");

  let currentRow = 1;
  sheet.getCell(
    `A${currentRow}`
  ).value = `Коммерческое предложение ${settings.header.companyName}`;
  sheet.getCell(`A${currentRow}`).font = {
    name: "Arial",
    size: 16,
    bold: true,
  };
  const numberOfColumns =
    settings.columns.filter((col) => col.selected).length + 2; // +1 для "ЗАКАЗ" и +1 для "Фото"
  sheet.mergeCells(
    `A${currentRow}:${String.fromCharCode(64 + numberOfColumns)}${currentRow}`
  );
  currentRow++;

  sheet.getCell(`A${currentRow}`).value = settings.header.date;
  sheet.getCell(`A${currentRow}`).font = { name: "Arial", size: 10 };
  sheet.getCell(`A${currentRow}`).alignment = { horizontal: "right" };
  sheet.mergeCells(
    `A${currentRow}:${String.fromCharCode(64 + numberOfColumns)}${currentRow}`
  );
  currentRow += 2;

  const contactInfoStartRow = currentRow;
  sheet.getCell(`A${currentRow}`).value = "Офис:";
  sheet.getCell(`A${currentRow}`).font = { bold: true };
  currentRow++;
  sheet.getCell(`A${currentRow}`).value = settings.header.office;
  sheet.mergeCells(`A${currentRow}:B${currentRow}`);
  currentRow++;
  sheet.getCell(`A${currentRow}`).value = `тел. ${settings.header.officePhone}`;
  sheet.mergeCells(`A${currentRow}:B${currentRow}`);
  currentRow++;

  if (settings.header.managerName) {
    sheet.getCell(`A${currentRow}`).value = "Менеджер:";
    sheet.getCell(`A${currentRow}`).font = { bold: true };
    currentRow++;
    sheet.getCell(`A${currentRow}`).value = settings.header.managerName;
    sheet.mergeCells(`A${currentRow}:B${currentRow}`);
    currentRow++;
  }

  const contactRightColStart = Math.max(Math.floor(numberOfColumns / 2), 3);
  const contactChar = String.fromCharCode(64 + contactRightColStart);

  currentRow = contactInfoStartRow;
  sheet.getCell(`${contactChar}${currentRow}`).value = "Склад:";
  sheet.getCell(`${contactChar}${currentRow}`).font = { bold: true };
  currentRow++;
  sheet.getCell(`${contactChar}${currentRow}`).value =
    settings.header.warehouse;
  sheet.mergeCells(
    `${contactChar}${currentRow}:${String.fromCharCode(
      67 + contactRightColStart
    )}${currentRow}`
  );
  currentRow++;
  sheet.getCell(
    `${contactChar}${currentRow}`
  ).value = `тел. ${settings.header.warehousePhone}`;
  sheet.mergeCells(
    `${contactChar}${currentRow}:${String.fromCharCode(
      67 + contactRightColStart
    )}${currentRow}`
  );
  currentRow++;

  if (settings.header.managerName && settings.header.managerContact) {
    sheet.getCell(`${contactChar}${currentRow}`).value = "E-mail/телефон:";
    sheet.getCell(`${contactChar}${currentRow}`).font = { bold: true };
    currentRow++;
    sheet.getCell(`${contactChar}${currentRow}`).value =
      settings.header.managerContact;
    sheet.mergeCells(
      `${contactChar}${currentRow}:${String.fromCharCode(
        67 + contactRightColStart
      )}${currentRow}`
    );
    currentRow++;
  }
  currentRow =
    Math.max(
      currentRow,
      contactInfoStartRow + (settings.header.managerName ? 6 : 4)
    ) + 1;

  sheet.getCell(`A${currentRow}`).value = "Условия доставки:";
  sheet.getCell(`A${currentRow}`).font = { bold: true };
  currentRow++;
  settings.header.deliveryInfo.forEach((info) => {
    sheet.getCell(`A${currentRow}`).value = info;
    sheet.mergeCells(
      `A${currentRow}:${String.fromCharCode(64 + numberOfColumns)}${currentRow}`
    );

    sheet.getRow(currentRow).eachCell({ includeEmpty: true }, (cell) => {
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFFFFFE0" }, // Светло-желтый цвет (LightYellow)
      };
    });

    currentRow++;
  });
  currentRow++;

  const selectedColumns = settings.columns.filter((col) => col.selected);
  const tableHeaders = [
    "Фото", // Добавляем столбец для фотографий
    ...selectedColumns.map((col) => col.headerName),
    "ЗАКАЗ",
  ];
  const headerRow = sheet.addRow(tableHeaders);
  headerRow.font = { bold: true, name: "Arial", size: 10 };
  headerRow.eachCell((cell, colNumber) => {
    cell.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFD3D3D3" },
    };
    cell.border = {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    };

    // Выравнивание заголовков
    if (colNumber === 1) {
      // Столбец "Фото" - центрируем
      cell.alignment = { vertical: "middle", horizontal: "center" };
    } else {
      // Остальные столбцы
      const colSetting = selectedColumns[colNumber - 2]; // -2 потому что первый столбец теперь "Фото"
      if (colSetting && colSetting.field === "name") {
        cell.alignment = { vertical: "middle", horizontal: "left" };
      } else {
        // Все остальные заголовки (включая "ЗАКАЗ") центрируем
        cell.alignment = { vertical: "middle", horizontal: "center" };
      }
    }
  });
  currentRow++;

  const nonEmptyGroupIds = new Set<string>();
  if (settings.includeGroups) {
    data.forEach((item) => {
      if (!isGroupHeaderRow(item) && item.group_id) {
        nonEmptyGroupIds.add(item.group_id);
      }
    });
  }

  data.forEach((item) => {
    if (isGroupHeaderRow(item)) {
      if (settings.includeGroups) {
        let shouldAddGroupHeader = false;
        if (item.groupId === "ungrouped") {
          shouldAddGroupHeader = true;
        } else if (nonEmptyGroupIds.has(item.groupId)) {
          shouldAddGroupHeader = true;
        }

        if (shouldAddGroupHeader) {
          const groupRow = sheet.addRow([item.groupName]);
          sheet.mergeCells(
            groupRow.number,
            1,
            groupRow.number,
            numberOfColumns
          );
          groupRow.getCell(1).font = { bold: true, name: "Arial", size: 11 };
          groupRow.getCell(1).fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFE0E0E0" },
          };
          groupRow.getCell(1).alignment = {
            vertical: "middle",
            horizontal: "center",
          };
          currentRow++;
        }
      }
    } else {
      const product = item as Product;
      let shouldAddProduct = false;
      if (!settings.includeGroups) {
        shouldAddProduct = true;
      } else {
        if (product.group_id === null) {
          shouldAddProduct = true;
        } else if (nonEmptyGroupIds.has(product.group_id)) {
          shouldAddProduct = true;
        }
      }

      if (shouldAddProduct) {
        // Создаем значение для столбца "Фото"
        let photoValue: string | { text: string; hyperlink: string } = "";
        if (product.product_images && product.product_images.length > 0) {
          const productUrl = getProductUrl(product);
          if (productUrl) {
            photoValue = {
              text: "📷",
              hyperlink: productUrl,
            };
          }
        }

        // Создаем значения для остальных столбцов
        const columnValues = selectedColumns.map((col) => {
          return formatValueExcel(product, col.field, col.prefix, col.postfix);
        });

        // Собираем все значения: Фото + остальные столбцы + ЗАКАЗ
        const rowValues = [photoValue, ...columnValues, ""];
        const dataRow = sheet.addRow(rowValues);
        dataRow.font = { name: "Arial", size: 11 };
        dataRow.height = 20; // Увеличиваем высоту строки для больших отступов
        dataRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          cell.border = {
            top: { style: "thin" },
            left: { style: "thin" },
            bottom: { style: "thin" },
            right: { style: "thin" },
          };

          // Определяем тип столбца
          if (colNumber === 1) {
            // Столбец "Фото"
            cell.alignment = { horizontal: "center", vertical: "middle" };

            // Обрабатываем гиперссылку для фотоаппарата
            if (
              cell.value &&
              typeof cell.value === "object" &&
              "text" in cell.value &&
              "hyperlink" in cell.value
            ) {
              cell.value = {
                text: (cell.value as { text: string }).text,
                hyperlink: (cell.value as { hyperlink: string }).hyperlink,
              };
              cell.font = {
                name: "Arial",
                size: 11,
                color: { argb: "FF0000FF" },
              };
            }
          } else {
            // Остальные столбцы (данные продукта + ЗАКАЗ)
            const colSetting = selectedColumns[colNumber - 2]; // -2 потому что первый столбец теперь "Фото"

            if (colSetting && colSetting.field === "name") {
              cell.alignment = { horizontal: "left", vertical: "middle" };
            } else {
              cell.alignment = { horizontal: "center", vertical: "middle" };
            }

            if (colSetting) {
              if (
                colSetting.field === "price" ||
                (colSetting.postfix === "₽" && typeof cell.value === "number")
              ) {
                cell.numFmt = '#,##0.00" ₽"';
              }
            }
          }
        });
        currentRow++;
      }
    }
  });

  // Определяем диапазон строк, относящихся к таблице данных
  const firstDataRowNumber = headerRow.number; // Номер строки с заголовками таблицы (1-based)
  const lastDataRowNumber = currentRow - 1;

  // Функция для расчета ширины текста в Excel с учетом особенностей шрифта Arial
  const calculateTextWidth = (text: string): number => {
    if (!text) return 0;

    let charCount = 0;

    for (const char of text) {
      if (/[WMwmШЩЖЫЮ@]/.test(char)) {
        charCount += 1.3; // Очень широкие символы
      } else if (
        /[ABCDEFGHIJKLNOPQRSTUVXYZАБВГДЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ#%&0-9]/.test(
          char
        )
      ) {
        charCount += 1.1; // Широкие символы
      } else if (/[iltjf.,;:!|'"]/.test(char)) {
        charCount += 0.7; // Узкие символы
      } else {
        charCount += 1; // Обычные символы
      }
    }

    return Math.ceil(charCount);
  };

  // Автоматический расчет ширины колонок на основе содержимого
  sheet.columns.forEach((column, colIdx) => {
    let maxWidth = 0;

    // Получаем текст заголовка колонки
    const headerText =
      colIdx < tableHeaders.length
        ? String(tableHeaders[colIdx] || `Колонка ${colIdx + 1}`)
        : `Колонка ${colIdx + 1}`;

    maxWidth = Math.max(maxWidth, calculateTextWidth(headerText));

    // Анализируем содержимое всех ячеек в колонке
    for (let i = firstDataRowNumber + 1; i <= lastDataRowNumber; i++) {
      const row = sheet.getRow(i);
      const colNum =
        typeof column.number === "number" ? column.number : colIdx + 1;
      const cell = row.getCell(colNum);

      let cellText = "";

      // Проверяем тип строки (заголовок группы или данные продукта)
      const firstCell = row.getCell(1);
      const isGroupHeader = !!(
        firstCell.isMerged && firstCell.master === firstCell
      );

      if (isGroupHeader && colNum === 1) {
        // Заголовок группы только в первой колонке
        cellText = cell.value ? cell.value.toString() : "";
      } else if (!isGroupHeader) {
        // Данные продукта
        if (
          cell.value &&
          typeof cell.value === "object" &&
          "text" in cell.value
        ) {
          cellText = (cell.value as { text: string }).text; // Гиперссылка
        } else if (
          cell.value &&
          typeof cell.value === "object" &&
          "richText" in cell.value
        ) {
          // Rich text - извлекаем только текст для расчета ширины
          const richTextValue = cell.value as {
            richText: Array<{ text: string }>;
          };
          cellText = richTextValue.richText.map((run) => run.text).join("");
        } else {
          cellText = cell.value ? cell.value.toString() : "";
        }
      }

      maxWidth = Math.max(maxWidth, calculateTextWidth(cellText));
    }

    // Финальная ширина с небольшими отступами
    let finalWidth = maxWidth + 2;

    // Специальная обработка для специальных колонок
    const isPhotoColumn = colIdx === 0;
    const isOrderColumn = colIdx === selectedColumns.length + 1; // +1 потому что добавили столбец "Фото"

    if (isPhotoColumn) {
      // Столбец "Фото" - фиксированная ширина
      finalWidth = 6;
    } else if (isOrderColumn) {
      // Столбец "ЗАКАЗ"
      finalWidth = Math.max(finalWidth, 8);
      finalWidth = Math.min(finalWidth, 15);
    }

    column.width = finalWidth;
  });

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });
  saveAs(
    blob,
    `Прайс-лист_${new Date()
      .toLocaleDateString("ru-RU")
      .replace(/\./g, "-")}.xlsx`
  );
};
