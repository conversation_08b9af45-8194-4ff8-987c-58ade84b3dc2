This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
.bolt/
  config.json
  prompt
.cursor/
  rules/
    database-best-practices.mdc
    pnpm-rule.mdc
    shadcn-rules.mdc
src/
  components/
    products/
      columns/
        productColumnUtils.ts
        useProductColumns.ts
      customFields/
        customFieldsHandler.ts
      editHandlers/
        useProductEditHandlers.ts
      export/
        PDFExportSettingsModal.tsx
        pdfExportUtils.ts
        useProductExport.ts
      grid/
        CustomFieldCellRenderer.tsx
        DropZone.tsx
        GroupHeaderRenderer.tsx
        ImageCellRenderer.tsx
        ImageGallery.tsx
        ImageViewer.tsx
        index.ts
        useProductGrid.ts
        VisibilityStatusRenderer.tsx
      modals/
        ColumnSettingsModal.tsx
        ProductGroupsModal.tsx
      ProductList.tsx
      ProductToolbar.tsx
    ui/
      alert.tsx
      Badge.tsx
      Button.tsx
      Card.tsx
      carousel.tsx
      checkbox.tsx
      dialog.tsx
      Divider.tsx
      dropdown-menu.tsx
      IconButton.tsx
      Input.tsx
      label.tsx
      Modal.tsx
      scroll-area.tsx
      select.tsx
      sheet.tsx
      switch.tsx
      toast.tsx
      toaster.tsx
      tooltip.tsx
    LoginPage.tsx
    ProtectedRoute.tsx
    UploadTest.tsx
  db/
    meta/
      _journal.json
      0000_snapshot.json
      0001_snapshot.json
      0002_snapshot.json
      0003_snapshot.json
      0004_snapshot.json
      0005_snapshot.json
      0006_snapshot.json
      0007_snapshot.json
    0000_crazy_energizer.sql
    0001_adorable_shriek.sql
    0002_silly_firebird.sql
    0003_tiresome_madame_hydra.sql
    0004_chilly_sister_grimm.sql
    0005_rare_cobalt_man.sql
    0006_last_the_watchers.sql
    0007_ambitious_vampiro.sql
    index.ts
    init.ts
    relations.ts
    schema.ts
    types.ts
  hooks/
    use-toast.ts
    useColumnSettings.ts
    useImageFolder.ts
    useImageUpload.ts
    useProductGroups.ts
    useProducts.ts
  lib/
    auth.tsx
    supabase.ts
    utils.ts
  services/
    pdfmakeExport.ts
    yandexDisk.ts
  types/
    supabase.ts
  App.tsx
  index.css
  main.tsx
  vite-env.d.ts
.env.example
.gitignore
.prettierrc
components.json
drizzle.config.ts
eslint.config.js
index.html
package.json
postcss.config.js
README.md
tailwind.config.js
tsconfig.app.json
tsconfig.json
tsconfig.node.json
vite.config.ts
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path=".bolt/config.json">
{
  "template": "bolt-vite-react-ts"
}
</file>

<file path=".bolt/prompt">
For all designs I ask you to make, have them be beautiful, not cookie cutter. Make webpages that are fully featured and worthy for production.

By default, this template supports JSX syntax with Tailwind CSS classes, React hooks, and Lucide React for icons. Do not install other packages for UI themes, icons, etc unless absolutely necessary or I request them.

Use icons from lucide-react for logos.

Use stock photos from unsplash where appropriate, only valid URLs you know exist. Do not download the images, only link to them in image tags.
</file>

<file path=".cursor/rules/database-best-practices.mdc">
---
description: Best practices for database management with Drizzle and Supabase
globs: 
alwaysApply: true
---
- Always define schema in `src/db/schema.ts` and relations in `src/db/relations.ts`
- DO NOT create migrations manually, instead run `pnpm db:generate` after schema changes to create migration files
- Apply migrations with `pnpm db:migrate` to update remote Supabase database
- Use Drizzle's type-safe query builders instead of raw SQL queries
- Implement database transactions for operations that modify multiple tables
- Add proper indexes on frequently queried columns for performance
- Keep sensitive data protected with RLS (Row Level Security) policies in Supabase
- Define enum types in schema file for consistent data validation
- Create database views for complex, frequently used queries
- `pnpm db:generate` and `pnpm db:migrate` are already created in package.json
</file>

<file path=".cursor/rules/shadcn-rules.mdc">
---
description: Guide on how to use shadcn components
globs: 
---
To add a shadcn component, use the command `pnpm dlx shadcn@latest add <component-name>`. 
It is important that the component name in the command is written in lowercase with hyphens ("-") between words. For example `pnpm dlx shadcn@latest add aspect-ratio` to add Aspect Ratio component.

List of all components:

*   Accordion
*   Alert
*   Alert Dialog
*   Aspect Ratio
*   Avatar
*   Badge
*   Breadcrumb
*   Button
*   Calendar
*   Card
*   Carousel
*   Chart
*   Checkbox
*   Collapsible
*   Combobox
*   Command
*   Context Menu
*   Data Table
*   Date Picker
*   Dialog
*   Drawer
*   Dropdown Menu
*   Form
*   Hover Card
*   Input
*   Input OTP
*   Label
*   Menubar
*   Navigation Menu
*   Pagination
*   Popover
*   Progress
*   Radio Group
*   Resizable
*   Scroll Area
*   Select
*   Separator
*   Sheet
*   Sidebar
*   Skeleton
*   Slider
*   Sonner
*   Switch
*   Table
*   Tabs
*   Textarea
*   Toast
*   Toggle
*   Toggle Group
*   Tooltip
</file>

<file path="src/components/products/customFields/customFieldsHandler.ts">
import type { Database } from "../../../types/supabase";

type Product = Database["public"]["Tables"]["products"]["Row"];

/**
 * Проверка является ли поле пользовательским по колонке или параметрам редактора
 */
export function isCustomFieldColumn(
  field: string | undefined,
  cellEditorParams: Record<string, unknown> | undefined
): boolean {
  if (!field) return false;
  return (
    field.startsWith("custom_fields.") ||
    cellEditorParams?.isCustomField === true
  );
}

/**
 * Получает имя пользовательского поля из пути колонки
 */
export function extractCustomFieldName(field: string | undefined): string {
  if (!field) return "unknown_field";

  if (field.startsWith("custom_fields.")) {
    return field.split(".")[1];
  }
  if (field.startsWith("custom_field.")) {
    return field.split(".")[1];
  }

  return field;
}

/**
 * Преобразует пользовательские поля продукта из строки в объект при необходимости
 */
export function parseProductCustomFields(
  product: Product
): Record<string, unknown> {
  if (!product.custom_fields) {
    return {};
  }

  // Если это строка, пытаемся распарсить JSON
  if (typeof product.custom_fields === "string") {
    try {
      return JSON.parse(product.custom_fields);
    } catch (e) {
      console.error("[ERROR] Не удалось распарсить строку custom_fields:", e);
      return {};
    }
  }

  // Если это объект, используем его
  if (
    typeof product.custom_fields === "object" &&
    product.custom_fields !== null
  ) {
    return product.custom_fields as Record<string, unknown>;
  }

  return {};
}

/**
 * Подготавливает значение пользовательского поля для сохранения
 */
export function prepareCustomFieldValue(
  value: unknown,
  isNumberField: boolean
): unknown {
  // Если это числовое поле и значение строка, преобразуем в число
  if (isNumberField && typeof value === "string") {
    const parsedValue = parseFloat(value);
    if (!isNaN(parsedValue)) {
      return parsedValue;
    }
  }

  return value;
}

/**
 * Обновляет пользовательское поле в объекте custom_fields
 */
export function updateCustomFieldValue(
  currentFields: Record<string, unknown>,
  fieldName: string,
  value: unknown,
  isNumberField: boolean
): Record<string, unknown> {
  const updatedValue = prepareCustomFieldValue(value, isNumberField);

  // Создаем новую копию объекта
  const newFields = { ...currentFields };
  newFields[fieldName] = updatedValue;

  return newFields;
}

/**
 * Подготавливает пользовательские поля для отправки на сервер
 */
export function prepareCustomFieldsForSave(
  customFields: Record<string, unknown>
): Record<string, unknown> {
  // Преобразуем в корректный JSON формат для Supabase
  try {
    // Глубокое клонирование объекта через JSON
    return JSON.parse(JSON.stringify(customFields));
  } catch (error) {
    console.error("Ошибка при форматировании custom_fields:", error);
    return customFields;
  }
}
</file>

<file path="src/components/products/grid/DropZone.tsx">
import { useState, useCallback } from "react";
import { Input } from "../../ui/Input";

interface DropZoneProps {
  onFilesSelected: (files: File[]) => Promise<void>;
  isLoading: boolean;
  productId: string;
}

export const DropZone = ({
  onFilesSelected,
  isLoading,
  productId,
}: DropZoneProps) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    async (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);
      const files = Array.from(e.dataTransfer.files);
      await onFilesSelected(files);
    },
    [onFilesSelected]
  );

  const handleFileSelect = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      await onFilesSelected(files);
      // Очищаем input после загрузки
      if (e.target) {
        e.target.value = "";
      }
    },
    [onFilesSelected]
  );

  return (
    <div
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      style={{
        border: `2px dashed ${isDragging ? "#2196f3" : "#ccc"}`,
        borderRadius: "4px",
        padding: "20px",
        textAlign: "center",
        backgroundColor: isDragging ? "rgba(33, 150, 243, 0.1)" : "#fafafa",
        cursor: "pointer",
        transition: "all 0.3s ease",
      }}
    >
      <Input
        type="file"
        multiple
        accept="image/*"
        onChange={handleFileSelect}
        style={{ display: "none" }}
        id={`fileInput-modal-${productId}`}
      />
      <label
        htmlFor={`fileInput-modal-${productId}`}
        style={{ cursor: "pointer" }}
      >
        {isLoading
          ? "Загрузка..."
          : "Перетащите изображения сюда или кликните для выбора"}
      </label>
    </div>
  );
};
</file>

<file path="src/components/products/grid/ImageGallery.tsx">
import { FolderItem } from "../../../hooks/useImageFolder";

interface ImageGalleryProps {
  folderContents: FolderItem[];
  onImageClick: (item: FolderItem) => void;
  isLoading: boolean;
}

export const ImageGallery = ({
  folderContents,
  onImageClick,
  isLoading,
}: ImageGalleryProps) => {
  if (isLoading) {
    return (
      <div style={{ textAlign: "center", padding: "20px" }}>
        Загрузка содержимого папки...
      </div>
    );
  }

  if (folderContents.length === 0) {
    return (
      <div style={{ textAlign: "center", padding: "10px" }}>
        Изображения не найдены
      </div>
    );
  }

  return (
    <div>
      <h3 style={{ marginBottom: "10px" }}>Изображения товара:</h3>
      <div style={{ display: "flex", flexWrap: "wrap", gap: "10px" }}>
        {folderContents.map((item) => (
          <div
            key={item.name}
            onClick={() => onImageClick(item)}
            style={{
              width: "150px",
              height: "150px",
              border: "1px solid #ccc",
              borderRadius: "4px",
              overflow: "hidden",
              cursor: "pointer",
              position: "relative",
            }}
          >
            <div
              style={{
                width: "100%",
                height: "120px",
                backgroundImage: item.preview ? `url(${item.preview})` : "none",
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundColor: "#f0f0f0",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              {!item.preview && (
                <span style={{ color: "#666", fontSize: "12px" }}>
                  Превью недоступно
                </span>
              )}
            </div>
            <div
              style={{
                padding: "5px",
                fontSize: "12px",
                textOverflow: "ellipsis",
                overflow: "hidden",
                whiteSpace: "nowrap",
              }}
            >
              {item.name}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
</file>

<file path="src/components/products/grid/ImageViewer.tsx">
interface ImageViewerProps {
  imageUrl: string;
  onBackClick: () => void;
}

export const ImageViewer = ({ imageUrl, onBackClick }: ImageViewerProps) => {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        gap: "10px",
      }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          overflow: "auto",
          maxHeight: "70vh",
          margin: "20px 0",
        }}
      >
        <img
          src={imageUrl}
          alt="Просмотр изображения"
          style={{
            maxWidth: "100%",
            maxHeight: "70vh",
            objectFit: "contain",
          }}
        />
      </div>
      <button
        onClick={onBackClick}
        style={{
          padding: "8px 16px",
          backgroundColor: "#2196f3",
          color: "white",
          border: "none",
          borderRadius: "4px",
          cursor: "pointer",
        }}
      >
        Вернуться к галерее
      </button>
    </div>
  );
};
</file>

<file path="src/components/products/grid/useProductGrid.ts">
import { useCallback, useState } from "react";
import { GridReadyEvent } from "ag-grid-community";
import type { Database } from "../../../types/supabase";

type Product = Database["public"]["Tables"]["products"]["Row"];

export function useProductGrid() {
  const [gridApi, setGridApi] = useState<GridReadyEvent["api"] | null>(null);
  const [selectedRows, setSelectedRows] = useState<Product[]>([]);

  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
  };

  const onSelectionChanged = useCallback(() => {
    const selectedNodes = gridApi?.getSelectedNodes();
    const selectedData =
      selectedNodes?.map((node) => node.data as Product) ?? [];
    setSelectedRows(selectedData);
  }, [gridApi]);

  return {
    gridApi,
    selectedRows,
    onGridReady,
    onSelectionChanged,
  };
}
</file>

<file path="src/components/products/grid/VisibilityStatusRenderer.tsx">
import { ICellRendererParams } from "ag-grid-community";

export const VisibilityStatusRenderer = (params: ICellRendererParams) => (
  <span
    className={`px-2 py-1 rounded-full text-sm ${
      params.value ? "bg-red-100 text-red-800" : "bg-green-100 text-green-800"
    }`}
  >
    {params.value ? "Hidden" : "Visible"}
  </span>
);
</file>

<file path="src/components/ui/alert.tsx">
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const alertVariants = cva(
  "relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        destructive:
          "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant, ...props }, ref) => (
  <div
    ref={ref}
    role="alert"
    className={cn(alertVariants({ variant }), className)}
    {...props}
  />
))
Alert.displayName = "Alert"

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h5
    ref={ref}
    className={cn("mb-1 font-medium leading-none tracking-tight", className)}
    {...props}
  />
))
AlertTitle.displayName = "AlertTitle"

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm [&_p]:leading-relaxed", className)}
    {...props}
  />
))
AlertDescription.displayName = "AlertDescription"

export { Alert, AlertTitle, AlertDescription }
</file>

<file path="src/components/ui/Badge.tsx">
import { cn } from "@/lib/utils";
import React, { HTMLAttributes, ReactNode } from "react";

export type BadgeVariant =
  | "default"
  | "primary"
  | "secondary"
  | "success"
  | "danger"
  | "warning"
  | "info";
export type BadgeSize = "sm" | "md" | "lg";

export interface BadgeProps extends HTMLAttributes<HTMLSpanElement> {
  children: ReactNode;
  variant?: BadgeVariant;
  size?: BadgeSize;
  className?: string;
}

export const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  (
    { children, variant = "default", size = "md", className = "", ...props },
    ref
  ) => {
    // Базовые стили для всех бейджей
    const baseStyles = "inline-flex items-center font-medium rounded-full";

    // Стили для разных вариантов
    const variantStyles = {
      default: "bg-gray-100 text-gray-800",
      primary: "bg-indigo-100 text-indigo-800",
      secondary: "bg-purple-100 text-purple-800",
      success: "bg-green-100 text-green-800",
      danger: "bg-red-100 text-red-800",
      warning: "bg-yellow-100 text-yellow-800",
      info: "bg-blue-100 text-blue-800",
    };

    // Стили для разных размеров
    const sizeStyles = {
      sm: "px-2 py-0.5 text-xs",
      md: "px-2.5 py-0.5 text-sm",
      lg: "px-3 py-1 text-base",
    };

    // Собираем все стили вместе
    const badgeStyles = cn(
      baseStyles,
      variantStyles[variant],
      sizeStyles[size],
      className
    );

    return (
      <span ref={ref} className={badgeStyles} {...props}>
        {children}
      </span>
    );
  }
);

Badge.displayName = "Badge";
</file>

<file path="src/components/ui/Card.tsx">
import React, { HTMLAttributes, ReactNode } from "react";
import { cn } from "@/lib/utils";

export interface CardProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  className?: string;
}

export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "bg-white rounded-lg shadow-md overflow-hidden",
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = "Card";

export interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  className?: string;
}

export const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("px-4 py-5 sm:px-6 border-b border-gray-200", className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardHeader.displayName = "CardHeader";

export interface CardTitleProps extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
  className?: string;
}

export const CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <h3
        ref={ref}
        className={cn("text-lg leading-6 font-medium text-gray-900", className)}
        {...props}
      >
        {children}
      </h3>
    );
  }
);

CardTitle.displayName = "CardTitle";

export interface CardDescriptionProps
  extends HTMLAttributes<HTMLParagraphElement> {
  children: ReactNode;
  className?: string;
}

export const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  CardDescriptionProps
>(({ children, className, ...props }, ref) => {
  return (
    <p
      ref={ref}
      className={cn("mt-1 max-w-2xl text-sm text-gray-500", className)}
      {...props}
    >
      {children}
    </p>
  );
});

CardDescription.displayName = "CardDescription";

export interface CardContentProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  className?: string;
}

export const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div ref={ref} className={cn("px-4 py-5 sm:p-6", className)} {...props}>
        {children}
      </div>
    );
  }
);

CardContent.displayName = "CardContent";

export interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  className?: string;
}

export const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("px-4 py-4 sm:px-6 border-t border-gray-200", className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardFooter.displayName = "CardFooter";
</file>

<file path="src/components/ui/carousel.tsx">
import * as React from "react"
import useEmblaCarousel, {
  type UseEmblaCarouselType,
} from "embla-carousel-react"
import { ArrowLeft, ArrowRight } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

type CarouselApi = UseEmblaCarouselType[1]
type UseCarouselParameters = Parameters<typeof useEmblaCarousel>
type CarouselOptions = UseCarouselParameters[0]
type CarouselPlugin = UseCarouselParameters[1]

type CarouselProps = {
  opts?: CarouselOptions
  plugins?: CarouselPlugin
  orientation?: "horizontal" | "vertical"
  setApi?: (api: CarouselApi) => void
}

type CarouselContextProps = {
  carouselRef: ReturnType<typeof useEmblaCarousel>[0]
  api: ReturnType<typeof useEmblaCarousel>[1]
  scrollPrev: () => void
  scrollNext: () => void
  canScrollPrev: boolean
  canScrollNext: boolean
} & CarouselProps

const CarouselContext = React.createContext<CarouselContextProps | null>(null)

function useCarousel() {
  const context = React.useContext(CarouselContext)

  if (!context) {
    throw new Error("useCarousel must be used within a <Carousel />")
  }

  return context
}

const Carousel = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & CarouselProps
>(
  (
    {
      orientation = "horizontal",
      opts,
      setApi,
      plugins,
      className,
      children,
      ...props
    },
    ref
  ) => {
    const [carouselRef, api] = useEmblaCarousel(
      {
        ...opts,
        axis: orientation === "horizontal" ? "x" : "y",
      },
      plugins
    )
    const [canScrollPrev, setCanScrollPrev] = React.useState(false)
    const [canScrollNext, setCanScrollNext] = React.useState(false)

    const onSelect = React.useCallback((api: CarouselApi) => {
      if (!api) {
        return
      }

      setCanScrollPrev(api.canScrollPrev())
      setCanScrollNext(api.canScrollNext())
    }, [])

    const scrollPrev = React.useCallback(() => {
      api?.scrollPrev()
    }, [api])

    const scrollNext = React.useCallback(() => {
      api?.scrollNext()
    }, [api])

    const handleKeyDown = React.useCallback(
      (event: React.KeyboardEvent<HTMLDivElement>) => {
        if (event.key === "ArrowLeft") {
          event.preventDefault()
          scrollPrev()
        } else if (event.key === "ArrowRight") {
          event.preventDefault()
          scrollNext()
        }
      },
      [scrollPrev, scrollNext]
    )

    React.useEffect(() => {
      if (!api || !setApi) {
        return
      }

      setApi(api)
    }, [api, setApi])

    React.useEffect(() => {
      if (!api) {
        return
      }

      onSelect(api)
      api.on("reInit", onSelect)
      api.on("select", onSelect)

      return () => {
        api?.off("select", onSelect)
      }
    }, [api, onSelect])

    return (
      <CarouselContext.Provider
        value={{
          carouselRef,
          api: api,
          opts,
          orientation:
            orientation || (opts?.axis === "y" ? "vertical" : "horizontal"),
          scrollPrev,
          scrollNext,
          canScrollPrev,
          canScrollNext,
        }}
      >
        <div
          ref={ref}
          onKeyDownCapture={handleKeyDown}
          className={cn("relative", className)}
          role="region"
          aria-roledescription="carousel"
          {...props}
        >
          {children}
        </div>
      </CarouselContext.Provider>
    )
  }
)
Carousel.displayName = "Carousel"

const CarouselContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { carouselRef, orientation } = useCarousel()

  return (
    <div ref={carouselRef} className="overflow-hidden">
      <div
        ref={ref}
        className={cn(
          "flex",
          orientation === "horizontal" ? "-ml-4" : "-mt-4 flex-col",
          className
        )}
        {...props}
      />
    </div>
  )
})
CarouselContent.displayName = "CarouselContent"

const CarouselItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { orientation } = useCarousel()

  return (
    <div
      ref={ref}
      role="group"
      aria-roledescription="slide"
      className={cn(
        "min-w-0 shrink-0 grow-0 basis-full",
        orientation === "horizontal" ? "pl-4" : "pt-4",
        className
      )}
      {...props}
    />
  )
})
CarouselItem.displayName = "CarouselItem"

const CarouselPrevious = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof Button>
>(({ className, variant = "outline", size = "icon", ...props }, ref) => {
  const { orientation, scrollPrev, canScrollPrev } = useCarousel()

  return (
    <Button
      ref={ref}
      variant={variant}
      size={size}
      className={cn(
        "absolute  h-8 w-8 rounded-full",
        orientation === "horizontal"
          ? "-left-12 top-1/2 -translate-y-1/2"
          : "-top-12 left-1/2 -translate-x-1/2 rotate-90",
        className
      )}
      disabled={!canScrollPrev}
      onClick={scrollPrev}
      {...props}
    >
      <ArrowLeft className="h-4 w-4" />
      <span className="sr-only">Previous slide</span>
    </Button>
  )
})
CarouselPrevious.displayName = "CarouselPrevious"

const CarouselNext = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof Button>
>(({ className, variant = "outline", size = "icon", ...props }, ref) => {
  const { orientation, scrollNext, canScrollNext } = useCarousel()

  return (
    <Button
      ref={ref}
      variant={variant}
      size={size}
      className={cn(
        "absolute h-8 w-8 rounded-full",
        orientation === "horizontal"
          ? "-right-12 top-1/2 -translate-y-1/2"
          : "-bottom-12 left-1/2 -translate-x-1/2 rotate-90",
        className
      )}
      disabled={!canScrollNext}
      onClick={scrollNext}
      {...props}
    >
      <ArrowRight className="h-4 w-4" />
      <span className="sr-only">Next slide</span>
    </Button>
  )
})
CarouselNext.displayName = "CarouselNext"

export {
  type CarouselApi,
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
}
</file>

<file path="src/components/ui/checkbox.tsx">
import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { Check } from "lucide-react"

import { cn } from "@/lib/utils"

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={cn(
      "peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
      className
    )}
    {...props}
  >
    <CheckboxPrimitive.Indicator
      className={cn("flex items-center justify-center text-current")}
    >
      <Check className="h-4 w-4" />
    </CheckboxPrimitive.Indicator>
  </CheckboxPrimitive.Root>
))
Checkbox.displayName = CheckboxPrimitive.Root.displayName

export { Checkbox }
</file>

<file path="src/components/ui/dialog.tsx">
import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

const Dialog = DialogPrimitive.Root

const DialogTrigger = DialogPrimitive.Trigger

const DialogPortal = DialogPrimitive.Portal

const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
        className
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
))
DialogContent.displayName = DialogPrimitive.Content.displayName

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = "DialogHeader"

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = "DialogFooter"

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
DialogTitle.displayName = DialogPrimitive.Title.displayName

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
DialogDescription.displayName = DialogPrimitive.Description.displayName

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogTrigger,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
}
</file>

<file path="src/components/ui/Divider.tsx">
import { cn } from "@/lib/utils";
import React, { HTMLAttributes } from "react";

export type DividerOrientation = "horizontal" | "vertical";
export type DividerVariant = "solid" | "dashed" | "dotted";

export interface DividerProps extends HTMLAttributes<HTMLDivElement> {
  orientation?: DividerOrientation;
  variant?: DividerVariant;
  className?: string;
  label?: string;
}

export const Divider = React.forwardRef<HTMLDivElement, DividerProps>(
  (
    {
      orientation = "horizontal",
      variant = "solid",
      className = "",
      label,
      ...props
    },
    ref
  ) => {
    // Базовые стили для разделителя
    const baseStyles = {
      horizontal: "w-full my-4",
      vertical: "h-full mx-2",
    };

    // Стили для разных вариантов
    const variantStyles = {
      solid: "border-solid",
      dashed: "border-dashed",
      dotted: "border-dotted",
    };

    // Стили для ориентации
    const orientationStyles = {
      horizontal: "border-t border-gray-200",
      vertical: "border-l border-gray-200 h-full",
    };

    // Собираем все стили вместе
    const dividerStyles = cn(
      baseStyles[orientation],
      variantStyles[variant],
      orientationStyles[orientation],
      className
    );

    // Если есть метка, то отображаем разделитель с меткой
    if (label && orientation === "horizontal") {
      return (
        <div className="relative" ref={ref} {...props}>
          <div className="absolute inset-0 flex items-center">
            <div className={dividerStyles}></div>
          </div>
          <div className="relative flex justify-center">
            <span className="bg-white px-2 text-sm text-gray-500">{label}</span>
          </div>
        </div>
      );
    }

    return <div ref={ref} className={dividerStyles} {...props} />;
  }
);

Divider.displayName = "Divider";
</file>

<file path="src/components/ui/dropdown-menu.tsx">
import * as React from "react"
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu"
import { Check, ChevronRight, Circle } from "lucide-react"

import { cn } from "@/lib/utils"

const DropdownMenu = DropdownMenuPrimitive.Root

const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger

const DropdownMenuGroup = DropdownMenuPrimitive.Group

const DropdownMenuPortal = DropdownMenuPrimitive.Portal

const DropdownMenuSub = DropdownMenuPrimitive.Sub

const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup

const DropdownMenuSubTrigger = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {
    inset?: boolean
  }
>(({ className, inset, children, ...props }, ref) => (
  <DropdownMenuPrimitive.SubTrigger
    ref={ref}
    className={cn(
      "flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
      inset && "pl-8",
      className
    )}
    {...props}
  >
    {children}
    <ChevronRight className="ml-auto" />
  </DropdownMenuPrimitive.SubTrigger>
))
DropdownMenuSubTrigger.displayName =
  DropdownMenuPrimitive.SubTrigger.displayName

const DropdownMenuSubContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>
>(({ className, ...props }, ref) => (
  <DropdownMenuPrimitive.SubContent
    ref={ref}
    className={cn(
      "z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className
    )}
    {...props}
  />
))
DropdownMenuSubContent.displayName =
  DropdownMenuPrimitive.SubContent.displayName

const DropdownMenuContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <DropdownMenuPrimitive.Portal>
    <DropdownMenuPrimitive.Content
      ref={ref}
      sideOffset={sideOffset}
      className={cn(
        "z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md",
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        className
      )}
      {...props}
    />
  </DropdownMenuPrimitive.Portal>
))
DropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName

const DropdownMenuItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <DropdownMenuPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",
      inset && "pl-8",
      className
    )}
    {...props}
  />
))
DropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName

const DropdownMenuCheckboxItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>
>(({ className, children, checked, ...props }, ref) => (
  <DropdownMenuPrimitive.CheckboxItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    checked={checked}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <Check className="h-4 w-4" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </DropdownMenuPrimitive.CheckboxItem>
))
DropdownMenuCheckboxItem.displayName =
  DropdownMenuPrimitive.CheckboxItem.displayName

const DropdownMenuRadioItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>
>(({ className, children, ...props }, ref) => (
  <DropdownMenuPrimitive.RadioItem
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  >
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <Circle className="h-2 w-2 fill-current" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </DropdownMenuPrimitive.RadioItem>
))
DropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName

const DropdownMenuLabel = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {
    inset?: boolean
  }
>(({ className, inset, ...props }, ref) => (
  <DropdownMenuPrimitive.Label
    ref={ref}
    className={cn(
      "px-2 py-1.5 text-sm font-semibold",
      inset && "pl-8",
      className
    )}
    {...props}
  />
))
DropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName

const DropdownMenuSeparator = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <DropdownMenuPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-muted", className)}
    {...props}
  />
))
DropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName

const DropdownMenuShortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn("ml-auto text-xs tracking-widest opacity-60", className)}
      {...props}
    />
  )
}
DropdownMenuShortcut.displayName = "DropdownMenuShortcut"

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
}
</file>

<file path="src/components/ui/IconButton.tsx">
import React, { ButtonHTMLAttributes, ReactNode } from "react";
import { cn } from "@/lib/utils";

export type IconButtonVariant =
  | "primary"
  | "secondary"
  | "danger"
  | "success"
  | "outline"
  | "ghost";
export type IconButtonSize = "sm" | "md" | "lg";

export interface IconButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement> {
  icon: ReactNode;
  variant?: IconButtonVariant;
  size?: IconButtonSize;
  isLoading?: boolean;
  tooltip?: string;
  className?: string;
}

export const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  (
    {
      icon,
      variant = "secondary",
      size = "md",
      isLoading = false,
      tooltip,
      className = "",
      disabled,
      ...props
    },
    ref
  ) => {
    // Базовые стили для всех кнопок
    const baseStyles =
      "inline-flex items-center justify-center rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors";

    // Стили для разных вариантов
    const variantStyles = {
      primary:
        "bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500 border border-transparent",
      secondary:
        "bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500 border border-gray-300",
      danger:
        "bg-white text-red-700 hover:bg-red-50 focus:ring-red-500 border border-red-300",
      success:
        "bg-white text-green-700 hover:bg-green-50 focus:ring-green-500 border border-green-300",
      outline:
        "bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500 border border-gray-300",
      ghost:
        "bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500 border-none",
    };

    // Стили для разных размеров
    const sizeStyles = {
      sm: "p-1",
      md: "p-2",
      lg: "p-3",
    };

    // Стили для состояния disabled
    const disabledStyles = "opacity-50 cursor-not-allowed";

    // Собираем все стили вместе
    const buttonStyles = cn(
      baseStyles,
      variantStyles[variant],
      sizeStyles[size],
      isLoading || disabled ? disabledStyles : "",
      className
    );

    return (
      <button
        ref={ref}
        className={buttonStyles}
        disabled={isLoading || disabled}
        title={tooltip}
        {...props}
      >
        {isLoading ? (
          <svg
            className="animate-spin h-5 w-5 text-current"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        ) : (
          icon
        )}
      </button>
    );
  }
);

IconButton.displayName = "IconButton";
</file>

<file path="src/components/ui/label.tsx">
import * as React from "react"
import * as LabelPrimitive from "@radix-ui/react-label"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const labelVariants = cva(
  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
)

const Label = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &
    VariantProps<typeof labelVariants>
>(({ className, ...props }, ref) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(labelVariants(), className)}
    {...props}
  />
))
Label.displayName = LabelPrimitive.Root.displayName

export { Label }
</file>

<file path="src/components/ui/scroll-area.tsx">
import * as React from "react"
import * as ScrollAreaPrimitive from "@radix-ui/react-scroll-area"

import { cn } from "@/lib/utils"

const ScrollArea = React.forwardRef<
  React.ElementRef<typeof ScrollAreaPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>
>(({ className, children, ...props }, ref) => (
  <ScrollAreaPrimitive.Root
    ref={ref}
    className={cn("relative overflow-hidden", className)}
    {...props}
  >
    <ScrollAreaPrimitive.Viewport className="h-full w-full rounded-[inherit]">
      {children}
    </ScrollAreaPrimitive.Viewport>
    <ScrollBar />
    <ScrollAreaPrimitive.Corner />
  </ScrollAreaPrimitive.Root>
))
ScrollArea.displayName = ScrollAreaPrimitive.Root.displayName

const ScrollBar = React.forwardRef<
  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,
  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>
>(({ className, orientation = "vertical", ...props }, ref) => (
  <ScrollAreaPrimitive.ScrollAreaScrollbar
    ref={ref}
    orientation={orientation}
    className={cn(
      "flex touch-none select-none transition-colors",
      orientation === "vertical" &&
        "h-full w-2.5 border-l border-l-transparent p-[1px]",
      orientation === "horizontal" &&
        "h-2.5 flex-col border-t border-t-transparent p-[1px]",
      className
    )}
    {...props}
  >
    <ScrollAreaPrimitive.ScrollAreaThumb className="relative flex-1 rounded-full bg-border" />
  </ScrollAreaPrimitive.ScrollAreaScrollbar>
))
ScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName

export { ScrollArea, ScrollBar }
</file>

<file path="src/components/ui/select.tsx">
import * as React from "react"
import * as SelectPrimitive from "@radix-ui/react-select"
import { Check, ChevronDown, ChevronUp } from "lucide-react"

import { cn } from "@/lib/utils"

const Select = SelectPrimitive.Root

const SelectGroup = SelectPrimitive.Group

const SelectValue = SelectPrimitive.Value

const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Trigger
    ref={ref}
    className={cn(
      "flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
      className
    )}
    {...props}
  >
    {children}
    <SelectPrimitive.Icon asChild>
      <ChevronDown className="h-4 w-4 opacity-50" />
    </SelectPrimitive.Icon>
  </SelectPrimitive.Trigger>
))
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName

const SelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronUp className="h-4 w-4" />
  </SelectPrimitive.ScrollUpButton>
))
SelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName

const SelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronDown className="h-4 w-4" />
  </SelectPrimitive.ScrollDownButton>
))
SelectScrollDownButton.displayName =
  SelectPrimitive.ScrollDownButton.displayName

const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = "popper", ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        "relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]",
        position === "popper" &&
          "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
        className
      )}
      position={position}
      {...props}
    >
      <SelectScrollUpButton />
      <SelectPrimitive.Viewport
        className={cn(
          "p-1",
          position === "popper" &&
            "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"
        )}
      >
        {children}
      </SelectPrimitive.Viewport>
      <SelectScrollDownButton />
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
))
SelectContent.displayName = SelectPrimitive.Content.displayName

const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn("px-2 py-1.5 text-sm font-semibold", className)}
    {...props}
  />
))
SelectLabel.displayName = SelectPrimitive.Label.displayName

const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  >
    <span className="absolute right-2 flex h-3.5 w-3.5 items-center justify-center">
      <SelectPrimitive.ItemIndicator>
        <Check className="h-4 w-4" />
      </SelectPrimitive.ItemIndicator>
    </span>
    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
))
SelectItem.displayName = SelectPrimitive.Item.displayName

const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-muted", className)}
    {...props}
  />
))
SelectSeparator.displayName = SelectPrimitive.Separator.displayName

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
}
</file>

<file path="src/components/ui/sheet.tsx">
import * as React from "react"
import * as SheetPrimitive from "@radix-ui/react-dialog"
import { cva, type VariantProps } from "class-variance-authority"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

const Sheet = SheetPrimitive.Root

const SheetTrigger = SheetPrimitive.Trigger

const SheetClose = SheetPrimitive.Close

const SheetPortal = SheetPrimitive.Portal

const SheetOverlay = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <SheetPrimitive.Overlay
    className={cn(
      "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
    ref={ref}
  />
))
SheetOverlay.displayName = SheetPrimitive.Overlay.displayName

const sheetVariants = cva(
  "fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",
  {
    variants: {
      side: {
        top: "inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",
        bottom:
          "inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",
        left: "inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",
        right:
          "inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm",
      },
    },
    defaultVariants: {
      side: "right",
    },
  }
)

interface SheetContentProps
  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,
    VariantProps<typeof sheetVariants> {}

const SheetContent = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Content>,
  SheetContentProps
>(({ side = "right", className, children, ...props }, ref) => (
  <SheetPortal>
    <SheetOverlay />
    <SheetPrimitive.Content
      ref={ref}
      className={cn(sheetVariants({ side }), className)}
      {...props}
    >
      <SheetPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </SheetPrimitive.Close>
      {children}
    </SheetPrimitive.Content>
  </SheetPortal>
))
SheetContent.displayName = SheetPrimitive.Content.displayName

const SheetHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-2 text-center sm:text-left",
      className
    )}
    {...props}
  />
)
SheetHeader.displayName = "SheetHeader"

const SheetFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
)
SheetFooter.displayName = "SheetFooter"

const SheetTitle = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>
>(({ className, ...props }, ref) => (
  <SheetPrimitive.Title
    ref={ref}
    className={cn("text-lg font-semibold text-foreground", className)}
    {...props}
  />
))
SheetTitle.displayName = SheetPrimitive.Title.displayName

const SheetDescription = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>
>(({ className, ...props }, ref) => (
  <SheetPrimitive.Description
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
SheetDescription.displayName = SheetPrimitive.Description.displayName

export {
  Sheet,
  SheetPortal,
  SheetOverlay,
  SheetTrigger,
  SheetClose,
  SheetContent,
  SheetHeader,
  SheetFooter,
  SheetTitle,
  SheetDescription,
}
</file>

<file path="src/components/ui/switch.tsx">
import * as React from "react"
import * as SwitchPrimitives from "@radix-ui/react-switch"

import { cn } from "@/lib/utils"

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      "peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",
      className
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        "pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"
      )}
    />
  </SwitchPrimitives.Root>
))
Switch.displayName = SwitchPrimitives.Root.displayName

export { Switch }
</file>

<file path="src/components/ui/toaster.tsx">
import { useToast } from "@/hooks/use-toast"
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        return (
          <Toast key={id} {...props}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription>{description}</ToastDescription>
              )}
            </div>
            {action}
            <ToastClose />
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}
</file>

<file path="src/components/ui/tooltip.tsx">
import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"

import { cn } from "@/lib/utils"

const TooltipProvider = TooltipPrimitive.Provider

const Tooltip = TooltipPrimitive.Root

const TooltipTrigger = TooltipPrimitive.Trigger

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Portal>
    <TooltipPrimitive.Content
      ref={ref}
      sideOffset={sideOffset}
      className={cn(
        "z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        className
      )}
      {...props}
    />
  </TooltipPrimitive.Portal>
))
TooltipContent.displayName = TooltipPrimitive.Content.displayName

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }
</file>

<file path="src/components/ProtectedRoute.tsx">
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../lib/auth";

export function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Пропускаем выполнение, если идет загрузка данных
    if (loading) return;

    // Если пользователь не авторизован, перенаправляем на страницу входа
    if (!user) {
      navigate("/login", {
        state: {
          from: location.pathname, // Сохраняем текущий путь для возврата после авторизации
        },
      });
    }
  }, [user, loading, navigate, location]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return <>{children}</>;
}
</file>

<file path="src/db/meta/0000_snapshot.json">
{
  "id": "00000000-0000-0000-0000-000000000000",
  "prevId": "",
  "version": "7",
  "dialect": "postgresql",
  "tables": {
    "public.product_images": {
      "name": "product_images",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "product_id": {
          "name": "product_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": true
        },
        "yandex_disk_path": {
          "name": "yandex_disk_path",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "public_url": {
          "name": "public_url",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "sort_order": {
          "name": "sort_order",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_main": {
          "name": "is_main",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {
        "product_images_product_id_fkey": {
          "name": "product_images_product_id_fkey",
          "tableFrom": "product_images",
          "tableTo": "products",
          "schemaTo": "public",
          "columnsFrom": [
            "product_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "cascade",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "product_images_product_id_sort_order_key": {
          "columns": [
            "product_id",
            "sort_order"
          ],
          "nullsNotDistinct": false,
          "name": "product_images_product_id_sort_order_key"
        }
      },
      "checkConstraints": {},
      "policies": {
        "Authenticated users can manage product images": {
          "name": "Authenticated users can manage product images",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "withCheck": "true",
          "using": "true"
        },
        "Public read access for product images": {
          "name": "Public read access for product images",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        }
      },
      "isRLSEnabled": true
    },
    "public.products": {
      "name": "products",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "weight": {
          "name": "weight",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "price": {
          "name": "price",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "validity_period": {
          "name": "validity_period",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_hidden": {
          "name": "is_hidden",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "custom_fields": {
          "name": "custom_fields",
          "type": "jsonb",
          "primaryKey": false,
          "notNull": false,
          "default": "'{}'::jsonb"
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "updated_at": {
          "name": "updated_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "checkConstraints": {
        "products_price_check": {
          "name": "products_price_check",
          "value": "price >= (0)::numeric"
        },
        "products_validity_period_check": {
          "name": "products_validity_period_check",
          "value": "validity_period >= 0"
        },
        "products_weight_check": {
          "name": "products_weight_check",
          "value": "weight >= (0)::numeric"
        }
      },
      "policies": {
        "Authenticated users can create products": {
          "name": "Authenticated users can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "authenticated"
          ],
          "withCheck": "true"
        },
        "Authenticated users can read products": {
          "name": "Authenticated users can read products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "authenticated"
          ]
        },
        "Authenticated users can delete products": {
          "name": "Authenticated users can delete products",
          "as": "PERMISSIVE",
          "for": "DELETE",
          "to": [
            "public"
          ]
        },
        "Authenticated users can update products": {
          "name": "Authenticated users can update products",
          "as": "PERMISSIVE",
          "for": "UPDATE",
          "to": [
            "public"
          ]
        },
        "Public read access for non-hidden products": {
          "name": "Public read access for non-hidden products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Anyone can create products": {
          "name": "Anyone can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ]
        }
      },
      "isRLSEnabled": true
    }
  },
  "enums": {
    "public.column_type": {
      "name": "column_type",
      "values": [
        "text",
        "number"
      ],
      "schema": "public"
    }
  },
  "schemas": {},
  "sequences": {},
  "roles": {},
  "policies": {},
  "views": {},
  "_meta": {
    "schemas": {},
    "tables": {},
    "columns": {}
  },
  "internal": {
    "tables": {}
  }
}
</file>

<file path="src/db/meta/0001_snapshot.json">
{
  "id": "eff04783-9b5d-483a-af9c-c869361553b9",
  "prevId": "00000000-0000-0000-0000-000000000000",
  "version": "7",
  "dialect": "postgresql",
  "tables": {
    "public.column_settings": {
      "name": "column_settings",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "display_name": {
          "name": "display_name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_visible": {
          "name": "is_visible",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": true
        },
        "data_type": {
          "name": "data_type",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "column_settings_name_unique": {
          "name": "column_settings_name_unique",
          "nullsNotDistinct": false,
          "columns": [
            "name"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage column settings": {
          "name": "Authenticated users can manage column settings",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for column settings": {
          "name": "Public read access for column settings",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_images": {
      "name": "product_images",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "product_id": {
          "name": "product_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": true
        },
        "yandex_disk_path": {
          "name": "yandex_disk_path",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "public_url": {
          "name": "public_url",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "sort_order": {
          "name": "sort_order",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_main": {
          "name": "is_main",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {
        "product_images_product_id_fkey": {
          "name": "product_images_product_id_fkey",
          "tableFrom": "product_images",
          "tableTo": "products",
          "columnsFrom": [
            "product_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "cascade",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "product_images_product_id_sort_order_key": {
          "name": "product_images_product_id_sort_order_key",
          "nullsNotDistinct": false,
          "columns": [
            "product_id",
            "sort_order"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage product images": {
          "name": "Authenticated users can manage product images",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for product images": {
          "name": "Public read access for product images",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.products": {
      "name": "products",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "weight": {
          "name": "weight",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "price": {
          "name": "price",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "validity_period": {
          "name": "validity_period",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_hidden": {
          "name": "is_hidden",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "custom_fields": {
          "name": "custom_fields",
          "type": "jsonb",
          "primaryKey": false,
          "notNull": false,
          "default": "'{}'::jsonb"
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "updated_at": {
          "name": "updated_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can create products": {
          "name": "Authenticated users can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "authenticated"
          ],
          "withCheck": "true"
        },
        "Authenticated users can read products": {
          "name": "Authenticated users can read products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "authenticated"
          ]
        },
        "Authenticated users can delete products": {
          "name": "Authenticated users can delete products",
          "as": "PERMISSIVE",
          "for": "DELETE",
          "to": [
            "public"
          ]
        },
        "Authenticated users can update products": {
          "name": "Authenticated users can update products",
          "as": "PERMISSIVE",
          "for": "UPDATE",
          "to": [
            "public"
          ]
        },
        "Public read access for non-hidden products": {
          "name": "Public read access for non-hidden products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Anyone can create products": {
          "name": "Anyone can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {
        "products_price_check": {
          "name": "products_price_check",
          "value": "price >= (0)::numeric"
        },
        "products_validity_period_check": {
          "name": "products_validity_period_check",
          "value": "validity_period >= 0"
        },
        "products_weight_check": {
          "name": "products_weight_check",
          "value": "weight >= (0)::numeric"
        }
      },
      "isRLSEnabled": false
    }
  },
  "enums": {
    "public.column_type": {
      "name": "column_type",
      "schema": "public",
      "values": [
        "text",
        "number"
      ]
    }
  },
  "schemas": {},
  "sequences": {},
  "roles": {},
  "policies": {},
  "views": {},
  "_meta": {
    "columns": {},
    "schemas": {},
    "tables": {}
  }
}
</file>

<file path="src/db/meta/0002_snapshot.json">
{
  "id": "5d9b3fbe-8cd4-47ea-8e96-27dd592da043",
  "prevId": "eff04783-9b5d-483a-af9c-c869361553b9",
  "version": "7",
  "dialect": "postgresql",
  "tables": {
    "public.column_settings": {
      "name": "column_settings",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "display_name": {
          "name": "display_name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_visible": {
          "name": "is_visible",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": true
        },
        "data_type": {
          "name": "data_type",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "column_settings_name_unique": {
          "name": "column_settings_name_unique",
          "nullsNotDistinct": false,
          "columns": [
            "name"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage column settings": {
          "name": "Authenticated users can manage column settings",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for column settings": {
          "name": "Public read access for column settings",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Allow public insertion for initialization": {
          "name": "Allow public insertion for initialization",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ],
          "withCheck": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_images": {
      "name": "product_images",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "product_id": {
          "name": "product_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": true
        },
        "yandex_disk_path": {
          "name": "yandex_disk_path",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "public_url": {
          "name": "public_url",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "sort_order": {
          "name": "sort_order",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_main": {
          "name": "is_main",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {
        "product_images_product_id_fkey": {
          "name": "product_images_product_id_fkey",
          "tableFrom": "product_images",
          "tableTo": "products",
          "columnsFrom": [
            "product_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "cascade",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "product_images_product_id_sort_order_key": {
          "name": "product_images_product_id_sort_order_key",
          "nullsNotDistinct": false,
          "columns": [
            "product_id",
            "sort_order"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage product images": {
          "name": "Authenticated users can manage product images",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for product images": {
          "name": "Public read access for product images",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.products": {
      "name": "products",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "weight": {
          "name": "weight",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "price": {
          "name": "price",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "validity_period": {
          "name": "validity_period",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_hidden": {
          "name": "is_hidden",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "custom_fields": {
          "name": "custom_fields",
          "type": "jsonb",
          "primaryKey": false,
          "notNull": false,
          "default": "'{}'::jsonb"
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "updated_at": {
          "name": "updated_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can create products": {
          "name": "Authenticated users can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "authenticated"
          ],
          "withCheck": "true"
        },
        "Authenticated users can read products": {
          "name": "Authenticated users can read products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "authenticated"
          ]
        },
        "Authenticated users can delete products": {
          "name": "Authenticated users can delete products",
          "as": "PERMISSIVE",
          "for": "DELETE",
          "to": [
            "public"
          ]
        },
        "Authenticated users can update products": {
          "name": "Authenticated users can update products",
          "as": "PERMISSIVE",
          "for": "UPDATE",
          "to": [
            "public"
          ]
        },
        "Public read access for non-hidden products": {
          "name": "Public read access for non-hidden products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Anyone can create products": {
          "name": "Anyone can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {
        "products_price_check": {
          "name": "products_price_check",
          "value": "price >= (0)::numeric"
        },
        "products_validity_period_check": {
          "name": "products_validity_period_check",
          "value": "validity_period >= 0"
        },
        "products_weight_check": {
          "name": "products_weight_check",
          "value": "weight >= (0)::numeric"
        }
      },
      "isRLSEnabled": false
    }
  },
  "enums": {
    "public.column_type": {
      "name": "column_type",
      "schema": "public",
      "values": [
        "text",
        "number"
      ]
    }
  },
  "schemas": {},
  "sequences": {},
  "roles": {},
  "policies": {},
  "views": {},
  "_meta": {
    "columns": {},
    "schemas": {},
    "tables": {}
  }
}
</file>

<file path="src/db/meta/0003_snapshot.json">
{
  "id": "f1d25ff9-ae4b-483a-a5e4-04ab8521f6a5",
  "prevId": "5d9b3fbe-8cd4-47ea-8e96-27dd592da043",
  "version": "7",
  "dialect": "postgresql",
  "tables": {
    "public.column_settings": {
      "name": "column_settings",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "display_name": {
          "name": "display_name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_visible": {
          "name": "is_visible",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": true
        },
        "data_type": {
          "name": "data_type",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "column_settings_name_unique": {
          "name": "column_settings_name_unique",
          "nullsNotDistinct": false,
          "columns": [
            "name"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage column settings": {
          "name": "Authenticated users can manage column settings",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for column settings": {
          "name": "Public read access for column settings",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Allow public insertion for initialization": {
          "name": "Allow public insertion for initialization",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ],
          "withCheck": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_groups": {
      "name": "product_groups",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": true,
          "default": "now()"
        }
      },
      "indexes": {
        "idx_product_groups_order_index": {
          "name": "idx_product_groups_order_index",
          "columns": [
            {
              "expression": "order_index",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            }
          ],
          "isUnique": false,
          "concurrently": false,
          "method": "btree",
          "with": {}
        }
      },
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can manage groups": {
          "name": "Authenticated users can manage groups",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for groups": {
          "name": "Public read access for groups",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ],
          "using": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_images": {
      "name": "product_images",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "product_id": {
          "name": "product_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": true
        },
        "yandex_disk_path": {
          "name": "yandex_disk_path",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "public_url": {
          "name": "public_url",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "sort_order": {
          "name": "sort_order",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_main": {
          "name": "is_main",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {
        "product_images_product_id_fkey": {
          "name": "product_images_product_id_fkey",
          "tableFrom": "product_images",
          "tableTo": "products",
          "columnsFrom": [
            "product_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "cascade",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "product_images_product_id_sort_order_key": {
          "name": "product_images_product_id_sort_order_key",
          "nullsNotDistinct": false,
          "columns": [
            "product_id",
            "sort_order"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage product images": {
          "name": "Authenticated users can manage product images",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for product images": {
          "name": "Public read access for product images",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.products": {
      "name": "products",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "weight": {
          "name": "weight",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "price": {
          "name": "price",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "validity_period": {
          "name": "validity_period",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_hidden": {
          "name": "is_hidden",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "custom_fields": {
          "name": "custom_fields",
          "type": "jsonb",
          "primaryKey": false,
          "notNull": false,
          "default": "'{}'::jsonb"
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "updated_at": {
          "name": "updated_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "group_id": {
          "name": "group_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": false
        }
      },
      "indexes": {},
      "foreignKeys": {
        "products_group_id_product_groups_id_fk": {
          "name": "products_group_id_product_groups_id_fk",
          "tableFrom": "products",
          "tableTo": "product_groups",
          "columnsFrom": [
            "group_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "set null",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can create products": {
          "name": "Authenticated users can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "authenticated"
          ],
          "withCheck": "true"
        },
        "Authenticated users can read products": {
          "name": "Authenticated users can read products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "authenticated"
          ]
        },
        "Authenticated users can delete products": {
          "name": "Authenticated users can delete products",
          "as": "PERMISSIVE",
          "for": "DELETE",
          "to": [
            "public"
          ]
        },
        "Authenticated users can update products": {
          "name": "Authenticated users can update products",
          "as": "PERMISSIVE",
          "for": "UPDATE",
          "to": [
            "public"
          ]
        },
        "Public read access for non-hidden products": {
          "name": "Public read access for non-hidden products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Anyone can create products": {
          "name": "Anyone can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {
        "products_price_check": {
          "name": "products_price_check",
          "value": "price >= (0)::numeric"
        },
        "products_validity_period_check": {
          "name": "products_validity_period_check",
          "value": "validity_period >= 0"
        },
        "products_weight_check": {
          "name": "products_weight_check",
          "value": "weight >= (0)::numeric"
        }
      },
      "isRLSEnabled": false
    }
  },
  "enums": {
    "public.column_type": {
      "name": "column_type",
      "schema": "public",
      "values": [
        "text",
        "number"
      ]
    }
  },
  "schemas": {},
  "sequences": {},
  "roles": {},
  "policies": {},
  "views": {},
  "_meta": {
    "columns": {},
    "schemas": {},
    "tables": {}
  }
}
</file>

<file path="src/db/meta/0004_snapshot.json">
{
  "id": "baf5a797-5cf3-43c6-8310-817a73b43865",
  "prevId": "f1d25ff9-ae4b-483a-a5e4-04ab8521f6a5",
  "version": "7",
  "dialect": "postgresql",
  "tables": {
    "public.column_settings": {
      "name": "column_settings",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "display_name": {
          "name": "display_name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_visible": {
          "name": "is_visible",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": true
        },
        "data_type": {
          "name": "data_type",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "column_settings_name_unique": {
          "name": "column_settings_name_unique",
          "nullsNotDistinct": false,
          "columns": [
            "name"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage column settings": {
          "name": "Authenticated users can manage column settings",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for column settings": {
          "name": "Public read access for column settings",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Allow public insertion for initialization": {
          "name": "Allow public insertion for initialization",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ],
          "withCheck": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_groups": {
      "name": "product_groups",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": true,
          "default": "now()"
        }
      },
      "indexes": {
        "idx_product_groups_order_index": {
          "name": "idx_product_groups_order_index",
          "columns": [
            {
              "expression": "order_index",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            }
          ],
          "isUnique": false,
          "concurrently": false,
          "method": "btree",
          "with": {}
        }
      },
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can manage groups": {
          "name": "Authenticated users can manage groups",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for groups": {
          "name": "Public read access for groups",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ],
          "using": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_images": {
      "name": "product_images",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "product_id": {
          "name": "product_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": true
        },
        "yandex_disk_path": {
          "name": "yandex_disk_path",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "public_url": {
          "name": "public_url",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "sort_order": {
          "name": "sort_order",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_main": {
          "name": "is_main",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {
        "product_images_product_id_fkey": {
          "name": "product_images_product_id_fkey",
          "tableFrom": "product_images",
          "tableTo": "products",
          "columnsFrom": [
            "product_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "cascade",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "product_images_product_id_sort_order_key": {
          "name": "product_images_product_id_sort_order_key",
          "nullsNotDistinct": false,
          "columns": [
            "product_id",
            "sort_order"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage product images": {
          "name": "Authenticated users can manage product images",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for product images": {
          "name": "Public read access for product images",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.products": {
      "name": "products",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "weight": {
          "name": "weight",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "price": {
          "name": "price",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "validity_period": {
          "name": "validity_period",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_hidden": {
          "name": "is_hidden",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "custom_fields": {
          "name": "custom_fields",
          "type": "jsonb",
          "primaryKey": false,
          "notNull": false,
          "default": "'{}'::jsonb"
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "updated_at": {
          "name": "updated_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "group_id": {
          "name": "group_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": false
        },
        "order_in_group": {
          "name": "order_in_group",
          "type": "integer",
          "primaryKey": false,
          "notNull": true,
          "default": 0
        }
      },
      "indexes": {
        "idx_products_group_order": {
          "name": "idx_products_group_order",
          "columns": [
            {
              "expression": "group_id",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            },
            {
              "expression": "order_in_group",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            }
          ],
          "isUnique": false,
          "concurrently": false,
          "method": "btree",
          "with": {}
        }
      },
      "foreignKeys": {
        "products_group_id_product_groups_id_fk": {
          "name": "products_group_id_product_groups_id_fk",
          "tableFrom": "products",
          "tableTo": "product_groups",
          "columnsFrom": [
            "group_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "set null",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can create products": {
          "name": "Authenticated users can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "authenticated"
          ],
          "withCheck": "true"
        },
        "Authenticated users can read products": {
          "name": "Authenticated users can read products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "authenticated"
          ]
        },
        "Authenticated users can delete products": {
          "name": "Authenticated users can delete products",
          "as": "PERMISSIVE",
          "for": "DELETE",
          "to": [
            "public"
          ]
        },
        "Authenticated users can update products": {
          "name": "Authenticated users can update products",
          "as": "PERMISSIVE",
          "for": "UPDATE",
          "to": [
            "public"
          ]
        },
        "Public read access for non-hidden products": {
          "name": "Public read access for non-hidden products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Anyone can create products": {
          "name": "Anyone can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {
        "products_price_check": {
          "name": "products_price_check",
          "value": "price >= (0)::numeric"
        },
        "products_validity_period_check": {
          "name": "products_validity_period_check",
          "value": "validity_period >= 0"
        },
        "products_weight_check": {
          "name": "products_weight_check",
          "value": "weight >= (0)::numeric"
        }
      },
      "isRLSEnabled": false
    }
  },
  "enums": {
    "public.column_type": {
      "name": "column_type",
      "schema": "public",
      "values": [
        "text",
        "number"
      ]
    }
  },
  "schemas": {},
  "sequences": {},
  "roles": {},
  "policies": {},
  "views": {},
  "_meta": {
    "columns": {},
    "schemas": {},
    "tables": {}
  }
}
</file>

<file path="src/db/meta/0005_snapshot.json">
{
  "id": "38ff7346-024b-40b0-8ff4-7074d84cca78",
  "prevId": "baf5a797-5cf3-43c6-8310-817a73b43865",
  "version": "7",
  "dialect": "postgresql",
  "tables": {
    "public.column_settings": {
      "name": "column_settings",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "display_name": {
          "name": "display_name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_visible": {
          "name": "is_visible",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": true
        },
        "data_type": {
          "name": "data_type",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "column_settings_name_unique": {
          "name": "column_settings_name_unique",
          "nullsNotDistinct": false,
          "columns": [
            "name"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage column settings": {
          "name": "Authenticated users can manage column settings",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for column settings": {
          "name": "Public read access for column settings",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Allow public insertion for initialization": {
          "name": "Allow public insertion for initialization",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ],
          "withCheck": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_groups": {
      "name": "product_groups",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": true,
          "default": "now()"
        }
      },
      "indexes": {
        "idx_product_groups_order_index": {
          "name": "idx_product_groups_order_index",
          "columns": [
            {
              "expression": "order_index",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            }
          ],
          "isUnique": false,
          "concurrently": false,
          "method": "btree",
          "with": {}
        }
      },
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can manage groups": {
          "name": "Authenticated users can manage groups",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for groups": {
          "name": "Public read access for groups",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ],
          "using": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_images": {
      "name": "product_images",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "product_id": {
          "name": "product_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": true
        },
        "yandex_disk_path": {
          "name": "yandex_disk_path",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "public_url": {
          "name": "public_url",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "sort_order": {
          "name": "sort_order",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_main": {
          "name": "is_main",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {
        "product_images_product_id_fkey": {
          "name": "product_images_product_id_fkey",
          "tableFrom": "product_images",
          "tableTo": "products",
          "columnsFrom": [
            "product_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "cascade",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "product_images_product_id_sort_order_key": {
          "name": "product_images_product_id_sort_order_key",
          "nullsNotDistinct": false,
          "columns": [
            "product_id",
            "sort_order"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage product images": {
          "name": "Authenticated users can manage product images",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for product images": {
          "name": "Public read access for product images",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.products": {
      "name": "products",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "weight": {
          "name": "weight",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "price": {
          "name": "price",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "validity_period": {
          "name": "validity_period",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_hidden": {
          "name": "is_hidden",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "custom_fields": {
          "name": "custom_fields",
          "type": "jsonb",
          "primaryKey": false,
          "notNull": false,
          "default": "'{}'::jsonb"
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "updated_at": {
          "name": "updated_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "group_id": {
          "name": "group_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": false
        },
        "order_in_group": {
          "name": "order_in_group",
          "type": "integer",
          "primaryKey": false,
          "notNull": true,
          "default": 0
        }
      },
      "indexes": {
        "idx_products_group_order": {
          "name": "idx_products_group_order",
          "columns": [
            {
              "expression": "group_id",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            },
            {
              "expression": "order_in_group",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            }
          ],
          "isUnique": false,
          "concurrently": false,
          "method": "btree",
          "with": {}
        }
      },
      "foreignKeys": {
        "products_group_id_product_groups_id_fk": {
          "name": "products_group_id_product_groups_id_fk",
          "tableFrom": "products",
          "tableTo": "product_groups",
          "columnsFrom": [
            "group_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "set null",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can create products": {
          "name": "Authenticated users can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "authenticated"
          ],
          "withCheck": "true"
        },
        "Authenticated users can read products": {
          "name": "Authenticated users can read products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "authenticated"
          ]
        },
        "Authenticated users can delete products": {
          "name": "Authenticated users can delete products",
          "as": "PERMISSIVE",
          "for": "DELETE",
          "to": [
            "public"
          ]
        },
        "Authenticated users can update products": {
          "name": "Authenticated users can update products",
          "as": "PERMISSIVE",
          "for": "UPDATE",
          "to": [
            "public"
          ]
        },
        "Public read access for non-hidden products": {
          "name": "Public read access for non-hidden products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Anyone can create products": {
          "name": "Anyone can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {
        "products_price_check": {
          "name": "products_price_check",
          "value": "price >= (0)::numeric"
        },
        "products_validity_period_check": {
          "name": "products_validity_period_check",
          "value": "validity_period >= 0"
        },
        "products_weight_check": {
          "name": "products_weight_check",
          "value": "weight >= (0)::numeric"
        }
      },
      "isRLSEnabled": false
    },
    "public.session_lock": {
      "name": "session_lock",
      "schema": "",
      "columns": {
        "lock_key": {
          "name": "lock_key",
          "type": "text",
          "primaryKey": true,
          "notNull": true
        },
        "user_id": {
          "name": "user_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": false
        },
        "session_id": {
          "name": "session_id",
          "type": "text",
          "primaryKey": false,
          "notNull": false
        },
        "locked_at": {
          "name": "locked_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Allow authenticated access": {
          "name": "Allow authenticated access",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    }
  },
  "enums": {
    "public.column_type": {
      "name": "column_type",
      "schema": "public",
      "values": [
        "text",
        "number"
      ]
    }
  },
  "schemas": {},
  "sequences": {},
  "roles": {},
  "policies": {},
  "views": {},
  "_meta": {
    "columns": {},
    "schemas": {},
    "tables": {}
  }
}
</file>

<file path="src/db/meta/0006_snapshot.json">
{
  "id": "fdc32a38-5a7a-4526-be55-4806f38aab5d",
  "prevId": "38ff7346-024b-40b0-8ff4-7074d84cca78",
  "version": "7",
  "dialect": "postgresql",
  "tables": {
    "public.column_settings": {
      "name": "column_settings",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "display_name": {
          "name": "display_name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_visible": {
          "name": "is_visible",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": true
        },
        "data_type": {
          "name": "data_type",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "prefix": {
          "name": "prefix",
          "type": "text",
          "primaryKey": false,
          "notNull": false
        },
        "postfix": {
          "name": "postfix",
          "type": "text",
          "primaryKey": false,
          "notNull": false
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "column_settings_name_unique": {
          "name": "column_settings_name_unique",
          "nullsNotDistinct": false,
          "columns": [
            "name"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage column settings": {
          "name": "Authenticated users can manage column settings",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for column settings": {
          "name": "Public read access for column settings",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Allow public insertion for initialization": {
          "name": "Allow public insertion for initialization",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ],
          "withCheck": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_groups": {
      "name": "product_groups",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": true,
          "default": "now()"
        }
      },
      "indexes": {
        "idx_product_groups_order_index": {
          "name": "idx_product_groups_order_index",
          "columns": [
            {
              "expression": "order_index",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            }
          ],
          "isUnique": false,
          "concurrently": false,
          "method": "btree",
          "with": {}
        }
      },
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can manage groups": {
          "name": "Authenticated users can manage groups",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for groups": {
          "name": "Public read access for groups",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ],
          "using": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_images": {
      "name": "product_images",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "product_id": {
          "name": "product_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": true
        },
        "yandex_disk_path": {
          "name": "yandex_disk_path",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "public_url": {
          "name": "public_url",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "sort_order": {
          "name": "sort_order",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_main": {
          "name": "is_main",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {
        "product_images_product_id_fkey": {
          "name": "product_images_product_id_fkey",
          "tableFrom": "product_images",
          "tableTo": "products",
          "columnsFrom": [
            "product_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "cascade",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "product_images_product_id_sort_order_key": {
          "name": "product_images_product_id_sort_order_key",
          "nullsNotDistinct": false,
          "columns": [
            "product_id",
            "sort_order"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage product images": {
          "name": "Authenticated users can manage product images",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for product images": {
          "name": "Public read access for product images",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.products": {
      "name": "products",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "weight": {
          "name": "weight",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "price": {
          "name": "price",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "validity_period": {
          "name": "validity_period",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_hidden": {
          "name": "is_hidden",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "custom_fields": {
          "name": "custom_fields",
          "type": "jsonb",
          "primaryKey": false,
          "notNull": false,
          "default": "'{}'::jsonb"
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "updated_at": {
          "name": "updated_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "group_id": {
          "name": "group_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": false
        },
        "order_in_group": {
          "name": "order_in_group",
          "type": "integer",
          "primaryKey": false,
          "notNull": true,
          "default": 0
        }
      },
      "indexes": {
        "idx_products_group_order": {
          "name": "idx_products_group_order",
          "columns": [
            {
              "expression": "group_id",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            },
            {
              "expression": "order_in_group",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            }
          ],
          "isUnique": false,
          "concurrently": false,
          "method": "btree",
          "with": {}
        }
      },
      "foreignKeys": {
        "products_group_id_product_groups_id_fk": {
          "name": "products_group_id_product_groups_id_fk",
          "tableFrom": "products",
          "tableTo": "product_groups",
          "columnsFrom": [
            "group_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "set null",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can create products": {
          "name": "Authenticated users can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "authenticated"
          ],
          "withCheck": "true"
        },
        "Authenticated users can read products": {
          "name": "Authenticated users can read products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "authenticated"
          ]
        },
        "Authenticated users can delete products": {
          "name": "Authenticated users can delete products",
          "as": "PERMISSIVE",
          "for": "DELETE",
          "to": [
            "public"
          ]
        },
        "Authenticated users can update products": {
          "name": "Authenticated users can update products",
          "as": "PERMISSIVE",
          "for": "UPDATE",
          "to": [
            "public"
          ]
        },
        "Public read access for non-hidden products": {
          "name": "Public read access for non-hidden products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Anyone can create products": {
          "name": "Anyone can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {
        "products_price_check": {
          "name": "products_price_check",
          "value": "price >= (0)::numeric"
        },
        "products_validity_period_check": {
          "name": "products_validity_period_check",
          "value": "validity_period >= 0"
        },
        "products_weight_check": {
          "name": "products_weight_check",
          "value": "weight >= (0)::numeric"
        }
      },
      "isRLSEnabled": false
    },
    "public.session_lock": {
      "name": "session_lock",
      "schema": "",
      "columns": {
        "lock_key": {
          "name": "lock_key",
          "type": "text",
          "primaryKey": true,
          "notNull": true
        },
        "user_id": {
          "name": "user_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": false
        },
        "session_id": {
          "name": "session_id",
          "type": "text",
          "primaryKey": false,
          "notNull": false
        },
        "locked_at": {
          "name": "locked_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Allow authenticated access": {
          "name": "Allow authenticated access",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    }
  },
  "enums": {
    "public.column_type": {
      "name": "column_type",
      "schema": "public",
      "values": [
        "text",
        "number"
      ]
    }
  },
  "schemas": {},
  "sequences": {},
  "roles": {},
  "policies": {},
  "views": {},
  "_meta": {
    "columns": {},
    "schemas": {},
    "tables": {}
  }
}
</file>

<file path="src/db/meta/0007_snapshot.json">
{
  "id": "2ef843f5-2186-4250-b71b-7bfce88a579d",
  "prevId": "fdc32a38-5a7a-4526-be55-4806f38aab5d",
  "version": "7",
  "dialect": "postgresql",
  "tables": {
    "public.column_settings": {
      "name": "column_settings",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "display_name": {
          "name": "display_name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_visible": {
          "name": "is_visible",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": true
        },
        "data_type": {
          "name": "data_type",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "prefix": {
          "name": "prefix",
          "type": "text",
          "primaryKey": false,
          "notNull": false
        },
        "postfix": {
          "name": "postfix",
          "type": "text",
          "primaryKey": false,
          "notNull": false
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "column_settings_name_unique": {
          "name": "column_settings_name_unique",
          "nullsNotDistinct": false,
          "columns": [
            "name"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage column settings": {
          "name": "Authenticated users can manage column settings",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for column settings": {
          "name": "Public read access for column settings",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Allow public insertion for initialization": {
          "name": "Allow public insertion for initialization",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ],
          "withCheck": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_groups": {
      "name": "product_groups",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "order_index": {
          "name": "order_index",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_hidden": {
          "name": "is_hidden",
          "type": "boolean",
          "primaryKey": false,
          "notNull": true,
          "default": false
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": true,
          "default": "now()"
        }
      },
      "indexes": {
        "idx_product_groups_order_index": {
          "name": "idx_product_groups_order_index",
          "columns": [
            {
              "expression": "order_index",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            }
          ],
          "isUnique": false,
          "concurrently": false,
          "method": "btree",
          "with": {}
        }
      },
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can manage groups": {
          "name": "Authenticated users can manage groups",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for groups": {
          "name": "Public read access for groups",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ],
          "using": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.product_images": {
      "name": "product_images",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "product_id": {
          "name": "product_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": true
        },
        "yandex_disk_path": {
          "name": "yandex_disk_path",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "public_url": {
          "name": "public_url",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "sort_order": {
          "name": "sort_order",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_main": {
          "name": "is_main",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        }
      },
      "indexes": {},
      "foreignKeys": {
        "product_images_product_id_fkey": {
          "name": "product_images_product_id_fkey",
          "tableFrom": "product_images",
          "tableTo": "products",
          "columnsFrom": [
            "product_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "cascade",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {
        "product_images_product_id_sort_order_key": {
          "name": "product_images_product_id_sort_order_key",
          "nullsNotDistinct": false,
          "columns": [
            "product_id",
            "sort_order"
          ]
        }
      },
      "policies": {
        "Authenticated users can manage product images": {
          "name": "Authenticated users can manage product images",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        },
        "Public read access for product images": {
          "name": "Public read access for product images",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    },
    "public.products": {
      "name": "products",
      "schema": "",
      "columns": {
        "id": {
          "name": "id",
          "type": "uuid",
          "primaryKey": true,
          "notNull": true,
          "default": "gen_random_uuid()"
        },
        "name": {
          "name": "name",
          "type": "text",
          "primaryKey": false,
          "notNull": true
        },
        "weight": {
          "name": "weight",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "price": {
          "name": "price",
          "type": "numeric",
          "primaryKey": false,
          "notNull": true
        },
        "validity_period": {
          "name": "validity_period",
          "type": "integer",
          "primaryKey": false,
          "notNull": true
        },
        "is_hidden": {
          "name": "is_hidden",
          "type": "boolean",
          "primaryKey": false,
          "notNull": false,
          "default": false
        },
        "custom_fields": {
          "name": "custom_fields",
          "type": "jsonb",
          "primaryKey": false,
          "notNull": false,
          "default": "'{}'::jsonb"
        },
        "created_at": {
          "name": "created_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "updated_at": {
          "name": "updated_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false,
          "default": "now()"
        },
        "group_id": {
          "name": "group_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": false
        },
        "order_in_group": {
          "name": "order_in_group",
          "type": "integer",
          "primaryKey": false,
          "notNull": true,
          "default": 0
        }
      },
      "indexes": {
        "idx_products_group_order": {
          "name": "idx_products_group_order",
          "columns": [
            {
              "expression": "group_id",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            },
            {
              "expression": "order_in_group",
              "isExpression": false,
              "asc": true,
              "nulls": "last"
            }
          ],
          "isUnique": false,
          "concurrently": false,
          "method": "btree",
          "with": {}
        }
      },
      "foreignKeys": {
        "products_group_id_product_groups_id_fk": {
          "name": "products_group_id_product_groups_id_fk",
          "tableFrom": "products",
          "tableTo": "product_groups",
          "columnsFrom": [
            "group_id"
          ],
          "columnsTo": [
            "id"
          ],
          "onDelete": "set null",
          "onUpdate": "no action"
        }
      },
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Authenticated users can create products": {
          "name": "Authenticated users can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "authenticated"
          ],
          "withCheck": "true"
        },
        "Authenticated users can read products": {
          "name": "Authenticated users can read products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "authenticated"
          ]
        },
        "Authenticated users can delete products": {
          "name": "Authenticated users can delete products",
          "as": "PERMISSIVE",
          "for": "DELETE",
          "to": [
            "public"
          ]
        },
        "Authenticated users can update products": {
          "name": "Authenticated users can update products",
          "as": "PERMISSIVE",
          "for": "UPDATE",
          "to": [
            "public"
          ]
        },
        "Public read access for non-hidden products": {
          "name": "Public read access for non-hidden products",
          "as": "PERMISSIVE",
          "for": "SELECT",
          "to": [
            "public"
          ]
        },
        "Anyone can create products": {
          "name": "Anyone can create products",
          "as": "PERMISSIVE",
          "for": "INSERT",
          "to": [
            "public"
          ]
        }
      },
      "checkConstraints": {
        "products_price_check": {
          "name": "products_price_check",
          "value": "price >= (0)::numeric"
        },
        "products_validity_period_check": {
          "name": "products_validity_period_check",
          "value": "validity_period >= 0"
        },
        "products_weight_check": {
          "name": "products_weight_check",
          "value": "weight >= (0)::numeric"
        }
      },
      "isRLSEnabled": false
    },
    "public.session_lock": {
      "name": "session_lock",
      "schema": "",
      "columns": {
        "lock_key": {
          "name": "lock_key",
          "type": "text",
          "primaryKey": true,
          "notNull": true
        },
        "user_id": {
          "name": "user_id",
          "type": "uuid",
          "primaryKey": false,
          "notNull": false
        },
        "session_id": {
          "name": "session_id",
          "type": "text",
          "primaryKey": false,
          "notNull": false
        },
        "locked_at": {
          "name": "locked_at",
          "type": "timestamp with time zone",
          "primaryKey": false,
          "notNull": false
        }
      },
      "indexes": {},
      "foreignKeys": {},
      "compositePrimaryKeys": {},
      "uniqueConstraints": {},
      "policies": {
        "Allow authenticated access": {
          "name": "Allow authenticated access",
          "as": "PERMISSIVE",
          "for": "ALL",
          "to": [
            "authenticated"
          ],
          "using": "true",
          "withCheck": "true"
        }
      },
      "checkConstraints": {},
      "isRLSEnabled": false
    }
  },
  "enums": {
    "public.column_type": {
      "name": "column_type",
      "schema": "public",
      "values": [
        "text",
        "number"
      ]
    }
  },
  "schemas": {},
  "sequences": {},
  "roles": {},
  "policies": {},
  "views": {},
  "_meta": {
    "columns": {},
    "schemas": {},
    "tables": {}
  }
}
</file>

<file path="src/db/0000_crazy_energizer.sql">
-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TYPE "public"."column_type" AS ENUM('text', 'number');--> statement-breakpoint
CREATE TABLE "product_images" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"product_id" uuid NOT NULL,
	"yandex_disk_path" text NOT NULL,
	"public_url" text NOT NULL,
	"sort_order" integer NOT NULL,
	"is_main" boolean DEFAULT false,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "product_images_product_id_sort_order_key" UNIQUE("product_id","sort_order")
);
--> statement-breakpoint
ALTER TABLE "product_images" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "products" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"weight" numeric NOT NULL,
	"price" numeric NOT NULL,
	"validity_period" integer NOT NULL,
	"is_hidden" boolean DEFAULT false,
	"custom_fields" jsonb DEFAULT '{}'::jsonb,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "products_price_check" CHECK (price >= (0)::numeric),
	CONSTRAINT "products_validity_period_check" CHECK (validity_period >= 0),
	CONSTRAINT "products_weight_check" CHECK (weight >= (0)::numeric)
);
--> statement-breakpoint
ALTER TABLE "products" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "product_images" ADD CONSTRAINT "product_images_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE POLICY "Authenticated users can manage product images" ON "product_images" AS PERMISSIVE FOR ALL TO "authenticated" USING (true) WITH CHECK (true);--> statement-breakpoint
CREATE POLICY "Public read access for product images" ON "product_images" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Authenticated users can create products" ON "products" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK (true);--> statement-breakpoint
CREATE POLICY "Authenticated users can read products" ON "products" AS PERMISSIVE FOR SELECT TO "authenticated";--> statement-breakpoint
CREATE POLICY "Authenticated users can delete products" ON "products" AS PERMISSIVE FOR DELETE TO public;--> statement-breakpoint
CREATE POLICY "Authenticated users can update products" ON "products" AS PERMISSIVE FOR UPDATE TO public;--> statement-breakpoint
CREATE POLICY "Public read access for non-hidden products" ON "products" AS PERMISSIVE FOR SELECT TO public;--> statement-breakpoint
CREATE POLICY "Anyone can create products" ON "products" AS PERMISSIVE FOR INSERT TO public;
*/
</file>

<file path="src/db/0001_adorable_shriek.sql">
CREATE TABLE "column_settings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"display_name" text NOT NULL,
	"order_index" integer NOT NULL,
	"is_visible" boolean DEFAULT true,
	"data_type" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "column_settings_name_unique" UNIQUE("name")
);
--> statement-breakpoint
ALTER TABLE "column_settings" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE POLICY "Authenticated users can manage column settings" ON "column_settings" AS PERMISSIVE FOR ALL TO "authenticated" USING (true) WITH CHECK (true);--> statement-breakpoint
CREATE POLICY "Public read access for column settings" ON "column_settings" AS PERMISSIVE FOR SELECT TO public;
</file>

<file path="src/db/0002_silly_firebird.sql">
CREATE POLICY "Allow public insertion for initialization" ON "column_settings" AS PERMISSIVE FOR INSERT TO public WITH CHECK (true);
</file>

<file path="src/db/0003_tiresome_madame_hydra.sql">
CREATE TABLE "product_groups" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"order_index" integer NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "product_groups" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "products" ADD COLUMN "group_id" uuid;--> statement-breakpoint
CREATE INDEX "idx_product_groups_order_index" ON "product_groups" USING btree ("order_index");--> statement-breakpoint
ALTER TABLE "products" ADD CONSTRAINT "products_group_id_product_groups_id_fk" FOREIGN KEY ("group_id") REFERENCES "public"."product_groups"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
CREATE POLICY "Authenticated users can manage groups" ON "product_groups" AS PERMISSIVE FOR ALL TO "authenticated" USING (true) WITH CHECK (true);--> statement-breakpoint
CREATE POLICY "Public read access for groups" ON "product_groups" AS PERMISSIVE FOR SELECT TO public USING (true);
</file>

<file path="src/db/0004_chilly_sister_grimm.sql">
ALTER TABLE "products" ADD COLUMN "order_in_group" integer DEFAULT 0 NOT NULL;--> statement-breakpoint
CREATE INDEX "idx_products_group_order" ON "products" USING btree ("group_id","order_in_group");
</file>

<file path="src/db/0005_rare_cobalt_man.sql">
CREATE TABLE "session_lock" (
	"lock_key" text PRIMARY KEY NOT NULL,
	"user_id" uuid,
	"session_id" text,
	"locked_at" timestamp with time zone
);
--> statement-breakpoint
ALTER TABLE "session_lock" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE POLICY "Allow authenticated access" ON "session_lock" AS PERMISSIVE FOR ALL TO "authenticated" USING (true) WITH CHECK (true);
</file>

<file path="src/db/0006_last_the_watchers.sql">
ALTER TABLE "column_settings" ADD COLUMN "prefix" text;--> statement-breakpoint
ALTER TABLE "column_settings" ADD COLUMN "postfix" text;
</file>

<file path="src/db/0007_ambitious_vampiro.sql">
ALTER TABLE "product_groups" ADD COLUMN "is_hidden" boolean DEFAULT false NOT NULL;
</file>

<file path="src/db/types.ts">
import { products, productImages } from "./schema";

// Типы для таблицы Products
export type InsertProduct = typeof products.$inferInsert;
export type SelectProduct = typeof products.$inferSelect;

// Типы для таблицы ProductImages
export type InsertProductImage = typeof productImages.$inferInsert;
export type SelectProductImage = typeof productImages.$inferSelect;
</file>

<file path="src/hooks/use-toast.ts">
"use client"

// Inspired by react-hot-toast library
import * as React from "react"

import type {
  ToastActionElement,
  ToastProps,
} from "@/components/ui/toast"

const TOAST_LIMIT = 1
const TOAST_REMOVE_DELAY = 1000000

type ToasterToast = ToastProps & {
  id: string
  title?: React.ReactNode
  description?: React.ReactNode
  action?: ToastActionElement
}

const actionTypes = {
  ADD_TOAST: "ADD_TOAST",
  UPDATE_TOAST: "UPDATE_TOAST",
  DISMISS_TOAST: "DISMISS_TOAST",
  REMOVE_TOAST: "REMOVE_TOAST",
} as const

let count = 0

function genId() {
  count = (count + 1) % Number.MAX_SAFE_INTEGER
  return count.toString()
}

type ActionType = typeof actionTypes

type Action =
  | {
      type: ActionType["ADD_TOAST"]
      toast: ToasterToast
    }
  | {
      type: ActionType["UPDATE_TOAST"]
      toast: Partial<ToasterToast>
    }
  | {
      type: ActionType["DISMISS_TOAST"]
      toastId?: ToasterToast["id"]
    }
  | {
      type: ActionType["REMOVE_TOAST"]
      toastId?: ToasterToast["id"]
    }

interface State {
  toasts: ToasterToast[]
}

const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()

const addToRemoveQueue = (toastId: string) => {
  if (toastTimeouts.has(toastId)) {
    return
  }

  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId)
    dispatch({
      type: "REMOVE_TOAST",
      toastId: toastId,
    })
  }, TOAST_REMOVE_DELAY)

  toastTimeouts.set(toastId, timeout)
}

export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "ADD_TOAST":
      return {
        ...state,
        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),
      }

    case "UPDATE_TOAST":
      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === action.toast.id ? { ...t, ...action.toast } : t
        ),
      }

    case "DISMISS_TOAST": {
      const { toastId } = action

      // ! Side effects ! - This could be extracted into a dismissToast() action,
      // but I'll keep it here for simplicity
      if (toastId) {
        addToRemoveQueue(toastId)
      } else {
        state.toasts.forEach((toast) => {
          addToRemoveQueue(toast.id)
        })
      }

      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === toastId || toastId === undefined
            ? {
                ...t,
                open: false,
              }
            : t
        ),
      }
    }
    case "REMOVE_TOAST":
      if (action.toastId === undefined) {
        return {
          ...state,
          toasts: [],
        }
      }
      return {
        ...state,
        toasts: state.toasts.filter((t) => t.id !== action.toastId),
      }
  }
}

const listeners: Array<(state: State) => void> = []

let memoryState: State = { toasts: [] }

function dispatch(action: Action) {
  memoryState = reducer(memoryState, action)
  listeners.forEach((listener) => {
    listener(memoryState)
  })
}

type Toast = Omit<ToasterToast, "id">

function toast({ ...props }: Toast) {
  const id = genId()

  const update = (props: ToasterToast) =>
    dispatch({
      type: "UPDATE_TOAST",
      toast: { ...props, id },
    })
  const dismiss = () => dispatch({ type: "DISMISS_TOAST", toastId: id })

  dispatch({
    type: "ADD_TOAST",
    toast: {
      ...props,
      id,
      open: true,
      onOpenChange: (open) => {
        if (!open) dismiss()
      },
    },
  })

  return {
    id: id,
    dismiss,
    update,
  }
}

function useToast() {
  const [state, setState] = React.useState<State>(memoryState)

  React.useEffect(() => {
    listeners.push(setState)
    return () => {
      const index = listeners.indexOf(setState)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }, [state])

  return {
    ...state,
    toast,
    dismiss: (toastId?: string) => dispatch({ type: "DISMISS_TOAST", toastId }),
  }
}

export { useToast, toast }
</file>

<file path="src/hooks/useImageFolder.ts">
import { useState } from "react";
import {
  getPublicFolderContents,
  getDirectFileUrl,
} from "../services/yandexDisk";

export interface FolderItem {
  name: string;
  preview?: string;
  public_url?: string;
  type: "dir" | "file";
  directUrl?: string;
}

export const useImageFolder = () => {
  const [folderContents, setFolderContents] = useState<FolderItem[]>([]);
  const [isLoadingContents, setIsLoadingContents] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Получение содержимого папки изображений
  const fetchFolderContents = async (url: string): Promise<boolean> => {
    setIsLoadingContents(true);
    setError(null);
    try {
      const items = await getPublicFolderContents(url);
      // Фильтруем только файлы изображений
      const imageItems = items
        .filter(
          (item) =>
            item.type === "file" &&
            item.name.match(/\.(jpeg|jpg|png|gif|webp)$/i)
        )
        .map((item) => ({
          name: item.name,
          preview: item.preview,
          public_url: item.public_url,
          type: item.type,
        }));

      setFolderContents(imageItems);
      return true;
    } catch (err) {
      setError(
        err instanceof Error
          ? `Ошибка получения содержимого папки: ${err.message}`
          : "Произошла ошибка при получении содержимого папки"
      );
      return false;
    } finally {
      setIsLoadingContents(false);
    }
  };

  // Получение прямой ссылки на изображение
  const getImageDirectUrl = async (
    folderUrl: string,
    fileName: string
  ): Promise<string | null> => {
    try {
      const directUrl = await getDirectFileUrl(folderUrl, fileName);
      setSelectedImage(directUrl);
      return directUrl;
    } catch (err) {
      setError(
        err instanceof Error
          ? `Ошибка получения прямой ссылки: ${err.message}`
          : "Произошла ошибка при получении прямой ссылки на изображение"
      );
      return null;
    }
  };

  return {
    folderContents,
    isLoadingContents,
    error,
    setError,
    fetchFolderContents,
    getImageDirectUrl,
    selectedImage,
    setSelectedImage,
  };
};
</file>

<file path="src/lib/auth.tsx">
import { createContext, useContext, useEffect, useState } from "react";
import { Session, User } from "@supabase/supabase-js";
import { supabase } from "./supabase";

interface AuthContextType {
  session: Session | null;
  user: User | null;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Получаем начальную сессию
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Подписываемся на изменения состояния аутентификации
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) throw error;
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  };

  const value = {
    session,
    user,
    signIn,
    signOut,
    loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
</file>

<file path="src/vite-env.d.ts">
/// <reference types="vite/client" />
</file>

<file path=".env.example">
VITE_SUPABASE_URL=your-supabase-project-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
VITE_YANDEX_DISK_TOKEN=your-yandex-disk-token
</file>

<file path=".gitignore">
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.*.local

# Testing
coverage
</file>

<file path=".prettierrc">
{}
</file>

<file path="components.json">
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "new-york",
  "rsc": false,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "src/index.css",
    "baseColor": "neutral",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils",
    "ui": "@/components/ui",
    "lib": "@/lib",
    "hooks": "@/hooks"
  },
  "iconLibrary": "lucide"
}
</file>

<file path="drizzle.config.ts">
import { config } from "dotenv";
import { defineConfig } from "drizzle-kit";

config({ path: ".env" });

export default defineConfig({
  schema: "./src/db/schema.ts",
  out: "./src/db",
  dialect: "postgresql",
  dbCredentials: {
    url: process.env.DATABASE_URL!,
  },
});
</file>

<file path="eslint.config.js">
import js from '@eslint/js';
import globals from 'globals';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import tseslint from 'typescript-eslint';

export default tseslint.config(
  { ignores: ['dist'] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
    },
  }
);
</file>

<file path="index.html">
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + React + TS</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
</file>

<file path="postcss.config.js">
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};
</file>

<file path="tsconfig.node.json">
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["vite.config.ts"]
}
</file>

<file path=".cursor/rules/pnpm-rule.mdc">
---
description: always use pnpm
globs: 
alwaysApply: true
---

In this project use only pnpm as package manager tool.
</file>

<file path="src/components/products/columns/productColumnUtils.ts">
// Список стандартных полей таблицы products
export const STANDARD_FIELDS = [
  "id", // Техническое поле, обычно не показываем
  "name",
  "weight",
  "price",
  "validity_period",
  "is_hidden",
  "created_at",
  "updated_at", // Техническое поле, обычно не показываем
  "product_images",
  "group_id",
  "order_in_group", // Техническое поле, обычно не показываем
  "custom_fields", // Само поле JSONB, его тоже не показываем как отдельный столбец настроек
];

// Известные пользовательские поля (можно оставить пустым, если генерация имен надежна)
export const KNOWN_CUSTOM_FIELDS: string[] = []; // Пример: ["test_field"];

/**
 * Проверка является ли поле пользовательским
 * (Логика остается прежней, но опирается на обновленный STANDARD_FIELDS)
 */
export function isCustomField(columnName: string): boolean {
  // Исключаем стандартные поля и само поле custom_fields
  if (STANDARD_FIELDS.includes(columnName) || columnName === "custom_fields") {
    return false;
  }
  // Все остальное считаем пользовательским
  return true;
}

/**
 * Получение пути для поля (для пользовательских полей преобразует к формату custom_fields.name)
 */
export function getFieldPath(columnName: string): string {
  if (isCustomField(columnName)) {
    // Убедимся, что имя поля не содержит уже префикс
    const actualFieldName = columnName.startsWith("custom_field.")
      ? columnName.split(".")[1]
      : columnName;
    return `custom_fields.${actualFieldName}`;
  }
  return columnName;
}

/**
 * Получение имени пользовательского поля из пути
 */
export function getCustomFieldName(fieldPath: string): string | null {
  const parts = fieldPath.split(".");
  if (parts.length === 2 && parts[0] === "custom_fields") {
    return parts[1];
  }
  // Если пришло имя без префикса и оно пользовательское, возвращаем его
  if (isCustomField(fieldPath)) {
    return fieldPath;
  }
  return null;
}

/**
 * Проверяет и преобразует пользовательские поля если необходимо
 * (Логика остается прежней)
 */
export function ensureCustomFieldsObject(
  customFields: unknown
): Record<string, unknown> {
  if (!customFields) {
    return {} as Record<string, unknown>;
  }

  if (typeof customFields === "string") {
    try {
      return JSON.parse(customFields);
    } catch (e) {
      console.error(
        `[ERROR] Не удалось преобразовать строку custom_fields:`,
        e
      );
      return {} as Record<string, unknown>;
    }
  }

  if (typeof customFields === "object" && customFields !== null) {
    return customFields as Record<string, unknown>;
  }

  return {} as Record<string, unknown>;
}
</file>

<file path="src/components/products/grid/CustomFieldCellRenderer.tsx">
import { ICellRendererParams } from "ag-grid-community";

// Расширяем ICellRendererParams для включения наших кастомных параметров
interface CustomFieldCellRendererParams extends ICellRendererParams {
  colDef: ICellRendererParams["colDef"] & {
    cellRendererParams?: {
      prefix?: string | null;
      postfix?: string | null;
      dataType?: "text" | "number";
    };
  };
}

export const CustomFieldCellRenderer = (
  params: CustomFieldCellRendererParams
) => {
  const { data, colDef } = params;
  if (!data || !colDef || !colDef.field) return null;

  // Проверяем, что поле имеет формат "custom_fields.field_name"
  const fieldPath = colDef.field.split(".");
  if (fieldPath.length !== 2 || fieldPath[0] !== "custom_fields") {
    console.warn(
      `[CustomFieldCellRenderer] Поле ${colDef.field} не соответствует ожидаемому формату "custom_fields.X", но рендерер был вызван.`
    );
    const directValue = data[colDef.field];
    return directValue !== undefined && directValue !== null
      ? String(directValue)
      : "";
  }

  const fieldName = fieldPath[1];

  // Проверяем наличие объекта custom_fields и данных в нем
  if (!data.custom_fields) {
    return "";
  }

  let customFieldsObject = data.custom_fields;
  // Проверяем тип custom_fields - должен быть объект
  if (typeof customFieldsObject === "string") {
    try {
      customFieldsObject = JSON.parse(customFieldsObject);
    } catch (e) {
      console.error(`[ERROR] Не удалось преобразовать custom_fields:`, e);
      return "";
    }
  }

  if (typeof customFieldsObject !== "object" || customFieldsObject === null) {
    return "";
  }

  const customFieldValue = (customFieldsObject as Record<string, unknown>)[
    fieldName
  ];

  if (customFieldValue === undefined || customFieldValue === null) {
    return "";
  }

  // --- ИЗМЕНЕНИЕ: Получаем prefix и postfix из cellRendererParams ---
  const prefix = params.colDef.cellRendererParams?.prefix || "";
  const postfix = params.colDef.cellRendererParams?.postfix || "";
  const dataType = params.colDef.cellRendererParams?.dataType;
  // --------------------------------------------------------------

  let valueStr = String(customFieldValue);

  // --- ИЗМЕНЕНИЕ: Применяем форматирование для числовых полей, включая денежный формат ---
  if (dataType === "number") {
    // Если постфикс "₽" (или другой символ валюты), считаем это денежным значением и форматируем
    if (postfix === "₽") {
      const numValue = Number(customFieldValue);
      if (!isNaN(numValue)) {
        valueStr = numValue.toFixed(2);
      }
    }
  }
  // ---------------------------------------------------------------------------------

  return `${prefix} ${valueStr} ${postfix}`;
};
</file>

<file path="src/components/products/grid/GroupHeaderRenderer.tsx">
import React, { useState, useRef, useEffect } from "react";
import { ICellRendererParams } from "ag-grid-community";
import { Edit2, Trash2 } from "lucide-react";
import { useProductGroupMutations } from "../../../hooks/useProductGroups";

// Тип данных для строки заголовка группы
export interface GroupHeaderRow {
  isGroupHeader: true;
  groupId: string;
  groupName: string;
  totalProducts?: number; // Опционально: количество продуктов в группе
}

interface GroupHeaderProps extends ICellRendererParams {
  data: GroupHeaderRow;
}

export const GroupHeaderRenderer = (props: GroupHeaderProps) => {
  const { data, api: gridApi, node } = props;
  const { update: updateGroup, remove: removeGroup } =
    useProductGroupMutations();

  const [isEditing, setIsEditing] = useState(false);
  const [currentName, setCurrentName] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  // Устанавливаем название в состояние при инициализации
  useEffect(() => {
    if (data && data.groupName) {
      setCurrentName(data.groupName);
    }
  }, [data]);

  // Фокусировка при входе в режим редактирования
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  if (!data || !data.isGroupHeader) {
    return null;
  }

  const startEditing = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Предотвращаем редактирование, если уже редактируется другая строка/ячейка
    if (gridApi?.getEditingCells().length > 0) {
      return;
    }
    setIsEditing(true);
  };

  const saveChanges = async () => {
    setIsEditing(false);
    const newName = currentName.trim();

    if (newName && newName !== data.groupName) {
      try {
        await updateGroup.mutateAsync({
          id: data.groupId,
          updates: { name: newName },
        });

        // Опционально: обновляем данные в узле AG Grid локально для мгновенного отображения
        if (node?.data) {
          node.setData({ ...node.data, groupName: newName });
        }
      } catch (error) {
        // Возвращаем старое имя при ошибке
        setCurrentName(data.groupName);
        console.error("Ошибка при обновлении имени группы:", error);
      }
    } else {
      // Если имя не изменилось или пустое, восстанавливаем оригинальное
      setCurrentName(data.groupName);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      saveChanges();
    } else if (event.key === "Escape") {
      setCurrentName(data.groupName); // Отмена - восстанавливаем старое имя
      setIsEditing(false);
    }
  };

  const handleBlur = () => {
    // Небольшая задержка, чтобы Enter успел сработать раньше blur
    setTimeout(saveChanges, 100);
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (data.groupId === "ungrouped") {
      return; // Не позволяем удалять специальную группу "Без группы"
    }

    // Запрашиваем подтверждение
    if (
      window.confirm(
        `Удалить группу "${data.groupName}"? Продукты будут перемещены в "Без группы".`
      )
    ) {
      try {
        await removeGroup.mutateAsync(data.groupId);
        // Обновление произойдет через invalidateQueries в мутации
      } catch (error) {
        console.error("Ошибка при удалении группы:", error);
      }
    }
  };

  return (
    <div className="group-header-row">
      <div className="flex items-center justify-between px-4 py-2 bg-gray-100 w-full font-medium text-gray-700 border-y border-gray-300 hover:bg-gray-200 transition-colors cursor-grab active:cursor-grabbing">
        <div className="flex items-center">
          {isEditing ? (
            <input
              ref={inputRef}
              type="text"
              value={currentName}
              onChange={(e) => setCurrentName(e.target.value)}
              onKeyDown={handleKeyDown}
              onBlur={handleBlur}
              className="px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          ) : (
            <span
              className="text-sm cursor-pointer hover:text-blue-600"
              onDoubleClick={startEditing}
              title="Двойной клик для редактирования"
            >
              {data.groupName}
            </span>
          )}

          {data.totalProducts !== undefined && (
            <span className="ml-2 px-2 py-0.5 bg-gray-200 rounded-full text-xs text-gray-700">
              {data.totalProducts}
            </span>
          )}
        </div>

        <div className="flex gap-2 opacity-50 hover:opacity-100 transition-opacity">
          <button
            onClick={startEditing}
            className="p-1 hover:bg-gray-200 rounded-full"
            title="Редактировать группу"
          >
            <Edit2 size={16} />
          </button>
          <button
            onClick={handleDelete}
            className="p-1 hover:bg-gray-200 rounded-full text-red-600"
            title="Удалить группу"
            disabled={data.groupId === "ungrouped"}
            style={{ opacity: data.groupId === "ungrouped" ? 0.3 : 1 }}
          >
            <Trash2 size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};
</file>

<file path="src/components/products/grid/index.ts">
export { ImageCellRenderer } from "./ImageCellRenderer";
export { CustomFieldCellRenderer } from "./CustomFieldCellRenderer";
export { VisibilityStatusRenderer } from "./VisibilityStatusRenderer";
export {
  GroupHeaderRenderer,
  type GroupHeaderRow,
} from "./GroupHeaderRenderer";
</file>

<file path="src/components/ui/Button.tsx">
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
</file>

<file path="src/components/ui/Modal.tsx">
import React, { ReactNode, useEffect } from "react";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  size?: "sm" | "md" | "lg" | "xl" | "full";
  closeOnEsc?: boolean;
  closeOnOverlayClick?: boolean;
  className?: string;
  headerClassName?: string;
  footer?: ReactNode;
  footerClassName?: string;
  bodyClassName?: string;
  showCloseButton?: boolean;
}

export function Modal({
  isOpen,
  onClose,
  title,
  children,
  size = "md",
  closeOnEsc = true,
  closeOnOverlayClick = true,
  className = "",
  headerClassName = "",
  footer,
  footerClassName = "",
  bodyClassName = "",
  showCloseButton = true,
}: ModalProps) {
  // Обработка нажатия клавиши Escape
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (closeOnEsc && event.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscKey);
      // Блокируем прокрутку body при открытом модальном окне
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey);
      // Восстанавливаем прокрутку при закрытии
      document.body.style.overflow = "";
    };
  }, [isOpen, onClose, closeOnEsc]);

  // Если модальное окно закрыто, не рендерим ничего
  if (!isOpen) return null;

  // Размеры модального окна
  const sizeClasses = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    full: "max-w-full h-full",
  };

  // Обработчик клика по оверлею
  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
      onClick={handleOverlayClick}
    >
      <div
        className={cn(
          "bg-white rounded-lg shadow-xl w-full flex flex-col",
          "max-h-[90vh]",
          sizeClasses[size],
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Заголовок модального окна */}
        {(title || showCloseButton) && (
          <div
            className={cn(
              "flex-shrink-0 flex justify-between items-center p-4 border-b border-gray-200",
              headerClassName
            )}
          >
            {title && (
              <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
            )}
            {showCloseButton && (
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
                aria-label="Закрыть"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
        )}

        {/* Основное содержимое (растягивается и прокручивается) */}
        <div className={cn("flex-grow overflow-y-auto", bodyClassName)}>
          {children}
        </div>

        {/* Футер (не сжимается, если есть) */}
        {footer && (
          <div
            className={cn(
              "flex-shrink-0 p-4 border-t border-gray-200",
              footerClassName
            )}
          >
            {footer}
          </div>
        )}
      </div>
    </div>
  );
}
</file>

<file path="src/components/ui/toast.tsx">
import * as React from "react";
import * as ToastPrimitives from "@radix-ui/react-toast";
import { cva, type VariantProps } from "class-variance-authority";
import { X } from "lucide-react";

import { cn } from "@/lib/utils";

const ToastProvider = ToastPrimitives.Provider;

const ToastViewport = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Viewport>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Viewport
    ref={ref}
    className={cn(
      "fixed bottom-0 right-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:flex-col md:max-w-[420px]",
      className
    )}
    {...props}
  />
));
ToastViewport.displayName = ToastPrimitives.Viewport.displayName;

const toastVariants = cva(
  "group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-bottom-full data-[state=open]:slide-in-from-bottom-full",
  {
    variants: {
      variant: {
        default: "border bg-background text-foreground",
        destructive:
          "destructive group border-destructive bg-destructive text-destructive-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

const Toast = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &
    VariantProps<typeof toastVariants>
>(({ className, variant, ...props }, ref) => {
  return (
    <ToastPrimitives.Root
      ref={ref}
      className={cn(toastVariants({ variant }), className)}
      {...props}
    />
  );
});
Toast.displayName = ToastPrimitives.Root.displayName;

const ToastAction = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Action>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Action
    ref={ref}
    className={cn(
      "inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",
      className
    )}
    {...props}
  />
));
ToastAction.displayName = ToastPrimitives.Action.displayName;

const ToastClose = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Close>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Close
    ref={ref}
    className={cn(
      "absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",
      className
    )}
    toast-close=""
    {...props}
  >
    <X className="h-4 w-4" />
  </ToastPrimitives.Close>
));
ToastClose.displayName = ToastPrimitives.Close.displayName;

const ToastTitle = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Title>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Title
    ref={ref}
    className={cn("text-sm font-semibold [&+div]:text-xs", className)}
    {...props}
  />
));
ToastTitle.displayName = ToastPrimitives.Title.displayName;

const ToastDescription = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Description>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Description
    ref={ref}
    className={cn("text-sm opacity-90", className)}
    {...props}
  />
));
ToastDescription.displayName = ToastPrimitives.Description.displayName;

type ToastProps = React.ComponentPropsWithoutRef<typeof Toast>;

type ToastActionElement = React.ReactElement<typeof ToastAction>;

export {
  type ToastProps,
  type ToastActionElement,
  ToastProvider,
  ToastViewport,
  Toast,
  ToastTitle,
  ToastDescription,
  ToastClose,
  ToastAction,
};
</file>

<file path="src/db/index.ts">
import { config } from "dotenv";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "./schema";

config({ path: ".env" });

const client = postgres(process.env.DATABASE_URL!);
export const db = drizzle(client, { schema });
</file>

<file path="src/db/init.ts">
import { supabase } from "../lib/supabase";

/**
 * Инициализирует таблицу column_settings, если она пуста.
 * Эта функция вызывается при запуске приложения.
 */
export async function initializeColumnSettings() {
  console.log("Запуск инициализации column_settings...");

  // Проверяем, есть ли уже записи в таблице
  const { count, error: countError } = await supabase
    .from("column_settings")
    .select("*", { count: "exact", head: true });

  if (countError) {
    console.error("Ошибка при подсчете записей column_settings:", countError);
    return;
  }

  // Если записи уже есть, не добавляем начальные данные
  if (count && count > 0) {
    console.log(
      "Таблица column_settings уже содержит данные, пропускаем инициализацию"
    );
    return;
  }

  // Начальные данные для column_settings
  const defaultColumns = [
    {
      name: "name",
      display_name: "Наименование",
      order_index: 0,
      is_visible: true,
      data_type: "text",
      prefix: null,
      postfix: null,
    },
    {
      name: "weight",
      display_name: "Упаковка",
      order_index: 1,
      is_visible: true,
      data_type: "number",
      prefix: null,
      postfix: "кг",
    },
    {
      name: "validity_period",
      display_name: "Сроки реализации",
      order_index: 2,
      is_visible: true,
      data_type: "number",
      prefix: null,
      postfix: "мес.",
    },
    {
      name: "price",
      display_name: "Цена",
      order_index: 3,
      is_visible: true,
      data_type: "number",
      prefix: null,
      postfix: "₽",
    },
    {
      name: "is_hidden",
      display_name: "Статус",
      order_index: 4,
      is_visible: true,
      data_type: "text",
      prefix: null,
      postfix: null,
    },
    {
      name: "product_images",
      display_name: "Изображения",
      order_index: 5,
      is_visible: true,
      data_type: "text",
      prefix: null,
      postfix: null,
    },
    {
      name: "created_at",
      display_name: "Дата добавления",
      order_index: 6,
      is_visible: true,
      data_type: "text",
      prefix: null,
      postfix: null,
    },
    {
      name: "group_id",
      display_name: "Группа",
      order_index: 7,
      is_visible: true,
      data_type: "text",
      prefix: null,
      postfix: null,
    },
  ];

  // Добавляем начальные данные
  const { error: insertError } = await supabase
    .from("column_settings")
    .insert(defaultColumns);

  if (insertError) {
    console.error(
      "Ошибка при добавлении начальных данных в column_settings:",
      insertError
    );
  } else {
    console.log(
      "Таблица column_settings успешно заполнена начальными данными с префиксами/постфиксами"
    );
  }
}
</file>

<file path="src/hooks/useColumnSettings.ts">
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { Database } from "../types/supabase";
import { supabase } from "../lib/supabase";

type ColumnSettingInsert =
  Database["public"]["Tables"]["column_settings"]["Insert"];
type ColumnSettingUpdate =
  Database["public"]["Tables"]["column_settings"]["Update"];

export function useColumnSettings() {
  return useQuery({
    queryKey: ["columnSettings"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("column_settings")
        .select("*")
        .order("order_index");

      if (error) throw error;
      return { data: data || [] };
    },
  });
}

export function useColumnSettingsMutations() {
  const queryClient = useQueryClient();

  const create = useMutation({
    mutationFn: async (column: ColumnSettingInsert) => {
      const { data, error } = await supabase
        .from("column_settings")
        .insert(column)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["columnSettings"] });
    },
  });

  const update = useMutation({
    mutationFn: async ({
      id,
      updates,
    }: {
      id: string;
      updates: ColumnSettingUpdate;
    }) => {
      const { data, error } = await supabase
        .from("column_settings")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["columnSettings"] });
    },
  });

  const remove = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("column_settings")
        .delete()
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["columnSettings"] });

      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const reorder = useMutation({
    mutationFn: async (columns: { id: string; order_index: number }[]) => {
      // Используем трюк с Promise.all для выполнения множественных обновлений
      await Promise.all(
        columns.map(({ id, order_index }) =>
          supabase.from("column_settings").update({ order_index }).eq("id", id)
        )
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["columnSettings"] });
    },
  });

  return { create, update, remove, reorder };
}
</file>

<file path="src/lib/supabase.ts">
import { createClient } from "@supabase/supabase-js";
import type { Database } from "../types/supabase";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables");
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
</file>

<file path="src/lib/utils.ts">
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
</file>

<file path="src/index.css">
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
</file>

<file path="src/main.tsx">
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "./index.css";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
</file>

<file path="tailwind.config.js">
/** @type {import('tailwindcss').Config} */
export default {
    darkMode: ['class'],
    content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
  	extend: {
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
};
</file>

<file path="tsconfig.app.json">
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"]
}
</file>

<file path="tsconfig.json">
{
  "files": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.node.json"
    }
  ],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
</file>

<file path="vite.config.ts">
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ["lucide-react"],
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
</file>

<file path="src/components/products/editHandlers/useProductEditHandlers.ts">
import { useCallback } from "react";
import { CellValueChangedEvent } from "ag-grid-community";
import { useProductMutations } from "../../../hooks/useProducts";
import { toast } from "@/hooks/use-toast";
import {
  isCustomFieldColumn,
  extractCustomFieldName,
  parseProductCustomFields,
  updateCustomFieldValue,
  prepareCustomFieldsForSave,
} from "../customFields/customFieldsHandler";
import { GroupHeaderRow } from "../grid/GroupHeaderRenderer";

import type { Database } from "../../../types/supabase";

type Product = Database["public"]["Tables"]["products"]["Row"];
type Json = Database["public"]["Tables"]["products"]["Row"]["custom_fields"];
type RowData = Product | GroupHeaderRow;

export function useProductEditHandlers() {
  const { update } = useProductMutations();

  const onCellValueChanged = useCallback(
    (event: CellValueChangedEvent<RowData>) => {
      // Игнорируем редактирование заголовков групп
      if ("isGroupHeader" in event.data && event.data.isGroupHeader) {
        return;
      }

      // Теперь безопасно приводим тип к Product
      const productData = event.data as Product;
      const { colDef } = event;
      const field = colDef.field;

      if (field === "is_hidden") return; // Обрабатываем видимость через тулбар

      // --- Добавляем обработку поля group_id ---
      if (field === "group_id") {
        // Для пустых значений или строки "null" устанавливаем null
        const groupId =
          event.newValue === "" || event.newValue === "null"
            ? null
            : event.newValue;

        update.mutate({
          id: productData.id,
          updates: { group_id: groupId },
        });
        return; // Прекращаем обработку для group_id
      }
      // -------------------------------------

      // Проверка типа данных для числовых полей
      const isNumberField = colDef.cellEditor === "agNumberCellEditor";
      let newValue = event.newValue;

      // Для числовых полей преобразуем строковые значения в числа
      if (isNumberField && typeof newValue === "string") {
        const parsedValue = parseFloat(newValue);
        if (!isNaN(parsedValue)) {
          newValue = parsedValue;
        }
      }

      // Обработка редактирования пользовательских полей
      // Проверяем, является ли поле динамическим по формату или по маркеру
      const isCustomField = isCustomFieldColumn(
        colDef.field,
        colDef.cellEditorParams
      );

      console.log(`[DEBUG] Изменение ячейки:`, {
        field: colDef.field,
        isCustomField,
        isNumberField,
        cellEditorParams: colDef.cellEditorParams,
        oldValue: event.oldValue,
        newValue,
        newValueType: typeof newValue,
        allData: productData,
      });

      if (isCustomField) {
        // Извлекаем имя поля
        const fieldName = extractCustomFieldName(colDef.field);

        // Получаем объект с пользовательскими полями
        const currentCustomFields = parseProductCustomFields(productData);

        // Обновляем значение в объекте
        const customFields = updateCustomFieldValue(
          currentCustomFields,
          fieldName,
          newValue,
          isNumberField
        );

        console.log(`[DEBUG] Обновление custom_fields:`, {
          id: productData.id,
          fieldName,
          oldValue: currentCustomFields[fieldName],
          newValue,
          newValueType: typeof newValue,
          isNumberField,
          newCustomFields: customFields,
        });

        try {
          // Конвертируем в корректный JSON формат для Supabase
          const cleanValue = prepareCustomFieldsForSave(customFields);

          // Обновляем продукт, отправляя полный обновленный объект custom_fields
          update.mutate({
            id: productData.id,
            updates: {
              custom_fields: cleanValue as Json,
            },
          });
        } catch (error) {
          console.error("Ошибка при форматировании custom_fields:", error);
          toast({
            title: "Ошибка",
            description: "Ошибка при обновлении поля",
            variant: "destructive",
          });
        }
      } else {
        // Обработка редактирования обычных полей
        update.mutate({
          id: productData.id,
          updates: { [colDef.field as string]: newValue },
        });
      }
    },
    [update]
  );

  return {
    onCellValueChanged,
  };
}
</file>

<file path="src/components/products/modals/ProductGroupsModal.tsx">
import { useState, useEffect, useCallback } from "react";
import {
  useProductGroups,
  useProductGroupMutations,
  ProductGroup,
} from "../../../hooks/useProductGroups";
import { toast } from "@/hooks/use-toast";
import {
  Plus,
  Trash2,
  ArrowUp,
  ArrowDown,
  Pencil,
  GripVertical,
} from "lucide-react";
import { Button } from "../../ui/Button";
import { IconButton } from "../../ui/IconButton";
import { Input } from "../../ui/Input";
import { Modal } from "../../ui/Modal";
import { Divider } from "../../ui/Divider";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface ProductGroupsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ProductGroupsModal({
  isOpen,
  onClose,
}: ProductGroupsModalProps) {
  const { data: groupsData, isLoading } = useProductGroups();
  const { create, update, remove, reorder } = useProductGroupMutations();

  // Локальное состояние для групп (позволяет вносить изменения без перезапроса данных)
  const [groups, setGroups] = useState<ProductGroup[]>([]);

  // Состояние для новой группы
  const [newGroupName, setNewGroupName] = useState("");

  // Заменяем старые состояния редактирования
  // const [editingGroupId, setEditingGroupId] = useState<string | null>(null);
  // const [editingGroupName, setEditingGroupName] = useState("");
  // На новые для inline-редактирования
  const [isEditingId, setIsEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState(""); // Имя группы во время редактирования

  // Обновляем локальное состояние при получении данных
  useEffect(() => {
    if (groupsData?.data) {
      setGroups(groupsData.data);
      // Если редактируемая группа исчезла из данных (например, удалена), сбрасываем редактирование
      if (isEditingId && !groupsData.data.some((g) => g.id === isEditingId)) {
        setIsEditingId(null);
        setEditingName("");
      }
    }
  }, [groupsData?.data, isEditingId]);

  // Обработчик добавления новой группы
  const handleAddGroup = useCallback(() => {
    if (!newGroupName.trim()) {
      toast({
        title: "Ошибка",
        description: "Введите название группы",
        variant: "destructive",
      });
      return;
    }
    const maxOrder =
      groups.length > 0
        ? Math.max(...groups.map((g) => g.order_index ?? 0))
        : -1; // Добавим ?? 0 для надежности

    create.mutate(
      {
        name: newGroupName.trim(),
        order_index: maxOrder + 1,
      },
      {
        onSuccess: () => {
          setNewGroupName("");
          // toast.success("Группа создана"); // Уведомление уже есть в мутации
        },
        onError: (error) => {
          console.error("Ошибка при создании группы:", error);
          // toast.error("Не удалось создать группу"); // Уведомление уже есть в мутации
        },
      }
    );
  }, [newGroupName, groups, create]);

  // Обработчик начала редактирования (обновлен)
  const handleStartEdit = useCallback((group: ProductGroup) => {
    setIsEditingId(group.id);
    setEditingName(group.name);
  }, []);

  // Обработчик изменения в поле ввода
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setEditingName(e.target.value);
    },
    []
  );

  // Обработчик отмены редактирования
  const handleCancelEdit = useCallback(() => {
    setIsEditingId(null);
    setEditingName("");
  }, []);

  // Обработчик сохранения изменений (вызывается по Enter или Blur)
  const handleSaveEdit = useCallback(
    async (groupId: string) => {
      const originalGroup = groups.find((g) => g.id === groupId);
      if (!originalGroup) return; // Группа не найдена

      const newName = editingName.trim();

      // Если имя не изменилось или стало пустым, просто выходим из режима редактирования
      if (!newName || newName === originalGroup.name) {
        handleCancelEdit();
        return;
      }

      try {
        await update.mutateAsync({
          id: groupId,
          updates: { name: newName },
        });
        toast({ title: "Успех", description: "Имя группы обновлено" });
      } catch (error) {
        console.error("Ошибка при обновлении имени группы:", error);
        toast({
          title: "Ошибка",
          description: "Не удалось обновить имя группы",
          variant: "destructive",
        });
        // При ошибке можно вернуть старое имя в поле ввода, если нужно
        // setEditingName(originalGroup.name);
      } finally {
        // В любом случае выходим из режима редактирования
        handleCancelEdit();
      }
    },
    [editingName, groups, update, handleCancelEdit]
  );

  // Обработчик нажатия клавиш в поле ввода
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>, groupId: string) => {
      if (event.key === "Enter") {
        handleSaveEdit(groupId);
      } else if (event.key === "Escape") {
        handleCancelEdit();
      }
    },
    [handleSaveEdit, handleCancelEdit]
  );

  // Обработчик потери фокуса полем ввода
  const handleBlur = useCallback(
    (groupId: string) => {
      // Небольшая задержка, чтобы Enter мог сработать раньше blur
      setTimeout(() => {
        // Проверяем, не закрылся ли режим редактирования уже (например, через Enter)
        if (isEditingId === groupId) {
          handleSaveEdit(groupId);
        }
      }, 150);
    },
    [handleSaveEdit, isEditingId]
  ); // Добавили isEditingId в зависимости

  // Обработчик удаления группы (обновлен)
  const handleDeleteGroup = useCallback(
    (group: ProductGroup) => {
      if (
        window.confirm(
          `Удалить группу "${group.name}"? Продукты в этой группе станут негруппированными.`
        )
      ) {
        remove.mutate(group.id, {
          onSuccess: () => {
            // toast.success("Группа удалена"); // Уведомление уже в мутации
          },
          onError: (error) => {
            console.error("Ошибка при удалении группы:", error);
            // toast.error("Не удалось удалить группу"); // Уведомление уже в мутации
          },
        });
      }
    },
    [remove]
  );

  // Обработчики перемещения групп вверх/вниз (обновлен)
  const handleMoveGroup = useCallback(
    (groupId: string, direction: "up" | "down") => {
      const currentIndex = groups.findIndex((g) => g.id === groupId);
      if (currentIndex === -1) return;

      const targetIndex =
        direction === "up"
          ? Math.max(0, currentIndex - 1)
          : Math.min(groups.length - 1, currentIndex + 1);

      if (targetIndex === currentIndex) return;

      const newGroups = [...groups];
      [newGroups[currentIndex], newGroups[targetIndex]] = [
        newGroups[targetIndex],
        newGroups[currentIndex],
      ];

      const updatedOrder = newGroups.map((group, index) => ({
        id: group.id,
        order_index: index,
      }));

      // Оптимистичное обновление UI
      setGroups(newGroups.map((g, i) => ({ ...g, order_index: i })));

      // Отправляем изменения на сервер
      reorder.mutate(
        updatedOrder.map((u) => ({ id: u.id, orderIndex: u.order_index })),
        {
          onError: (error) => {
            console.error("Ошибка при изменении порядка групп:", error);
            // toast.error("Не удалось изменить порядок групп"); // Уведомление в мутации
            // Возвращаем исходное состояние при ошибке (опционально, зависит от UX)
            if (groupsData?.data) {
              setGroups(groupsData.data);
            }
          },
        }
      );
    },
    [groups, reorder, groupsData?.data, setGroups] // Добавили setGroups
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Управление группами продуктов"
      size="lg"
      bodyClassName="p-6"
      footer={
        <div className="flex justify-end gap-2">
          <Button variant="secondary" onClick={onClose}>
            Закрыть
          </Button>
        </div>
      }
    >
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Добавить новую группу</h3>
        <div className="flex items-center gap-2">
          <Input
            id="newGroupName"
            placeholder="Название группы"
            value={newGroupName}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setNewGroupName(e.target.value)
            }
            onKeyDown={(e: React.KeyboardEvent) =>
              e.key === "Enter" && handleAddGroup()
            }
            className="h-9 flex-1"
          />
          <Button
            variant="default"
            size="default"
            onClick={handleAddGroup}
            title="Добавить группу"
            disabled={create.isPending || !newGroupName.trim()}
            className="h-9"
          >
            <Plus size={18} className="mr-1" />
            {create.isPending ? "Добавление..." : "Добавить"}
          </Button>
        </div>
      </div>

      <Divider className="my-6" />

      <div className="mb-4 space-y-2">
        <div className="mb-2">
          <h3 className="text-lg font-semibold">Существующие группы</h3>
          <p className="text-xs text-gray-500">
            Двойной клик для редактирования имени. Используйте кнопки для
            управления.
          </p>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-20">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900" />
          </div>
        ) : groups.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            Нет созданных групп
          </div>
        ) : (
          <div className="border rounded-md">
            {groups.map((group, index) => (
              <div
                key={group.id}
                className="flex items-center justify-between gap-2 md:gap-4 py-3 px-4 border-b last:border-b-0 hover:bg-gray-50"
              >
                <div className="flex items-center gap-2 md:gap-3 flex-1 min-w-0">
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="hidden sm:inline-block cursor-grab text-gray-400 hover:text-gray-600">
                          <GripVertical size={18} />
                        </span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Перетащить для изменения порядка (не реализовано)</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  {isEditingId === group.id ? (
                    <Input
                      value={editingName}
                      onChange={handleInputChange}
                      onKeyDown={(e) => handleKeyDown(e, group.id)}
                      onBlur={() => handleBlur(group.id)}
                      className="text-sm h-8 flex-1"
                      autoFocus
                    />
                  ) : (
                    <span
                      className="text-sm flex-1 cursor-pointer hover:text-blue-600 truncate"
                      onDoubleClick={() => handleStartEdit(group)}
                      title={`${group.name} (Двойной клик для редактирования)`}
                    >
                      {group.name}
                    </span>
                  )}
                </div>

                <div className="flex items-center gap-1 flex-shrink-0">
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <IconButton
                          icon={<ArrowUp size={16} />}
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMoveGroup(group.id, "up")}
                          disabled={index === 0 || reorder.isPending}
                          className={cn(
                            "p-1",
                            index === 0 && "opacity-30 cursor-not-allowed"
                          )}
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Переместить вверх</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <IconButton
                          icon={<ArrowDown size={16} />}
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMoveGroup(group.id, "down")}
                          disabled={
                            index === groups.length - 1 || reorder.isPending
                          }
                          className={cn(
                            "p-1",
                            index === groups.length - 1 &&
                              "opacity-30 cursor-not-allowed"
                          )}
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Переместить вниз</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <IconButton
                          icon={<Pencil size={16} />}
                          variant="ghost"
                          size="sm"
                          onClick={() => handleStartEdit(group)}
                          disabled={isEditingId === group.id}
                          className="p-1"
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Редактировать имя</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <IconButton
                          icon={
                            remove.isPending &&
                            remove.variables === group.id ? (
                              <div className="animate-spin h-4 w-4 border-2 border-red-600 rounded-full border-t-transparent" />
                            ) : (
                              <Trash2 size={16} />
                            )
                          }
                          variant="ghost"
                          size="sm"
                          className="p-1 text-red-600 hover:bg-red-100"
                          onClick={() => handleDeleteGroup(group)}
                          disabled={remove.isPending}
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Удалить группу</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </Modal>
  );
}
</file>

<file path="src/components/ui/Input.tsx">
import * as React from "react";

import { cn } from "@/lib/utils";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, label, id, ...props }, ref) => {
    return (
      <div className="space-y-2">
        {label && (
          <label
            htmlFor={id}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
          </label>
        )}
        <input
          type={type}
          id={id}
          className={cn(
            "flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
            className
          )}
          ref={ref}
          {...props}
        />
      </div>
    );
  }
);
Input.displayName = "Input";

export { Input };
</file>

<file path="src/db/relations.ts">
import { relations } from "drizzle-orm/relations";
import { products, productImages, productGroups } from "./schema";

export const productImagesRelations = relations(productImages, ({ one }) => ({
  product: one(products, {
    fields: [productImages.productId],
    references: [products.id],
  }),
}));

export const productsRelations = relations(products, ({ one, many }) => ({
  productImages: many(productImages),
  productGroup: one(productGroups, {
    fields: [products.groupId],
    references: [productGroups.id],
  }),
}));

export const productGroupsRelations = relations(productGroups, ({ many }) => ({
  products: many(products),
}));
</file>

<file path="src/components/products/columns/useProductColumns.ts">
import { useCallback, useMemo } from "react";
import { ColDef } from "ag-grid-community";
import { useColumnSettings } from "../../../hooks/useColumnSettings";
import { isCustomField, getFieldPath } from "./productColumnUtils";
import {
  ImageCellRenderer,
  CustomFieldCellRenderer,
  VisibilityStatusRenderer,
} from "../grid";
import {
  useProductGroups,
  ProductGroup,
} from "../../../hooks/useProductGroups";

import type { Database } from "../../../types/supabase";

type ColumnSettings = Database["public"]["Tables"]["column_settings"]["Row"];

export function useProductColumns() {
  const { data: columnSettingsData } = useColumnSettings();
  const { data: groupsData } = useProductGroups(); // Получаем данные о группах

  // Создаем Map из групп для быстрого доступа
  const groupsMap = useMemo(() => {
    if (!groupsData?.data) return new Map<string, ProductGroup>();
    return new Map(groupsData.data.map((group) => [group.id, group]));
  }, [groupsData?.data]);

  // Функция для создания определения столбца на основе настроек
  const createColumnDef = useCallback(
    (column: ColumnSettings): ColDef => {
      const isNumberField = column.data_type === "number";
      const isColumnCustomField = isCustomField(column.name);
      const fieldPath = getFieldPath(column.name);

      const { prefix, postfix, name: columnName, display_name } = column;

      const colDef: ColDef = {
        field: fieldPath,
        headerName: display_name,
        editable: true,
        cellEditorParams: isColumnCustomField
          ? {
              isCustomField: true,
              dataType: isNumberField ? "number" : "text",
            }
          : undefined,
        filter: isNumberField ? "agNumberColumnFilter" : "agTextColumnFilter",
        flex: 1,
      };

      // Общие настройки для числовых полей (редактор, парсер)
      if (isNumberField) {
        colDef.cellEditor = "agNumberCellEditor";
        colDef.valueParser = (params) => {
          const parsedValue = parseFloat(params.newValue);
          return isNaN(parsedValue) ? null : parsedValue;
        };
        colDef.cellEditorPopup = false;
        colDef.cellEditorPopupPosition = "under";
      }

      // Применение valueFormatter ТОЛЬКО для стандартных числовых полей
      if (isNumberField && !isColumnCustomField) {
        colDef.valueFormatter = (params) => {
          if (params.value === null || params.value === undefined) return "";
          const currentPrefix = prefix || "";
          const currentPostfix = postfix || "";
          let valueStr = String(params.value);
          if (currentPostfix === "₽") {
            const numValue = Number(params.value);
            if (!isNaN(numValue)) {
              valueStr = numValue.toFixed(2);
            }
          }
          return `${currentPrefix} ${valueStr} ${currentPostfix}`;
        };
      }

      // Настройки для стандартных НЕчисловых полей или специфичные рендереры
      if (!isColumnCustomField) {
        switch (columnName) {
          case "is_hidden":
            colDef.filter = "agSetColumnFilter";
            colDef.cellRenderer = VisibilityStatusRenderer;
            break;
          case "product_images":
            colDef.filter = false;
            colDef.sortable = false;
            colDef.cellRenderer = ImageCellRenderer;
            break;
          case "created_at":
            colDef.filter = "agDateColumnFilter";
            colDef.valueFormatter = (params) =>
              new Date(params.value).toLocaleDateString();
            break;
          case "group_id":
            colDef.headerName = "Группа";
            colDef.cellEditor = "agSelectCellEditor";
            colDef.cellEditorParams = {
              values: groupsData?.data?.map((g) => g.id) || [],
            };
            colDef.valueFormatter = (params) => {
              const groupId = params.value;
              if (!groupId) return "Без группы";
              const group = groupsMap.get(groupId);
              return group ? group.name : "Без группы";
            };
            break;
        }
      } else {
        // Для пользовательских полей
        colDef.cellRenderer = CustomFieldCellRenderer;
        colDef.cellRendererParams = {
          prefix: prefix,
          postfix: postfix,
          dataType: isNumberField ? "number" : "text",
        };
      }

      return colDef;
    },
    [groupsData?.data, groupsMap]
  );

  // Генерация столбцов на основе настроек
  const columnDefs = useMemo<ColDef[]>(() => {
    // Первый столбец с селектором всегда присутствует
    const baseColumns: ColDef[] = [
      {
        headerCheckboxSelection: true,
        checkboxSelection: true,
        width: 50,
        pinned: "left",
      },
    ];

    // Если настройки столбцов загружены, используем их
    if (columnSettingsData?.data && columnSettingsData.data.length > 0) {
      const customColumns = columnSettingsData.data
        .filter((column) => column.is_visible)
        .sort((a, b) => a.order_index - b.order_index)
        .map((column) => createColumnDef(column));

      return [...baseColumns, ...customColumns];
    }

    // Иначе возвращаем стандартные столбцы
    return [
      ...baseColumns,
      {
        field: "name",
        headerName: "Наименование",
        editable: true,
        filter: "agTextColumnFilter",
        flex: 2,
      },
      // Добавляем колонку группы в стандартный набор
      {
        field: "group_id",
        headerName: "Группа",
        editable: true,
        filter: "agTextColumnFilter",
        cellEditor: "agSelectCellEditor",
        cellEditorParams: {
          values: groupsData?.data?.map((g) => g.id) || [],
        },
        valueFormatter: (params) => {
          const groupId = params.value;
          if (!groupId) return "Без группы";
          const group = groupsMap.get(groupId);
          return group ? group.name : "Без группы";
        },
        flex: 1,
      },
      {
        field: "weight",
        headerName: "Упаковка",
        editable: true,
        filter: "agNumberColumnFilter",
        flex: 1,
      },
      {
        field: "validity_period",
        headerName: "Сроки реализации",
        editable: true,
        filter: "agNumberColumnFilter",
        flex: 1,
      },
      {
        field: "price",
        headerName: "Цена",
        editable: true,
        filter: "agNumberColumnFilter",
        flex: 1,
      },
      {
        field: "is_hidden",
        headerName: "Статус",
        filter: "agSetColumnFilter",
        cellRenderer: VisibilityStatusRenderer,
        flex: 1,
      },
      {
        field: "product_images",
        headerName: "Изображения",
        filter: false,
        sortable: false,
        cellRenderer: ImageCellRenderer,
        flex: 1,
      },
      {
        field: "created_at",
        headerName: "Дата добавления",
        filter: "agDateColumnFilter",
        valueFormatter: (params) => new Date(params.value).toLocaleDateString(),
        flex: 1,
      },
    ];
  }, [columnSettingsData?.data, createColumnDef, groupsData?.data, groupsMap]);

  const defaultColDef = useMemo(
    () => ({
      sortable: true,
      resizable: true,
    }),
    []
  );

  return {
    columnDefs,
    defaultColDef,
    createColumnDef,
  };
}
</file>

<file path="src/components/products/export/pdfExportUtils.ts">
import { ColDef } from "ag-grid-community";
import type { Database } from "../../../types/supabase";

// Определяем тип для настроек колонки из БД, если он еще не импортирован глобально
type DbColumnSetting = Database["public"]["Tables"]["column_settings"]["Row"];

// Интерфейсы для настроек экспорта PDF
export interface PDFHeaderSettings {
  date: string;
  companyName: string;
  office: string;
  officePhone: string;
  warehouse: string;
  warehousePhone: string;
  managerName: string;
  managerContact: string;
  deliveryInfo: string[];
}

export interface PDFExportColumnSettings {
  field: string;
  headerName: string;
  selected: boolean;
  dataType?: "text" | "number";
  prefix?: string | null;
  postfix?: string | null;
}

export interface PDFExportSettings {
  columns: PDFExportColumnSettings[];
  header: PDFHeaderSettings;
  includeGroups: boolean;
}

// Начальные настройки заголовка (переименовываем для ясности)
export const initialHeaderSettings: PDFHeaderSettings = {
  date: new Date().toLocaleDateString("ru-RU"),
  companyName: "ООО Эдельвейс",
  office: "г. Санкт-Петербург, ул. Магнитогорская, 51",
  officePhone: "(812) 313-26-36",
  warehouse: "г. Санкт-Петербург, п. Шушары, Московское шоссе, д.82",
  warehousePhone: "+79119723492",
  managerName: "",
  managerContact: "",
  deliveryInfo: [
    "ДОСТАВКА ДО 500КГ ПО СПБ 5 РУБ/КГ(мин. партия 200кг)",
    "ДОСТАВКА ОТ 500КГ ДО 1000КГ ПО СПБ З РУБ/КГ",
    "ДОСТАВКА ОТ 1000КГ ПО СПБ 1 РУБ/КГ (в том числе по акции)",
  ],
};

// Дефолтные настройки столбцов (переименовываем для ясности)
export const initialColumnSettings: PDFExportColumnSettings[] = [
  {
    field: "name",
    headerName: "Наименование",
    selected: true,
    prefix: null,
    postfix: null,
  },
  {
    field: "weight",
    headerName: "Упаковка",
    selected: true,
    prefix: null,
    postfix: "кг",
  },
  {
    field: "validity_period",
    headerName: "Сроки реализации",
    selected: true,
    prefix: null,
    postfix: "мес.",
  },
  {
    field: "price",
    headerName: "Цена",
    selected: true,
    prefix: null,
    postfix: "₽",
  },
];

// Комплексные начальные настройки
export const initialPDFSettings: PDFExportSettings = {
  columns: initialColumnSettings,
  header: initialHeaderSettings,
  includeGroups: true,
};

// Функция для получения настроек столбцов на основе существующих определений столбцов
export const getColumnSettingsFromDefs = (
  columnDefs: ColDef[],
  allDbColumnSettings: DbColumnSetting[] = []
): PDFExportColumnSettings[] => {
  if (!columnDefs || columnDefs.length === 0) {
    if (allDbColumnSettings.length > 0) {
      return allDbColumnSettings
        .filter((setting) => setting.is_visible && setting.name)
        .sort((a, b) => a.order_index - b.order_index)
        .map((setting) => ({
          field: setting.name.startsWith("field_")
            ? `custom_fields.${setting.name}`
            : setting.name,
          headerName: setting.display_name,
          selected: true,
          dataType: setting.data_type === "number" ? "number" : "text",
          prefix: setting.prefix,
          postfix: setting.postfix,
        }));
    }
    return initialColumnSettings;
  }

  return columnDefs
    .filter((col) => col.field && !col.headerCheckboxSelection)
    .map((colDef) => {
      const fieldName = colDef.field!;
      let originalName = fieldName;
      if (fieldName.startsWith("custom_fields.")) {
        originalName = fieldName.split(".")[1];
      }

      const dbSetting = allDbColumnSettings.find(
        (s) => s.name === originalName
      );

      let dataType: "text" | "number" = "text";
      if (dbSetting) {
        dataType = dbSetting.data_type === "number" ? "number" : "text";
      } else if (
        colDef.cellEditorParams?.dataType === "number" ||
        colDef.cellEditor === "agNumberCellEditor" ||
        ["price", "weight", "validity_period"].includes(originalName)
      ) {
        dataType = "number";
      }

      return {
        field: fieldName,
        headerName: colDef.headerName || fieldName,
        selected: true,
        dataType: dataType,
        prefix: dbSetting?.prefix ?? null,
        postfix: dbSetting?.postfix ?? null,
      };
    });
};

// Функция загрузки настроек из localStorage
export const loadPDFSettings = (): PDFExportSettings => {
  let settings: PDFExportSettings | null = null;

  try {
    const savedSettings = localStorage.getItem("pdfExportSettings");
    if (savedSettings) {
      const parsed = JSON.parse(savedSettings);
      settings = {
        ...initialPDFSettings,
        ...parsed,
        header: {
          ...initialPDFSettings.header,
          ...(parsed.header || {}),
        },
        columns: parsed.columns || initialPDFSettings.columns,
        includeGroups:
          parsed.includeGroups !== undefined
            ? parsed.includeGroups
            : initialPDFSettings.includeGroups,
      };
    }
  } catch (error) {
    console.error("Ошибка при загрузке настроек PDF:", error);
  }

  return settings || initialPDFSettings;
};

// Функция сохранения настроек в localStorage
export const savePDFSettings = (settings: PDFExportSettings) => {
  try {
    localStorage.setItem("pdfExportSettings", JSON.stringify(settings));
  } catch (error) {
    console.error("Ошибка при сохранении настроек PDF:", error);
  }
};
</file>

<file path="src/components/products/grid/ImageCellRenderer.tsx">
import { useState, useCallback, useRef, useEffect } from "react";
import { ICellRendererParams } from "ag-grid-community";
import type { Database } from "../../../types/supabase";
import {
  uploadFilesAndPublish,
  getPublicFolderContents,
  getDirectFileUrl,
  deleteFile,
  getUploadUrl,
  uploadFile,
} from "../../../services/yandexDisk";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useProductMutations } from "@/hooks/useProducts";
import { ImageIcon, X, Upload, Trash2 } from "lucide-react";
import { supabase } from "@/lib/supabase";

type Product = Database["public"]["Tables"]["products"]["Row"];

interface FolderItem {
  name: string;
  preview?: string;
  public_url?: string;
  type: "dir" | "file";
  directUrl?: string;
}

// Ключи для localStorage
const DIALOG_STATE_KEY = "image_dialog_state";
const CURRENT_PRODUCT_KEY = "image_dialog_product_id";

export const ImageCellRenderer = (props: ICellRendererParams<Product>) => {
  const { data } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [publicUrl, setPublicUrl] = useState<string | null>(null);
  const [folderPath, setFolderPath] = useState<string | null>(null);
  const [folderName, setFolderName] = useState<string | null>(null);
  const [folderContents, setFolderContents] = useState<FolderItem[]>([]);
  const [isLoadingContents, setIsLoadingContents] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isDeletingAll, setIsDeletingAll] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const additionalFileInputRef = useRef<HTMLInputElement>(null);

  const { updateImages } = useProductMutations();

  // Добавляем флаг для предотвращения закрытия диалога при обновлении данных
  const isOperationInProgress = useRef(false);

  // Проверяем наличие изображений в базе данных
  const hasImages = data?.id && publicUrl;

  // Инициализация данных при монтировании компонента
  useEffect(() => {
    // Вызываем инициализацию данных при первом рендере
    if (data?.id) {
      initializeData();
    }
  }, [data?.id]); // Зависимость от ID продукта, чтобы перезагружать данные при изменении продукта

  // Получение содержимого папки
  const fetchFolderContents = useCallback(async (url: string) => {
    setIsLoadingContents(true);
    try {
      const items = await getPublicFolderContents(url);
      // Фильтруем только файлы изображений
      const imageItems = items
        .filter(
          (item) =>
            item.type === "file" &&
            item.name.match(/\.(jpeg|jpg|png|gif|webp)$/i)
        )
        .map((item) => ({
          name: item.name,
          preview: item.preview,
          public_url: item.public_url,
          type: item.type,
        }));

      setFolderContents(imageItems);
    } catch (err) {
      setError(
        err instanceof Error
          ? `Ошибка получения содержимого папки: ${err.message}`
          : "Произошла ошибка при получении содержимого папки"
      );
    } finally {
      setIsLoadingContents(false);
    }
  }, []);

  // Теперь добавляем эффект для проверки сохраненного состояния диалога
  useEffect(() => {
    const savedDialogState = localStorage.getItem(DIALOG_STATE_KEY);
    const savedProductId = localStorage.getItem(CURRENT_PRODUCT_KEY);

    // Открываем диалог, только если он был открыт для текущего продукта
    if (savedDialogState === "open" && savedProductId === String(data?.id)) {
      setIsOpen(true);
      if (publicUrl) {
        fetchFolderContents(publicUrl);
      }
    }
  }, [data?.id, fetchFolderContents, publicUrl]);

  // Получаем и сохраняем информацию об изображениях
  const saveImagesToDb = useCallback(
    async (publicUrl: string, targetFolder: string) => {
      if (!data?.id) return;

      try {
        isOperationInProgress.current = true;

        // Убедимся, что диалог остается открытым
        localStorage.setItem(DIALOG_STATE_KEY, "open");
        if (data?.id) {
          localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
        }

        const items = await getPublicFolderContents(publicUrl);

        // Фильтруем только файлы изображений
        const imageItems = items
          .filter(
            (item) =>
              item.type === "file" &&
              item.name.match(/\.(jpeg|jpg|png|gif|webp)$/i)
          )
          .map((item, index) => ({
            product_id: data.id,
            yandex_disk_path: `/${targetFolder}/${item.name}`,
            public_url: publicUrl,
            sort_order: index,
            is_main: index === 0, // Первое изображение - главное
          }));

        // Сохраняем в базе данных
        await updateImages.mutate({
          productId: data.id,
          images: imageItems,
        });

        // Повторно убедимся, что диалог остается открытым после мутации
        setIsOpen(true);

        isOperationInProgress.current = false;
        return items;
      } catch (err) {
        isOperationInProgress.current = false;
        console.error("Ошибка при сохранении изображений в БД:", err);
        setError(
          err instanceof Error
            ? err.message
            : "Произошла ошибка при сохранении изображений"
        );
        return null;
      }
    },
    [data?.id, updateImages]
  );

  // Загрузка изображений при первой загрузке
  const handleUploadImages = useCallback(
    async (files: File[]) => {
      if (!data?.id) {
        setError("ID товара не найден");
        return;
      }

      // Проверяем, что все файлы - изображения
      const isAllImages = files.every((file) => file.type.startsWith("image/"));
      if (!isAllImages) {
        setError("Пожалуйста, загружайте только изображения");
        return;
      }

      setIsLoading(true);
      setError(null);
      isOperationInProgress.current = true;

      // Сохраняем состояние диалога
      localStorage.setItem(DIALOG_STATE_KEY, "open");
      if (data?.id) {
        localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
      }

      try {
        // Создаем имя папки на основе ID товара и времени
        const timestamp = new Date().getTime();
        const targetFolder = `product_${data.id}_${timestamp}`;
        const path = `/${targetFolder}`;

        // Загружаем файлы
        const url = await uploadFilesAndPublish(files, targetFolder);
        setPublicUrl(url);
        setFolderPath(path);
        setFolderName(targetFolder);

        // Сохраняем информацию в базе данных
        const items = await saveImagesToDb(url, targetFolder);

        // Получаем содержимое папки
        if (items) {
          // Фильтруем только файлы изображений
          const imageItems = items
            .filter(
              (item) =>
                item.type === "file" &&
                item.name.match(/\.(jpeg|jpg|png|gif|webp)$/i)
            )
            .map((item) => ({
              name: item.name,
              preview: item.preview,
              public_url: item.public_url,
              type: item.type,
            }));

          setFolderContents(imageItems);
        }

        // После всех операций явно устанавливаем состояние диалога
        setIsOpen(true);

        setSuccessMessage("Изображения успешно загружены");
        setTimeout(() => setSuccessMessage(null), 3000);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Произошла ошибка при загрузке"
        );
      } finally {
        setIsLoading(false);
        isOperationInProgress.current = false;
      }
    },
    [data?.id, saveImagesToDb]
  );

  // Инициализация данных при первом открытии
  const initializeData = useCallback(async () => {
    if (!data?.id) return;

    try {
      // Получаем изображения для продукта
      const { data: productImages, error } = await supabase
        .from("product_images")
        .select("*")
        .eq("product_id", data.id)
        .order("sort_order", { ascending: true });

      if (error) {
        console.error("Ошибка при получении изображений продукта:", error);
        return;
      }

      if (productImages && productImages.length > 0) {
        // Берем информацию из первого изображения
        const firstImage = productImages[0];
        setPublicUrl(firstImage.public_url);

        // Извлекаем folderPath из yandex_disk_path
        const pathMatch = firstImage.yandex_disk_path.match(/^\/([^/]+)/);
        if (pathMatch && pathMatch[1]) {
          const folder = pathMatch[1];
          setFolderName(folder);
          setFolderPath(`/${folder}`);
          console.log("Установлен путь к папке:", `/${folder}`);
        }
      }
    } catch (err) {
      console.error("Ошибка при инициализации данных:", err);
    }
  }, [data?.id]);

  // Догрузка дополнительных файлов
  const handleUploadMore = useCallback(
    async (files: File[]) => {
      if (files.length === 0 || !folderName || !publicUrl || !data?.id) {
        console.error("Не хватает данных для догрузки файлов:", {
          filesCount: files.length,
          folderName,
          publicUrl,
          productId: data?.id,
          folderPath,
        });
        return;
      }

      // Проверяем, что все файлы - изображения
      const isAllImages = files.every((file) => file.type.startsWith("image/"));
      if (!isAllImages) {
        setError("Пожалуйста, загружайте только изображения");
        return;
      }

      setIsUploading(true);
      setError(null);
      setSuccessMessage(null);
      isOperationInProgress.current = true;

      // Сохраняем состояние диалога
      localStorage.setItem(DIALOG_STATE_KEY, "open");
      if (data?.id) {
        localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
      }

      try {
        console.log("Начинаем догрузку файлов в папку:", folderName);
        // Загружаем файлы по одному в существующую папку
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          setUploadProgress(
            `Загрузка ${i + 1} из ${files.length}: ${file.name}`
          );

          const filePath = `/${folderName}/${file.name}`;
          console.log("Загружаем файл по пути:", filePath);
          const uploadUrl = await getUploadUrl(filePath);
          await uploadFile(uploadUrl, file);
        }

        setUploadProgress(null);
        setSuccessMessage(
          `Дополнительные файлы успешно загружены (${files.length} шт.)`
        );

        // Обновляем информацию в базе данных
        await saveImagesToDb(publicUrl, folderName);

        // Обновляем содержимое папки
        await fetchFolderContents(publicUrl);

        // После всех операций явно устанавливаем состояние диалога
        setIsOpen(true);

        // Скрываем сообщение через 3 секунды
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        console.error("Ошибка при догрузке файлов:", err);
        setError(
          err instanceof Error
            ? `Ошибка загрузки дополнительных файлов: ${err.message}`
            : "Произошла ошибка при загрузке дополнительных файлов"
        );
        setUploadProgress(null);
      } finally {
        setIsUploading(false);
        isOperationInProgress.current = false;
      }
    },
    [
      folderName,
      publicUrl,
      data?.id,
      fetchFolderContents,
      saveImagesToDb,
      folderPath,
    ]
  );

  // Удаление файла
  const handleDeleteImage = useCallback(
    async (item: FolderItem, e: React.MouseEvent) => {
      e.stopPropagation(); // Предотвращаем открытие изображения при клике на кнопку удаления

      if (!folderPath) {
        console.error("Путь к папке не найден при удалении изображения");
        setError("Путь к папке не найден");
        return;
      }

      // Формируем полный путь к файлу
      const filePath = `${folderPath}/${item.name}`;
      console.log("Попытка удалить файл по пути:", filePath);

      if (
        window.confirm(`Вы действительно хотите удалить файл ${item.name}?`)
      ) {
        setIsDeleting(item.name);
        setError(null);
        setSuccessMessage(null);
        isOperationInProgress.current = true;

        // Сохраняем состояние диалога
        localStorage.setItem(DIALOG_STATE_KEY, "open");
        if (data?.id) {
          localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
        }

        try {
          await deleteFile(filePath);
          // Обновляем список файлов, удаляя удаленный файл
          setFolderContents((prev) => prev.filter((i) => i.name !== item.name));

          // Обновляем информацию в базе данных
          if (publicUrl && folderName && data?.id) {
            await saveImagesToDb(publicUrl, folderName);
          }

          setSuccessMessage(`Файл ${item.name} успешно удален`);

          // После всех операций явно устанавливаем состояние диалога
          setIsOpen(true);

          // Скрываем сообщение через 3 секунды
          setTimeout(() => {
            setSuccessMessage(null);
          }, 3000);
        } catch (err) {
          console.error("Ошибка при удалении файла:", err);
          setError(
            err instanceof Error
              ? `Ошибка удаления файла: ${err.message}`
              : "Произошла ошибка при удалении файла"
          );
        } finally {
          setIsDeleting(null);
          isOperationInProgress.current = false;
        }
      }
    },
    [folderPath, publicUrl, folderName, data?.id, saveImagesToDb]
  );

  // Удаление всех файлов
  const handleDeleteAllImages = useCallback(async () => {
    if (!folderPath || folderContents.length === 0 || !data?.id) {
      console.error("Не хватает данных для удаления всех файлов:", {
        folderPath,
        filesCount: folderContents.length,
        productId: data?.id,
      });
      return;
    }

    if (
      window.confirm(
        `Вы действительно хотите удалить все ${folderContents.length} файлов?`
      )
    ) {
      setIsDeletingAll(true);
      setError(null);
      setSuccessMessage(null);
      isOperationInProgress.current = true;

      // Сохраняем состояние диалога
      localStorage.setItem(DIALOG_STATE_KEY, "open");
      if (data?.id) {
        localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
      }

      try {
        // Удаляем файлы последовательно
        for (const item of folderContents) {
          const filePath = `${folderPath}/${item.name}`;
          console.log("Удаляем файл:", filePath);
          await deleteFile(filePath);
        }

        // Очищаем список файлов
        setFolderContents([]);

        // Удаляем информацию о изображениях из базы данных
        await updateImages.mutate({
          productId: data.id,
          images: [],
        });

        // Очищаем состояния
        setPublicUrl(null);
        setFolderPath(null);
        setFolderName(null);

        // После очистки всех изображений сбрасываем состояние диалога,
        // так как больше нет данных для отображения
        localStorage.removeItem(DIALOG_STATE_KEY);
        localStorage.removeItem(CURRENT_PRODUCT_KEY);

        setSuccessMessage(`Все файлы успешно удалены`);

        // Скрываем сообщение через 3 секунды
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        console.error("Ошибка при удалении всех файлов:", err);
        setError(
          err instanceof Error
            ? `Ошибка удаления файлов: ${err.message}`
            : "Произошла ошибка при удалении файлов"
        );
      } finally {
        setIsDeletingAll(false);
        isOperationInProgress.current = false;
      }
    }
  }, [folderPath, folderContents, data?.id, updateImages]);

  // Открытие диалога для просмотра изображения
  const handleImageClick = useCallback(
    async (item: FolderItem) => {
      if (!publicUrl) {
        setError("Ссылка на публичную папку недоступна");
        return;
      }

      try {
        const directUrl = await getDirectFileUrl(publicUrl, item.name);

        // Обновляем элемент в массиве folderContents с directUrl
        setFolderContents((prev) =>
          prev.map((i) => (i.name === item.name ? { ...i, directUrl } : i))
        );

        setSelectedImage(directUrl);
        setIsImageDialogOpen(true);
      } catch (err) {
        setError(
          err instanceof Error
            ? `Ошибка получения прямой ссылки: ${err.message}`
            : "Произошла ошибка при получении прямой ссылки на изображение"
        );
      }
    },
    [publicUrl]
  );

  // Обработка перетаскивания файлов
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length === 0) return;

      if (publicUrl && folderName) {
        console.log("Догружаем файлы в существующую папку:", folderName);
        // Если папка уже создана, догружаем файлы
        handleUploadMore(files);
      } else {
        console.log("Создаем новую папку для файлов");
        // Если папки нет, создаем новую
        handleUploadImages(files);
      }
    },
    [publicUrl, folderName, handleUploadMore, handleUploadImages]
  );

  // Обработка выбора файлов через input
  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      if (files.length === 0) return;

      if (publicUrl && folderName) {
        console.log("Input: Догружаем файлы в существующую папку:", folderName);
        // Если папка уже создана, догружаем файлы
        handleUploadMore(files);
      } else {
        console.log("Input: Создаем новую папку для файлов");
        // Если папки нет, создаем новую
        handleUploadImages(files);
      }

      // Очищаем input
      if (e.target) {
        e.target.value = "";
      }
    },
    [publicUrl, folderName, handleUploadMore, handleUploadImages]
  );

  // Открытие диалога
  const openDialog = useCallback(() => {
    setIsOpen(true);

    // Если есть publicUrl, загружаем содержимое папки
    if (publicUrl) {
      fetchFolderContents(publicUrl);
    }
  }, [publicUrl, fetchFolderContents]);

  // Обработка клика на элемент выбора файла
  const handleSelectFilesClick = useCallback(() => {
    if (publicUrl) {
      console.log("Открываем диалог выбора файлов для догрузки");
      additionalFileInputRef.current?.click();
    } else {
      console.log("Открываем диалог выбора файлов для первой загрузки");
      fileInputRef.current?.click();
    }
  }, [publicUrl]);

  // Модифицируем handleOpenChange чтобы сохранять состояние
  const handleOpenChange = useCallback(
    (open: boolean) => {
      // Не закрываем диалог если операция в процессе
      if (!open && isOperationInProgress.current) {
        return;
      }

      setIsOpen(open);

      // Сохраняем состояние в localStorage
      if (open) {
        localStorage.setItem(DIALOG_STATE_KEY, "open");
        if (data?.id) {
          localStorage.setItem(CURRENT_PRODUCT_KEY, String(data.id));
        }
        initializeData();
      } else {
        localStorage.removeItem(DIALOG_STATE_KEY);
        localStorage.removeItem(CURRENT_PRODUCT_KEY);
      }
    },
    [initializeData, data?.id]
  );

  // Рендер ячейки
  return (
    <>
      <div
        onClick={openDialog}
        className="w-full h-full flex items-center justify-center cursor-pointer hover:bg-gray-50"
      >
        {hasImages ? (
          <div className="flex items-center gap-2">
            <ImageIcon size={18} />
            <span>Просмотреть фото</span>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Upload size={18} />
            <span>Загрузить</span>
          </div>
        )}
      </div>

      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Управление изображениями товара</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Область перетаскивания */}
            <div
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleSelectFilesClick}
              className={`
                border-2 border-dashed rounded-md p-6 text-center cursor-pointer transition-colors
                ${
                  isDragging
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-300 bg-gray-50"
                }
                ${isLoading ? "opacity-70 pointer-events-none" : ""}
              `}
            >
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileSelect}
                ref={fileInputRef}
                className="hidden"
              />
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileSelect}
                ref={additionalFileInputRef}
                className="hidden"
              />

              {isLoading ? (
                <div className="text-sm text-gray-500">Загрузка...</div>
              ) : publicUrl ? (
                <div className="text-sm text-gray-500">
                  Перетащите изображения сюда или кликните для догрузки
                </div>
              ) : (
                <div className="text-sm text-gray-500">
                  Перетащите изображения сюда или кликните для выбора
                </div>
              )}
            </div>

            {/* Сообщения */}
            {error && (
              <div className="text-sm text-red-500 p-2 bg-red-50 rounded-md">
                {error}
              </div>
            )}

            {successMessage && (
              <div className="text-sm text-green-500 p-2 bg-green-50 rounded-md">
                {successMessage}
              </div>
            )}

            {uploadProgress && (
              <div className="text-sm text-blue-500 p-2 bg-blue-50 rounded-md">
                {uploadProgress}
              </div>
            )}

            {/* Индикаторы загрузки */}
            {isUploading && !uploadProgress && (
              <div className="text-sm text-blue-500 p-2 bg-blue-50 rounded-md flex items-center gap-2">
                <svg
                  className="animate-spin h-4 w-4 text-blue-500"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Загрузка дополнительных файлов...
              </div>
            )}

            {isLoadingContents && (
              <div className="text-sm text-blue-500 p-2 bg-blue-50 rounded-md flex items-center gap-2">
                <svg
                  className="animate-spin h-4 w-4 text-blue-500"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Загрузка содержимого папки...
              </div>
            )}

            {/* Публичная ссылка */}
            {publicUrl && (
              <div className="text-sm p-3 bg-gray-50 rounded-md break-all">
                <p className="font-medium mb-1">Ссылка на загруженные файлы:</p>
                <a
                  href={publicUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  {publicUrl}
                </a>
                {folderPath && (
                  <p className="text-xs text-gray-500 mt-1">
                    Путь к папке: {folderPath}
                  </p>
                )}
              </div>
            )}

            {/* Превью изображений */}
            {folderContents.length > 0 && (
              <div>
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-lg font-medium">
                    Загруженные изображения:
                  </h3>
                  <button
                    onClick={handleDeleteAllImages}
                    disabled={isDeletingAll}
                    className="flex items-center gap-1 px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
                  >
                    <Trash2 size={14} />
                    {isDeletingAll ? "Удаление..." : "Удалить все"}
                  </button>
                </div>

                {isLoadingContents ? (
                  <div className="p-8 text-center">
                    <svg
                      className="animate-spin h-8 w-8 text-blue-500 mx-auto mb-2"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <p className="text-blue-500">Загрузка изображений...</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {folderContents.map((item) => (
                      <div
                        key={item.name}
                        onClick={() => handleImageClick(item)}
                        className="relative border rounded-md overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                      >
                        <div
                          className="w-full h-32 bg-cover bg-center bg-gray-100 flex items-center justify-center"
                          style={{
                            backgroundImage: item.preview
                              ? `url(${item.preview})`
                              : "none",
                          }}
                        >
                          {!item.preview && (
                            <span className="text-xs text-gray-500">
                              Превью недоступно
                            </span>
                          )}
                        </div>
                        <div className="p-2 text-xs truncate">{item.name}</div>
                        <button
                          onClick={(e) => handleDeleteImage(item, e)}
                          disabled={isDeleting === item.name}
                          className="absolute top-1 right-1 w-6 h-6 rounded-full bg-red-500 text-white flex items-center justify-center hover:bg-red-600"
                        >
                          {isDeleting === item.name ? "..." : <X size={14} />}
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Диалог для просмотра изображения */}
      <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Просмотр изображения</DialogTitle>
          </DialogHeader>
          <div className="flex justify-center items-center overflow-auto max-h-[70vh] my-4">
            {selectedImage && (
              <img
                src={selectedImage}
                alt="Просмотр изображения"
                className="max-w-full max-h-[70vh] object-contain"
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
</file>

<file path="src/hooks/useImageUpload.ts">
import { useState } from "react";
import { uploadFilesAndPublish } from "../services/yandexDisk";
import { useProductMutations } from "./useProducts";

interface UseImageUploadProps {
  productId: string;
}

export const useImageUpload = ({ productId }: UseImageUploadProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { updateImages } = useProductMutations();

  // Проверка, что все файлы - изображения
  const validateImages = (files: File[]): boolean => {
    const isAllImages = files.every((file) => file.type.startsWith("image/"));
    if (!isAllImages) {
      setError("Пожалуйста, загружайте только изображения");
      return false;
    }
    return true;
  };

  // Загрузка изображений
  const uploadImages = async (files: File[]): Promise<string | null> => {
    if (files.length === 0) return null;

    if (!validateImages(files)) return null;

    setIsLoading(true);
    setError(null);

    try {
      const url = await uploadFilesAndPublish(files);

      // Обновляем запись в базе данных
      await updateImages.mutateAsync({
        productId,
        images: [
          {
            yandex_disk_path: url,
            public_url: url,
            sort_order: 1,
            is_main: true,
            product_id: productId,
          },
        ],
      });

      return url;
    } catch (err) {
      setError(err instanceof Error ? err.message : "Ошибка при загрузке");
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    error,
    setError,
    uploadImages,
    validateImages,
  };
};
</file>

<file path="src/hooks/useProductGroups.ts">
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "../lib/supabase";
import type { Database } from "../types/supabase";
import { toast } from "@/hooks/use-toast";

export type ProductGroup =
  Database["public"]["Tables"]["product_groups"]["Row"];

export function useProductGroups() {
  return useQuery({
    queryKey: ["product_groups"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("product_groups")
        .select("*")
        .order("order_index", { ascending: true });

      if (error) throw error;
      return { data: data as ProductGroup[] };
    },
  });
}

export function useProductGroupMutations() {
  const queryClient = useQueryClient();

  const create = useMutation({
    mutationFn: async (
      group: Omit<
        Database["public"]["Tables"]["product_groups"]["Insert"],
        "id"
      >
    ) => {
      const { data, error } = await supabase
        .from("product_groups")
        .insert(group)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product_groups"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast({ title: "Успех", description: "Группа создана" });
    },
    onError: (error) => {
      toast({
        title: "Ошибка",
        description: `Ошибка создания группы: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const update = useMutation({
    mutationFn: async ({
      id,
      updates,
    }: {
      id: string;
      updates: Partial<
        Database["public"]["Tables"]["product_groups"]["Update"]
      >;
    }) => {
      const { data, error } = await supabase
        .from("product_groups")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product_groups"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
    onError: (error) => {
      toast({
        title: "Ошибка",
        description: `Ошибка обновления группы: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const remove = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("product_groups")
        .delete()
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product_groups"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast({ title: "Успех", description: "Группа удалена" });
    },
    onError: (error) => {
      toast({
        title: "Ошибка",
        description: `Ошибка удаления группы: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const reorder = useMutation({
    mutationFn: async (groups: { id: string; orderIndex: number }[]) => {
      // Обновляем порядок групп с помощью транзакции
      const promises = groups.map(({ id, orderIndex }) =>
        supabase
          .from("product_groups")
          .update({ order_index: orderIndex })
          .eq("id", id)
      );

      await Promise.all(promises);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["product_groups"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast({ title: "Успех", description: "Порядок групп обновлен" });
    },
    onError: (error) => {
      toast({
        title: "Ошибка",
        description: `Ошибка обновления порядка групп: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Добавляем новую мутацию для массового обновления order_index в группах
  const bulkUpdateOrder = useMutation({
    mutationFn: async (updates: Array<{ id: string; order_index: number }>) => {
      console.log(
        `[GroupBulkUpdate] Начало массового обновления порядка для ${updates.length} групп`
      );

      try {
        // Выполняем обновления через Promise.all
        const promises = updates.map((u) => {
          console.log(
            `[GroupBulkUpdate] Обновление группы ${u.id} -> order_index: ${u.order_index}`
          );
          return supabase
            .from("product_groups")
            .update({ order_index: u.order_index })
            .eq("id", u.id);
        });

        const results = await Promise.allSettled(promises);

        // Проверяем на ошибки более детально
        const failed = results.filter(
          (r) => r.status === "rejected"
        ) as PromiseRejectedResult[];
        const errors = results
          .filter(
            (r) =>
              r.status === "fulfilled" &&
              (r.value as { error: unknown | null }).error !== null
          )
          .map((r) => {
            const result = r as PromiseFulfilledResult<{
              error: unknown;
              status: number;
            }>;
            return {
              error: result.value.error,
              status: result.value.status,
            };
          });

        if (failed.length > 0 || errors.length > 0) {
          console.error(
            "[GroupBulkUpdate] Ошибки при обновлении порядка групп:",
            {
              rejections: failed.map((f) => f.reason),
              apiErrors: errors,
            }
          );

          throw new Error(
            `Не удалось обновить порядок ${
              failed.length + errors.length
            } групп.`
          );
        }

        console.log(
          `[GroupBulkUpdate] Успешно обновлен порядок для ${updates.length} групп`
        );
      } catch (error) {
        console.error(
          "[GroupBulkUpdate] Произошла ошибка при выполнении массового обновления:",
          error
        );
        throw error;
      }
    },
    onSuccess: () => {
      console.log(
        "[GroupBulkUpdate] Инвалидация кеша после успешного обновления порядка групп"
      );
      queryClient.invalidateQueries({ queryKey: ["product_groups"] });
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast({ title: "Успех", description: "Порядок групп обновлен" });
    },
    onError: (error) => {
      console.error("[GroupBulkUpdate] Ошибка в мутации:", error);
      toast({
        title: "Ошибка",
        description: `Ошибка обновления порядка групп: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  return {
    create,
    update,
    remove,
    reorder,
    bulkUpdateOrder,
  };
}
</file>

<file path="src/services/yandexDisk.ts">
import axios, { AxiosError } from "axios";

const YANDEX_DISK_API = "https://cloud-api.yandex.net/v1/disk/resources";
const token = import.meta.env.VITE_YANDEX_DISK_TOKEN;

if (!token) {
  throw new Error("Missing Yandex.Disk token in environment variables");
}

interface UploadUrlResponse {
  href: string;
  method: string;
  templated: boolean;
}

interface PublicUrlResponse {
  public_url: string;
}

interface YandexDiskItem {
  name: string;
  path: string;
  preview?: string;
  public_key?: string;
  type: "dir" | "file";
  resource_id: string;
  public_url?: string;
}

interface YandexDiskPublicFolderResponse {
  _embedded: {
    items: YandexDiskItem[];
  };
  public_key: string;
  public_url: string;
}

/**
 * Получает URL для загрузки файла на Яндекс.Диск
 * @param path - путь, куда будет загружен файл на Яндекс.Диске
 */
export const getUploadUrl = async (path: string): Promise<string> => {
  try {
    const response = await axios.get<UploadUrlResponse>(
      `${YANDEX_DISK_API}/upload`,
      {
        headers: {
          Authorization: `OAuth ${token}`,
        },
        params: {
          path,
        },
      }
    );
    return response.data.href;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка получения URL для загрузки: ${error.message}`);
    }
    throw error;
  }
};

/**
 * Загружает файл на Яндекс.Диск
 * @param uploadUrl - URL для загрузки, полученный из getUploadUrl
 * @param file - файл для загрузки
 */
export const uploadFile = async (
  uploadUrl: string,
  file: File
): Promise<void> => {
  try {
    await axios.put(uploadUrl, file, {
      headers: {
        "Content-Type": file.type,
      },
    });
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка загрузки файла: ${error.message}`);
    }
    throw error;
  }
};

/**
 * Публикует папку на Яндекс.Диске
 * @param path - путь к папке на Яндекс.Диске
 */
export const publishFolder = async (path: string): Promise<void> => {
  try {
    await axios.put(`${YANDEX_DISK_API}/publish`, null, {
      headers: {
        Authorization: `OAuth ${token}`,
      },
      params: {
        path,
      },
    });
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка публикации папки: ${error.message}`);
    }
    throw error;
  }
};

/**
 * Получает публичную ссылку на опубликованный ресурс
 * @param path - путь к ресурсу на Яндекс.Диске
 */
export const getPublicUrl = async (path: string): Promise<string> => {
  try {
    const response = await axios.get<PublicUrlResponse>(YANDEX_DISK_API, {
      headers: {
        Authorization: `OAuth ${token}`,
      },
      params: {
        path,
        fields: "public_url",
      },
    });
    return response.data.public_url;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка получения публичной ссылки: ${error.message}`);
    }
    throw error;
  }
};

/**
 * Создает папку на Яндекс.Диске
 * @param path - путь к создаваемой папке
 */
export const createFolder = async (path: string): Promise<void> => {
  try {
    await axios.put(YANDEX_DISK_API, null, {
      headers: {
        Authorization: `OAuth ${token}`,
      },
      params: {
        path,
      },
    });
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка создания папки: ${error.message}`);
    }
    throw error;
  }
};

/**
 * Получает список файлов из публичной папки
 * @param publicUrl - публичная ссылка на папку
 * @returns список файлов в папке
 */
export const getPublicFolderContents = async (
  publicUrl: string
): Promise<YandexDiskItem[]> => {
  try {
    const response = await axios.get<YandexDiskPublicFolderResponse>(
      "https://cloud-api.yandex.net/v1/disk/public/resources",
      {
        headers: {
          Authorization: `OAuth ${token}`,
        },
        params: {
          public_key: publicUrl,
          fields:
            "_embedded.items.name,_embedded.items.path,_embedded.items.preview,_embedded.items.public_url,_embedded.items.type",
        },
      }
    );

    return response.data._embedded.items;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(
        `Ошибка получения содержимого публичной папки: ${error.message}`
      );
    }
    throw error;
  }
};

/**
 * Получает прямую ссылку на файл для просмотра из публичной ссылки
 * @param publicUrl - публичная ссылка на файл или на папку
 * @param fileName - имя файла (если publicUrl указывает на папку)
 * @returns прямая ссылка на файл для просмотра
 */
export const getDirectFileUrl = async (
  publicUrl: string,
  fileName?: string
): Promise<string> => {
  try {
    const params: Record<string, string> = {
      public_key: publicUrl,
    };

    // Если указано имя файла, добавляем его в параметр path
    if (fileName) {
      params.path = `/${fileName}`;
    }

    const response = await axios.get(
      "https://cloud-api.yandex.net/v1/disk/public/resources/download",
      {
        params,
      }
    );

    return response.data.href;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(
        `Ошибка получения прямой ссылки на файл: ${error.message}`
      );
    }
    throw error;
  }
};

/**
 * Загружает файлы в новую папку на Яндекс.Диске и публикует её
 * @param files - один или несколько файлов для загрузки
 * @param folderName - название папки (если не указано, будет сгенерировано автоматически)
 * @returns публичную ссылку на папку
 */
export const uploadFilesAndPublish = async (
  files: File | File[],
  folderName?: string
): Promise<string> => {
  try {
    // Преобразуем один файл в массив
    const fileArray = Array.isArray(files) ? files : [files];

    // Генерируем уникальное имя папки, если оно не указано
    const timestamp = new Date().getTime();
    const targetFolder = folderName || `uploads_${timestamp}`;
    const folderPath = `/${targetFolder}`;

    // Создаем папку
    await createFolder(folderPath);

    // Загружаем все файлы параллельно
    const uploadPromises = fileArray.map(async (file) => {
      const filePath = `${folderPath}/${file.name}`;
      const uploadUrl = await getUploadUrl(filePath);
      await uploadFile(uploadUrl, file);
    });

    await Promise.all(uploadPromises);

    // Публикуем папку
    await publishFolder(folderPath);

    // Получаем и возвращаем публичную ссылку
    return await getPublicUrl(folderPath);
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(
        `Ошибка загрузки файлов и публикации папки: ${error.message}`
      );
    }
    throw error;
  }
};

/**
 * Удаляет файл или папку на Яндекс.Диске
 * @param path - путь к удаляемому ресурсу
 * @param permanently - признак безвозвратного удаления (true - удалить без помещения в корзину)
 */
export const deleteFile = async (
  path: string,
  permanently: boolean = false
): Promise<void> => {
  try {
    await axios.delete(YANDEX_DISK_API, {
      headers: {
        Authorization: `OAuth ${token}`,
      },
      params: {
        path,
        permanently,
      },
    });
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new Error(`Ошибка удаления ресурса: ${error.message}`);
    }
    throw error;
  }
};
</file>

<file path="README.md">
# Price List Manager: Project Overview

This project is a web application for managing product lists, including names, prices, images, and other details. It's built with React, shadcn/ui, TypeScript, Supabase, AG Grid, and Yandex.Disk, showcasing a modern web development stack.

**Key Features:**

- **CRUD Operations:** Create, Read, Update, and Delete products.
- **Inline Adding:** Add new products directly in the table without modal forms.
- **Authentication:** Secure user login via Supabase Auth.
- **Real-time Updates:** Changes are reflected instantly across clients.
- **Interactive Data Grid:** Powerful table view with inline editing, sorting, and filtering (AG Grid).
- **Image Management:** Upload and store product images using Yandex.Disk.
- **Drag-and-Drop Image Upload:** User-friendly image uploading.
- **Modal Forms:** Improved user experience for creating products.
- **Protected Routes:** Access control for authenticated users.
- **Efficient State Management:** Uses React Query for data fetching and caching.
- **UI Feedback:** Loading states, notifications (react-hot-toast).
- **Component-Based Architecture:** Reusable React components.
- **TypeScript:** Enhanced code quality with strong typing
- **Tailwind CSS:** Rapid and consistent UI styling

**Technologies:**

- **Frontend:** React, TypeScript, AG Grid, React Router, React Query, Tailwind CSS, Lucide React, react-hot-toast, Vite.
- **Backend:** Supabase (PostgreSQL database, authentication, real-time).
- **Cloud Storage:** Yandex.Disk

**Project Structure (Simplified):**

- `components/`: React components (e.g., `ProductList`, `CreateProductModal`, `LoginPage`).
- `hooks/`: Custom React hooks (e.g., `useProducts` for data management).
- `lib/`: Utilities (e.g., Supabase client, authentication logic).
- `services/`: API interactions (e.g., Supabase database, Yandex.Disk).
- `types/`: TypeScript type definitions.
- `supabase/migrations`: Database schema

This project demonstrates a practical example of building a data-driven web application with a focus on user experience, real-time capabilities, and secure data management. It serves as a solid foundation for learning and building similar applications.
</file>

<file path="src/components/UploadTest.tsx">
import { useState, useCallback } from "react";
import {
  uploadFilesAndPublish,
  getPublicFolderContents,
  getDirectFileUrl,
  deleteFile,
  getUploadUrl,
  uploadFile,
} from "../services/yandexDisk";
import { Input } from "@/components/ui/Input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface FolderItem {
  name: string;
  preview?: string;
  public_url?: string;
  type: "dir" | "file";
  directUrl?: string;
}

const UploadTest = () => {
  const [isDragging, setIsDragging] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [publicUrl, setPublicUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [folderContents, setFolderContents] = useState<FolderItem[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isLoadingContents, setIsLoadingContents] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [folderPath, setFolderPath] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isDeletingAll, setIsDeletingAll] = useState(false);
  const [folderName, setFolderName] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string | null>(null);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    setError(null);

    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;

    // Проверяем, что все файлы - изображения
    const isAllImages = files.every((file) => file.type.startsWith("image/"));
    if (!isAllImages) {
      setError("Пожалуйста, загружайте только изображения");
      return;
    }

    setIsLoading(true);
    try {
      // Создаем имя папки на основе времени
      const timestamp = new Date().getTime();
      const targetFolder = `uploads_${timestamp}`;
      const path = `/${targetFolder}`;

      const url = await uploadFilesAndPublish(files, targetFolder);
      setPublicUrl(url);
      setFolderPath(path);
      setFolderName(targetFolder);
      await fetchFolderContents(url);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Произошла ошибка при загрузке"
      );
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleFileSelect = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      if (files.length === 0) return;

      setError(null);
      setIsLoading(true);
      try {
        // Создаем имя папки на основе времени
        const timestamp = new Date().getTime();
        const targetFolder = `uploads_${timestamp}`;
        const path = `/${targetFolder}`;

        const url = await uploadFilesAndPublish(files, targetFolder);
        setPublicUrl(url);
        setFolderPath(path);
        setFolderName(targetFolder);
        await fetchFolderContents(url);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Произошла ошибка при загрузке"
        );
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const handleUploadMore = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      if (files.length === 0 || !folderName || !publicUrl) return;

      // Проверяем, что все файлы - изображения
      const isAllImages = files.every((file) => file.type.startsWith("image/"));
      if (!isAllImages) {
        setError("Пожалуйста, загружайте только изображения");
        return;
      }

      setIsUploading(true);
      setError(null);
      setSuccessMessage(null);

      try {
        // Загружаем файлы по одному в существующую папку
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          setUploadProgress(
            `Загрузка ${i + 1} из ${files.length}: ${file.name}`
          );

          const filePath = `/${folderName}/${file.name}`;
          const uploadUrl = await getUploadUrl(filePath);
          await uploadFile(uploadUrl, file);
        }

        setUploadProgress(null);
        setSuccessMessage(
          `Дополнительные файлы успешно загружены (${files.length} шт.)`
        );

        // Обновляем содержимое папки
        await fetchFolderContents(publicUrl);

        // Скрываем сообщение через 3 секунды
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        setError(
          err instanceof Error
            ? `Ошибка загрузки дополнительных файлов: ${err.message}`
            : "Произошла ошибка при загрузке дополнительных файлов"
        );
        setUploadProgress(null);
      } finally {
        setIsUploading(false);
        // Очищаем input
        if (e.target) {
          e.target.value = "";
        }
      }
    },
    [folderName, publicUrl]
  );

  const fetchFolderContents = async (url: string) => {
    setIsLoadingContents(true);
    try {
      const items = await getPublicFolderContents(url);
      // Фильтруем только файлы изображений
      const imageItems = items
        .filter(
          (item) =>
            item.type === "file" &&
            item.name.match(/\.(jpeg|jpg|png|gif|webp)$/i)
        )
        .map((item) => ({
          name: item.name,
          preview: item.preview,
          public_url: item.public_url,
          type: item.type,
        }));

      setFolderContents(imageItems);
    } catch (err) {
      setError(
        err instanceof Error
          ? `Ошибка получения содержимого папки: ${err.message}`
          : "Произошла ошибка при получении содержимого папки"
      );
    } finally {
      setIsLoadingContents(false);
    }
  };

  const handleImageClick = async (item: FolderItem) => {
    console.log({ item });
    if (!publicUrl) {
      setError("Ссылка на публичную папку недоступна");
      return;
    }

    try {
      const directUrl = await getDirectFileUrl(publicUrl, item.name);

      // Обновляем элемент в массиве folderContents с directUrl
      setFolderContents((prev) =>
        prev.map((i) => (i.name === item.name ? { ...i, directUrl } : i))
      );

      setSelectedImage(directUrl);
      setIsDialogOpen(true);
    } catch (err) {
      setError(
        err instanceof Error
          ? `Ошибка получения прямой ссылки: ${err.message}`
          : "Произошла ошибка при получении прямой ссылки на изображение"
      );
    }
  };

  const handleViewImages = useCallback(() => {
    if (publicUrl) {
      fetchFolderContents(publicUrl);
    }
  }, [publicUrl]);

  const handleDeleteImage = async (item: FolderItem, e: React.MouseEvent) => {
    e.stopPropagation(); // Предотвращаем открытие изображения при клике на кнопку удаления

    if (!folderPath) {
      setError("Путь к папке не найден");
      return;
    }

    // Формируем полный путь к файлу
    const filePath = `${folderPath}/${item.name}`;

    if (window.confirm(`Вы действительно хотите удалить файл ${item.name}?`)) {
      setIsDeleting(item.name);
      setError(null);
      setSuccessMessage(null);

      try {
        await deleteFile(filePath);
        // Обновляем список файлов, удаляя удаленный файл
        setFolderContents((prev) => prev.filter((i) => i.name !== item.name));
        setSuccessMessage(`Файл ${item.name} успешно удален`);

        // Скрываем сообщение через 3 секунды
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        setError(
          err instanceof Error
            ? `Ошибка удаления файла: ${err.message}`
            : "Произошла ошибка при удалении файла"
        );
      } finally {
        setIsDeleting(null);
      }
    }
  };

  const handleDeleteAllImages = async () => {
    if (!folderPath || folderContents.length === 0) {
      return;
    }

    if (
      window.confirm(
        `Вы действительно хотите удалить все ${folderContents.length} файлов?`
      )
    ) {
      setIsDeletingAll(true);
      setError(null);
      setSuccessMessage(null);

      try {
        // Удаляем файлы последовательно
        for (const item of folderContents) {
          const filePath = `${folderPath}/${item.name}`;
          await deleteFile(filePath);
        }

        // Очищаем список файлов
        setFolderContents([]);
        setSuccessMessage(`Все файлы успешно удалены`);

        // Скрываем сообщение через 3 секунды
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        setError(
          err instanceof Error
            ? `Ошибка удаления файлов: ${err.message}`
            : "Произошла ошибка при удалении файлов"
        );
      } finally {
        setIsDeletingAll(false);
      }
    }
  };

  return (
    <div style={{ padding: "20px" }}>
      <h1>Тест загрузки файлов</h1>
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        style={{
          border: `2px dashed ${isDragging ? "#2196f3" : "#ccc"}`,
          borderRadius: "4px",
          padding: "20px",
          textAlign: "center",
          backgroundColor: isDragging ? "rgba(33, 150, 243, 0.1)" : "#fafafa",
          cursor: "pointer",
          transition: "all 0.3s ease",
          marginBottom: "20px",
        }}
      >
        <Input
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          style={{ display: "none" }}
          id="fileInput"
        />
        <label htmlFor="fileInput" style={{ cursor: "pointer" }}>
          {isLoading
            ? "Загрузка..."
            : "Перетащите изображения сюда или кликните для выбора"}
        </label>
      </div>

      {error && (
        <div style={{ color: "red", marginBottom: "10px" }}>{error}</div>
      )}

      {successMessage && (
        <div style={{ color: "green", marginBottom: "10px" }}>
          {successMessage}
        </div>
      )}

      {uploadProgress && (
        <div style={{ color: "blue", marginBottom: "10px" }}>
          {uploadProgress}
        </div>
      )}

      {publicUrl && (
        <div
          style={{
            padding: "10px",
            backgroundColor: "#e3f2fd",
            borderRadius: "4px",
            wordBreak: "break-all",
            marginBottom: "20px",
          }}
        >
          <p>Ссылка на загруженные файлы:</p>
          <a href={publicUrl} target="_blank" rel="noopener noreferrer">
            {publicUrl}
          </a>

          <div style={{ display: "flex", gap: "10px", marginTop: "10px" }}>
            <button
              onClick={handleViewImages}
              style={{
                padding: "8px 16px",
                backgroundColor: "#2196f3",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
              }}
            >
              {isLoadingContents
                ? "Загрузка..."
                : "Просмотреть загруженные изображения"}
            </button>

            <div style={{ position: "relative" }}>
              <Input
                type="file"
                multiple
                accept="image/*"
                onChange={handleUploadMore}
                style={{ display: "none" }}
                id="uploadMoreInput"
                disabled={isUploading}
              />
              <label
                htmlFor="uploadMoreInput"
                style={{
                  display: "inline-block",
                  padding: "8px 16px",
                  backgroundColor: "#4caf50",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: isUploading ? "not-allowed" : "pointer",
                }}
              >
                {isUploading ? "Загрузка..." : "Догрузить еще фото"}
              </label>
            </div>
          </div>
        </div>
      )}

      {folderContents.length > 0 && (
        <div>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "10px",
            }}
          >
            <h2>Загруженные изображения:</h2>
            <button
              onClick={handleDeleteAllImages}
              disabled={isDeletingAll}
              style={{
                padding: "8px 16px",
                backgroundColor: "#f44336",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: isDeletingAll ? "not-allowed" : "pointer",
              }}
            >
              {isDeletingAll ? "Удаление..." : "Удалить все файлы"}
            </button>
          </div>
          <div style={{ display: "flex", flexWrap: "wrap", gap: "10px" }}>
            {folderContents.map((item) => (
              <div
                key={item.name}
                onClick={() => handleImageClick(item)}
                style={{
                  width: "150px",
                  height: "150px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  overflow: "hidden",
                  cursor: "pointer",
                  position: "relative",
                }}
              >
                <div
                  style={{
                    width: "100%",
                    height: "120px",
                    backgroundImage: item.preview
                      ? `url(${item.preview})`
                      : "none",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundColor: "#f0f0f0",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {!item.preview && (
                    <span style={{ color: "#666", fontSize: "12px" }}>
                      Превью недоступно
                    </span>
                  )}
                </div>
                <div
                  style={{
                    padding: "5px",
                    fontSize: "12px",
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                  }}
                >
                  {item.name}
                </div>
                <button
                  onClick={(e) => handleDeleteImage(item, e)}
                  disabled={isDeleting === item.name}
                  style={{
                    position: "absolute",
                    top: "5px",
                    right: "5px",
                    background: "rgba(255, 0, 0, 0.7)",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    width: "24px",
                    height: "24px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    cursor: "pointer",
                    fontSize: "14px",
                  }}
                >
                  {isDeleting === item.name ? "..." : "✕"}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[80vw] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Просмотр изображения</DialogTitle>
          </DialogHeader>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              overflow: "auto",
              maxHeight: "70vh",
              margin: "20px 0",
            }}
          >
            {selectedImage && (
              <img
                src={selectedImage}
                alt="Просмотр изображения"
                style={{
                  maxWidth: "100%",
                  maxHeight: "70vh",
                  objectFit: "contain",
                }}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UploadTest;
</file>

<file path="src/types/supabase.ts">
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      products: {
        Row: {
          id: string;
          name: string;
          weight: number;
          price: number;
          validity_period: number;
          is_hidden: boolean;
          custom_fields: Json;
          created_at: string;
          updated_at: string;
          group_id: string | null;
          order_in_group: number;
        };
        Insert: {
          id?: string;
          name: string;
          weight: number;
          price: number;
          validity_period: number;
          is_hidden?: boolean;
          custom_fields?: Json;
          created_at?: string;
          updated_at?: string;
          group_id?: string | null;
          order_in_group: number;
        };
        Update: {
          id?: string;
          name?: string;
          weight?: number;
          price?: number;
          validity_period?: number;
          is_hidden?: boolean;
          custom_fields?: Json;
          created_at?: string;
          updated_at?: string;
          group_id?: string | null;
          order_in_group?: number;
        };
      };
      product_groups: {
        Row: {
          id: string;
          name: string;
          order_index: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          order_index: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          order_index?: number;
          created_at?: string;
        };
      };
      product_images: {
        Row: {
          id: string;
          product_id: string;
          yandex_disk_path: string;
          public_url: string;
          sort_order: number;
          is_main: boolean;
          created_at: string;
        };
        Insert: {
          id?: string;
          product_id: string;
          yandex_disk_path: string;
          public_url: string;
          sort_order: number;
          is_main?: boolean;
          created_at?: string;
        };
        Update: {
          id?: string;
          product_id?: string;
          yandex_disk_path?: string;
          public_url?: string;
          sort_order?: number;
          is_main?: boolean;
          created_at?: string;
        };
      };
      column_settings: {
        Row: {
          id: string;
          name: string;
          display_name: string;
          order_index: number;
          is_visible: boolean;
          data_type: string;
          prefix: string | null;
          postfix: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          display_name: string;
          order_index: number;
          is_visible?: boolean;
          data_type: string;
          prefix?: string | null;
          postfix?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          display_name?: string;
          order_index?: number;
          is_visible?: boolean;
          data_type?: string;
          prefix?: string | null;
          postfix?: string | null;
          created_at?: string;
        };
      };
    };
  };
}
</file>

<file path="src/components/products/export/useProductExport.ts">
import { useCallback, useState } from "react";
import { toast } from "@/hooks/use-toast";
import { exportToPDFMake } from "../../../services/pdfmakeExport";
import type { Database } from "../../../types/supabase";
import { loadPDFSettings, getColumnSettingsFromDefs, PDFExportSettings } from "./pdfExportUtils";
import { GroupHeaderRow } from "../grid/GroupHeaderRenderer";
import { ColDef } from "ag-grid-community";

type Product = Database["public"]["Tables"]["products"]["Row"] & {
  product_images?: Array<Database["public"]["Tables"]["product_images"]["Row"]>;
};
type RowData = Product | GroupHeaderRow;
type DbColumnSetting = Database["public"]["Tables"]["column_settings"]["Row"];

export function useProductExport() {
  const [isExporting, setIsExporting] = useState(false);
  const [isPreviewingPDF, setIsPreviewingPDF] = useState(false);

  const handleExportToPDF = useCallback(
    async (
      data: RowData[] | undefined,
      columnDefs: ColDef[],
      allDbColumnSettings: DbColumnSetting[] | undefined,
      preview = false
    ) => {
      if (!data || data.length === 0) {
        toast({
          title: "Ошибка",
          description: "Нет данных для экспорта",
          variant: "destructive",
        });
        return;
      }

      try {
        if (preview) {
          setIsPreviewingPDF(true);
        } else {
          setIsExporting(true);
        }

        const exportData = data;

        const loadedSettings = loadPDFSettings();

        const actualExportColumns = getColumnSettingsFromDefs(
          columnDefs,
          allDbColumnSettings || []
        );

        const finalColumns = actualExportColumns.map(colDef => {
          const savedColumnSetting = loadedSettings.columns.find(
            savedCol => savedCol.field === colDef.field
          );
          return {
            ...colDef,
            selected: savedColumnSetting ? savedColumnSetting.selected : true,
          };
        });
        
        const finalExportSettings: PDFExportSettings = {
          ...loadedSettings,
          columns: finalColumns,
        };

        await exportToPDFMake(exportData, finalExportSettings, preview);

        if (!preview) {
          toast({ title: "Успех", description: "PDF успешно создан" });
        }
      } catch (error) {
        console.error("Ошибка при создании PDF:", error);
        toast({
          title: "Ошибка",
          description: "Ошибка при создании PDF",
          variant: "destructive",
        });
      } finally {
        if (preview) {
          setIsPreviewingPDF(false);
        } else {
          setIsExporting(false);
        }
      }
    },
    []
  );

  return {
    isExporting,
    isPreviewingPDF,
    handleExportToPDF,
  };
}
</file>

<file path="src/components/LoginPage.tsx">
import { useState, useEffect } from "react";
import { useAuth } from "../lib/auth";
import { toast } from "@/hooks/use-toast";
import { useNavigate, useLocation } from "react-router-dom";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";

export function LoginPage() {
  const { signIn, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);

  // Если пользователь уже аутентифицирован, перенаправляем на сохраненный URL или на главную
  useEffect(() => {
    if (user) {
      const from = (location.state as { from?: string })?.from || "/";
      navigate(from, { replace: true });
    }
  }, [user, navigate, location]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await signIn(email, password);
      toast({ title: "Успех", description: "Успешный вход в систему" });
      // Перенаправление будет выполнено в useEffect
    } catch (error) {
      toast({
        title: "Ошибка",
        description: error instanceof Error ? error.message : "Ошибка входа",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Вход в систему
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email" className="sr-only">
                Email
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                placeholder="Email"
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Пароль
              </label>
              <Input
                id="password"
                name="password"
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                placeholder="Пароль"
              />
            </div>
          </div>

          <div>
            <Button type="submit" disabled={loading} className="w-full">
              {loading ? "Вход..." : "Войти"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
</file>

<file path="src/components/products/export/PDFExportSettingsModal.tsx">
import { useState, useEffect } from "react";
import { toast } from "@/hooks/use-toast";
import { ColDef } from "ag-grid-community";
import { Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/label";
import { Divider } from "@/components/ui/Divider";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { ScrollArea } from "@/components/ui/scroll-area";
import { IconButton } from "@/components/ui/IconButton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { useColumnSettings } from "../../../hooks/useColumnSettings";
import {
  PDFExportSettings,
  PDFHeaderSettings,
  loadPDFSettings,
  savePDFSettings,
  getColumnSettingsFromDefs,
} from "./pdfExportUtils";

interface PDFExportSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  columnDefs: ColDef[];
}

export const PDFExportSettingsModal: React.FC<PDFExportSettingsModalProps> = ({
  isOpen,
  onClose,
  columnDefs,
}) => {
  const { data: columnSettingsData, isLoading: isLoadingColumnSettings } =
    useColumnSettings();

  const [settings, setSettings] = useState<PDFExportSettings>(() => {
    const loadedSettings = loadPDFSettings();
    if (columnSettingsData?.data) {
      const actualColumns = getColumnSettingsFromDefs(
        columnDefs,
        columnSettingsData.data
      );
      const mergedColumns = actualColumns.map((col) => {
        const savedCol = loadedSettings.columns.find(
          (s) => s.field === col.field
        );
        return {
          ...col,
          selected: savedCol ? savedCol.selected : true,
        };
      });
      return {
        ...loadedSettings,
        columns: mergedColumns,
      };
    }
    return loadedSettings;
  });

  useEffect(() => {
    if (isOpen && columnSettingsData?.data && !isLoadingColumnSettings) {
      const actualColumns = getColumnSettingsFromDefs(
        columnDefs,
        columnSettingsData.data
      );
      setSettings((prevSettings) => {
        const mergedColumns = actualColumns.map((col) => {
          const savedCol = prevSettings.columns.find(
            (s) => s.field === col.field
          );
          return {
            ...col,
            selected: savedCol ? savedCol.selected : true,
          };
        });
        return {
          header: prevSettings.header,
          includeGroups: prevSettings.includeGroups,
          columns: mergedColumns,
        };
      });
    }
  }, [columnDefs, isOpen, columnSettingsData?.data, isLoadingColumnSettings]);

  const updateHeaderSetting = (
    key: keyof PDFHeaderSettings,
    value: string | string[]
  ) => {
    setSettings((prev) => ({
      ...prev,
      header: {
        ...prev.header,
        [key]: value,
      },
    }));
  };

  const updateDeliveryInfo = (index: number, value: string) => {
    const newDeliveryInfo = [...settings.header.deliveryInfo];
    newDeliveryInfo[index] = value;
    updateHeaderSetting("deliveryInfo", newDeliveryInfo);
  };

  const addDeliveryInfoLine = () => {
    updateHeaderSetting("deliveryInfo", [...settings.header.deliveryInfo, ""]);
  };

  const removeDeliveryInfoLine = (index: number) => {
    const newDeliveryInfo = [...settings.header.deliveryInfo];
    newDeliveryInfo.splice(index, 1);
    updateHeaderSetting("deliveryInfo", newDeliveryInfo);
  };

  const toggleColumnSelection = (field: string) => {
    setSettings((prev) => ({
      ...prev,
      columns: prev.columns.map((col) =>
        col.field === field ? { ...col, selected: !col.selected } : col
      ),
    }));
  };

  const toggleIncludeGroups = () => {
    setSettings((prev) => ({
      ...prev,
      includeGroups: !prev.includeGroups,
    }));
  };

  const handleSave = () => {
    savePDFSettings(settings);
    toast({ title: "Успех", description: "Настройки сохранены" });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="max-w-3xl w-[95vw]"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>Настройки экспорта PDF</DialogTitle>
        </DialogHeader>
        {isLoadingColumnSettings && !settings.columns.length ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
          </div>
        ) : (
          <ScrollArea className="max-h-[70vh] pr-6 -mr-6">
            <div className="space-y-6 py-4">
              {/* Секция: Заголовок и Контакты */}
              <div>
                <h3 className="text-lg font-semibold mb-3">
                  Заголовок и Контакты
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                  <div className="space-y-3">
                    <Input
                      id="pdf-date"
                      label="Дата"
                      value={settings.header.date}
                      onChange={(e) =>
                        updateHeaderSetting("date", e.target.value)
                      }
                      placeholder="Дата прайс-листа"
                    />
                    <Input
                      id="pdf-companyName"
                      label="Название компании"
                      value={settings.header.companyName}
                      onChange={(e) =>
                        updateHeaderSetting("companyName", e.target.value)
                      }
                      placeholder="Название компании"
                    />
                    <Input
                      id="pdf-office"
                      label="Офис"
                      value={settings.header.office}
                      onChange={(e) =>
                        updateHeaderSetting("office", e.target.value)
                      }
                      placeholder="Адрес офиса"
                    />
                    <Input
                      id="pdf-officePhone"
                      label="Телефон офиса"
                      value={settings.header.officePhone}
                      onChange={(e) =>
                        updateHeaderSetting("officePhone", e.target.value)
                      }
                      placeholder="Телефон офиса"
                    />
                  </div>
                  <div className="space-y-3">
                    <Input
                      id="pdf-warehouse"
                      label="Склад"
                      value={settings.header.warehouse}
                      onChange={(e) =>
                        updateHeaderSetting("warehouse", e.target.value)
                      }
                      placeholder="Адрес склада"
                    />
                    <Input
                      id="pdf-warehousePhone"
                      label="Телефон склада"
                      value={settings.header.warehousePhone}
                      onChange={(e) =>
                        updateHeaderSetting("warehousePhone", e.target.value)
                      }
                      placeholder="Телефон склада"
                    />
                    <Input
                      id="pdf-managerName"
                      label="Менеджер"
                      value={settings.header.managerName}
                      onChange={(e) =>
                        updateHeaderSetting("managerName", e.target.value)
                      }
                      placeholder="Имя менеджера (необязательно)"
                    />
                    <Input
                      id="pdf-managerContact"
                      label="Контакт менеджера"
                      value={settings.header.managerContact}
                      onChange={(e) =>
                        updateHeaderSetting("managerContact", e.target.value)
                      }
                      placeholder="Телефон/email (необязательно)"
                    />
                  </div>
                </div>
              </div>
              <Divider />
              {/* Секция: Информация о доставке */}
              <div>
                <h3 className="text-lg font-semibold mb-3">
                  Информация о доставке
                </h3>
                <TooltipProvider delayDuration={100}>
                  <div className="space-y-2">
                    {settings.header.deliveryInfo.map((info, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div className="flex-grow">
                          <Input
                            id={`delivery-${index}`}
                            value={info}
                            onChange={(e) =>
                              updateDeliveryInfo(index, e.target.value)
                            }
                            placeholder="Строка информации о доставке"
                          />
                        </div>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <IconButton
                              icon={<Trash2 size={16} />}
                              variant="ghost"
                              size="sm"
                              onClick={() => removeDeliveryInfoLine(index)}
                              className="text-red-600 hover:bg-red-100 flex-shrink-0"
                            />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Удалить строку</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    ))}
                  </div>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={addDeliveryInfoLine}
                        variant="outline"
                        size="sm"
                        className="mt-3"
                      >
                        <Plus size={16} className="mr-1" />
                        Добавить строку
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Добавить новую строку информации о доставке</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Divider />
              {/* Секция: Выбор столбцов */}
              <div>
                <h3 className="text-lg font-semibold mb-3">
                  Столбцы для экспорта
                </h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-x-6 gap-y-3">
                  {settings.columns
                    .filter(
                      (col) => col.field && !col.field.startsWith("checkbox")
                    )
                    .map((col) => (
                      <div key={col.field} className="flex items-center">
                        <Checkbox
                          id={`col-${col.field}`}
                          checked={col.selected}
                          onCheckedChange={() =>
                            toggleColumnSelection(col.field)
                          }
                          aria-labelledby={`label-col-${col.field}`}
                        />
                        <Label
                          htmlFor={`col-${col.field}`}
                          id={`label-col-${col.field}`}
                          className="ml-2 text-sm cursor-pointer select-none"
                        >
                          {col.headerName || col.field}
                        </Label>
                      </div>
                    ))}
                </div>
              </div>
              <Divider />
              {/* Секция: Отображение групп */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Группировка</h3>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="includeGroups"
                    checked={settings.includeGroups}
                    onCheckedChange={toggleIncludeGroups}
                    aria-labelledby="label-includeGroups"
                  />
                  <Label
                    htmlFor="includeGroups"
                    id="label-includeGroups"
                    className="text-sm cursor-pointer select-none"
                  >
                    Включать названия групп в PDF
                  </Label>
                </div>
              </div>
            </div>
          </ScrollArea>
        )}
        <DialogFooter className="mt-4">
          <DialogClose asChild>
            <Button variant="outline">Отмена</Button>
          </DialogClose>
          <Button onClick={handleSave}>Сохранить</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
</file>

<file path="src/components/products/modals/ColumnSettingsModal.tsx">
import { useState, useEffect, useMemo } from "react";
import { toast } from "@/hooks/use-toast";
import { Plus, Trash2, ArrowUp, ArrowDown, GripVertical } from "lucide-react";
import {
  useColumnSettings,
  useColumnSettingsMutations,
} from "../../../hooks/useColumnSettings";
import type { Database } from "../../../types/supabase";
import { Button } from "@/components/ui/Button";
import { IconButton } from "@/components/ui/IconButton";
import { Input } from "@/components/ui/Input";
import { Modal } from "@/components/ui/Modal";
import { Divider } from "@/components/ui/Divider";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { isCustomField } from "../columns/productColumnUtils";
import { cn } from "@/lib/utils";

type ColumnSetting = Database["public"]["Tables"]["column_settings"]["Row"];

interface ColumnSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const HIDDEN_IN_SETTINGS_FIELDS = [
  "id",
  "updated_at",
  "order_in_group",
  "custom_fields",
];

function generateUniqueFieldName(): string {
  const uuid = crypto.randomUUID();
  return `field_${uuid.substring(0, 8)}`;
}

export function ColumnSettingsModal({
  isOpen,
  onClose,
}: ColumnSettingsModalProps) {
  const { data: columnSettingsData, isLoading } = useColumnSettings();
  const { create, update, remove, reorder } = useColumnSettingsMutations();
  const [newColumnDisplayName, setNewColumnDisplayName] = useState("");
  const [newColumnType, setNewColumnType] = useState<"text" | "number">("text");
  const [editableDisplayNames, setEditableDisplayNames] = useState<
    Record<string, string>
  >({});
  const [editablePrefixes, setEditablePrefixes] = useState<
    Record<string, string>
  >({});
  const [editablePostfixes, setEditablePostfixes] = useState<
    Record<string, string>
  >({});

  const allColumns = useMemo(
    () => columnSettingsData?.data || [],
    [columnSettingsData?.data]
  );

  const { standardColumns, customColumns } = useMemo(() => {
    const standard: ColumnSetting[] = [];
    const custom: ColumnSetting[] = [];
    allColumns
      .filter((col) => !HIDDEN_IN_SETTINGS_FIELDS.includes(col.name))
      .forEach((col) => {
        if (isCustomField(col.name)) {
          custom.push(col);
        } else {
          standard.push(col);
        }
      });
    // Сортируем оба списка по order_index перед возвратом
    standard.sort((a, b) => a.order_index - b.order_index);
    custom.sort((a, b) => a.order_index - b.order_index);
    return { standardColumns: standard, customColumns: custom };
  }, [allColumns]);

  useEffect(() => {
    if (columnSettingsData?.data) {
      const initialNames: Record<string, string> = {};
      const initialPrefixes: Record<string, string> = {};
      const initialPostfixes: Record<string, string> = {};

      columnSettingsData.data.forEach((col) => {
        initialNames[col.id] = col.display_name;
        initialPrefixes[col.id] = col.prefix || "";
        initialPostfixes[col.id] = col.postfix || "";
      });
      setEditableDisplayNames(initialNames);
      setEditablePrefixes(initialPrefixes);
      setEditablePostfixes(initialPostfixes);
    }
  }, [columnSettingsData?.data]);

  const handleDisplayNameChange = (id: string, value: string) => {
    setEditableDisplayNames((prev) => ({ ...prev, [id]: value }));
  };

  const handleDisplayNameBlur = async (column: ColumnSetting) => {
    const newDisplayName = editableDisplayNames[column.id]?.trim();
    if (newDisplayName && newDisplayName !== column.display_name) {
      try {
        await update.mutateAsync({
          id: column.id,
          updates: { display_name: newDisplayName },
        });
        toast({
          title: "Успех",
          description: `Отображаемое имя для "${column.name}" обновлено`,
        });
      } catch (error) {
        console.error("Ошибка при обновлении имени столбца:", error);
        toast({
          title: "Ошибка",
          description: "Не удалось обновить имя столбца",
          variant: "destructive",
        });
        handleDisplayNameChange(column.id, column.display_name);
      }
    } else {
      handleDisplayNameChange(column.id, column.display_name);
    }
  };

  const handlePrefixChange = (id: string, value: string) => {
    setEditablePrefixes((prev) => ({ ...prev, [id]: value }));
  };

  const handlePostfixChange = (id: string, value: string) => {
    setEditablePostfixes((prev) => ({ ...prev, [id]: value }));
  };

  const handlePrefixBlur = async (column: ColumnSetting) => {
    const newPrefix = editablePrefixes[column.id]?.trim();
    const currentDbPrefix = column.prefix || "";

    if (newPrefix !== undefined && newPrefix !== currentDbPrefix) {
      try {
        await update.mutateAsync({
          id: column.id,
          updates: { prefix: newPrefix === "" ? null : newPrefix },
        });
        toast({
          title: "Успех",
          description: `Префикс для "${column.display_name}" обновлен`,
        });
      } catch (error) {
        console.error("Ошибка при обновлении префикса столбца:", error);
        toast({
          title: "Ошибка",
          description: "Не удалось обновить префикс столбца",
          variant: "destructive",
        });
        handlePrefixChange(column.id, currentDbPrefix);
      }
    } else if (newPrefix === undefined) {
      handlePrefixChange(column.id, currentDbPrefix);
    }
  };

  const handlePostfixBlur = async (column: ColumnSetting) => {
    const newPostfix = editablePostfixes[column.id]?.trim();
    const currentDbPostfix = column.postfix || "";

    if (newPostfix !== undefined && newPostfix !== currentDbPostfix) {
      try {
        await update.mutateAsync({
          id: column.id,
          updates: { postfix: newPostfix === "" ? null : newPostfix },
        });
        toast({
          title: "Успех",
          description: `Постфикс для "${column.display_name}" обновлен`,
        });
      } catch (error) {
        console.error("Ошибка при обновлении постфикса столбца:", error);
        toast({
          title: "Ошибка",
          description: "Не удалось обновить постфикс столбца",
          variant: "destructive",
        });
        handlePostfixChange(column.id, currentDbPostfix);
      }
    } else if (newPostfix === undefined) {
      handlePostfixChange(column.id, currentDbPostfix);
    }
  };

  const handleCreateColumn = async () => {
    if (!newColumnDisplayName.trim()) {
      toast({
        title: "Ошибка",
        description: "Введите отображаемое имя столбца",
        variant: "destructive",
      });
      return;
    }
    const technicalName = generateUniqueFieldName();
    try {
      await create.mutateAsync({
        name: technicalName,
        display_name: newColumnDisplayName.trim(),
        data_type: newColumnType,
        order_index: allColumns.length,
        is_visible: true,
      });
      setNewColumnDisplayName("");
      setNewColumnType("text");
      toast({
        title: "Успех",
        description: "Пользовательский столбец успешно добавлен",
      });
    } catch (error) {
      console.error("Ошибка при создании столбца:", error);
      toast({
        title: "Ошибка",
        description: "Не удалось создать столбец",
        variant: "destructive",
      });
    }
  };

  const handleToggleVisibility = async (
    column: ColumnSetting,
    checked: boolean
  ) => {
    try {
      await update.mutateAsync({
        id: column.id,
        updates: { is_visible: checked },
      });
    } catch (error) {
      console.error("Ошибка при обновлении видимости столбца:", error);
      toast({
        title: "Ошибка",
        description: "Не удалось обновить видимость столбца",
        variant: "destructive",
      });
    }
  };

  const handleDeleteColumn = async (column: ColumnSetting) => {
    if (!isCustomField(column.name)) {
      toast({
        title: "Ошибка",
        description: "Стандартные поля не могут быть удалены.",
        variant: "destructive",
      });
      return;
    }
    if (
      window.confirm(
        `Вы уверены, что хотите удалить столбец "${column.display_name}"? \n\nВНИМАНИЕ: Все данные этого поля будут БЕЗВОЗВРАТНО удалены из всех продуктов!`
      )
    ) {
      try {
        // Вызываем напрямую удаление по ID (так как remove ожидает строку)
        await remove.mutateAsync(column.id);
        toast({
          title: "Успех",
          description: `Столбец "${column.display_name}" удален`,
        });
      } catch (error) {
        console.error("Ошибка при удалении столбца:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Неизвестная ошибка";
        toast({
          title: "Ошибка",
          description: `Не удалось удалить столбец: ${errorMessage}`,
          variant: "destructive",
        });
      }
    }
  };

  const handleMoveColumn = async (
    index: number,
    direction: "up" | "down",
    listType: "standard" | "custom"
  ) => {
    const list = listType === "standard" ? standardColumns : customColumns;
    if (
      (direction === "up" && index === 0) ||
      (direction === "down" && index === list.length - 1)
    ) {
      return;
    }

    const targetIndex = direction === "up" ? index - 1 : index + 1;
    const reorderedList = [...list];
    [reorderedList[index], reorderedList[targetIndex]] = [
      reorderedList[targetIndex],
      reorderedList[index],
    ];

    // Объединяем списки для получения полного порядка
    const combinedList =
      listType === "standard"
        ? [...reorderedList, ...customColumns]
        : [...standardColumns, ...reorderedList];

    // Готовим обновления только для измененных позиций
    const columnsToUpdate = combinedList
      .map((col, idx) => ({
        id: col.id,
        order_index: idx,
        current_order_index:
          allColumns.find((c) => c.id === col.id)?.order_index ?? -1,
      }))
      .filter((col) => col.order_index !== col.current_order_index);

    if (columnsToUpdate.length === 0) return;

    try {
      await reorder.mutateAsync(
        columnsToUpdate.map(({ id, order_index }) => ({ id, order_index }))
      );
      toast({ title: "Успех", description: "Порядок столбцов обновлен" });
    } catch (error) {
      console.error("Ошибка при изменении порядка столбцов:", error);
      toast({
        title: "Ошибка",
        description: "Не удалось изменить порядок столбцов",
        variant: "destructive",
      });
    }
  };

  const renderColumnItem = (
    column: ColumnSetting,
    index: number,
    listType: "standard" | "custom"
  ) => {
    const list = listType === "standard" ? standardColumns : customColumns;

    return (
      // Контейнер строки
      <div
        key={column.id}
        className="flex items-start md:items-center justify-between gap-2 md:gap-4 py-3 px-4 border-b last:border-b-0 hover:bg-gray-50"
      >
        {/* Левая часть: Перетаскивание + Имя + Тип + Префикс/Постфикс */}
        <div className="flex flex-col gap-2 flex-1 min-w-0">
          <div className="flex items-center gap-2 md:gap-3">
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="hidden sm:inline-block cursor-grab text-gray-400 hover:text-gray-600 mt-1 md:mt-0">
                    <GripVertical size={18} />
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Перетащить для изменения порядка</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <div className="flex-1 min-w-0">
              <Input
                value={editableDisplayNames[column.id] || ""}
                onChange={(e) =>
                  handleDisplayNameChange(column.id, e.target.value)
                }
                onBlur={() => handleDisplayNameBlur(column)}
                className="text-sm h-8"
                title="Отображаемое имя столбца"
              />
            </div>
            <span className="hidden xs:inline-block text-xs text-gray-500 uppercase w-12 text-center flex-shrink-0 mt-1 md:mt-0">
              {column.data_type}
            </span>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 pl-0 sm:pl-8 md:pl-11">
            <div>
              <Label
                htmlFor={`prefix-${column.id}`}
                className="text-xs text-gray-500 mb-1 block"
              >
                Префикс
              </Label>
              <Input
                id={`prefix-${column.id}`}
                value={editablePrefixes[column.id] || ""}
                onChange={(e) => handlePrefixChange(column.id, e.target.value)}
                onBlur={() => handlePrefixBlur(column)}
                className="text-sm h-8"
                placeholder=""
                title="Префикс значения (напр. '$', 'ID: ')"
              />
            </div>
            <div>
              <Label
                htmlFor={`postfix-${column.id}`}
                className="text-xs text-gray-500 mb-1 block"
              >
                Постфикс
              </Label>
              <Input
                id={`postfix-${column.id}`}
                value={editablePostfixes[column.id] || ""}
                onChange={(e) => handlePostfixChange(column.id, e.target.value)}
                onBlur={() => handlePostfixBlur(column)}
                className="text-sm h-8"
                placeholder=""
                title="Постфикс значения (напр. 'кг', 'шт.', '%')"
              />
            </div>
          </div>
        </div>

        {/* Правая часть: Управление (кнопки) */}
        <div className="flex items-center gap-1 md:gap-2 flex-shrink-0 mt-1 md:mt-0">
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  {" "}
                  {/* Обертка для Tooltip + Switch */}
                  <Switch
                    id={`visible-${column.id}`}
                    checked={column.is_visible}
                    onCheckedChange={(checked) =>
                      handleToggleVisibility(column, checked)
                    }
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {column.is_visible ? "Скрыть столбец" : "Показать столбец"}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Кнопки перемещения */}
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  icon={<ArrowUp size={16} />}
                  variant="ghost"
                  size="sm"
                  onClick={() => handleMoveColumn(index, "up", listType)}
                  disabled={index === 0}
                  className={cn(
                    "p-1",
                    index === 0 && "opacity-30 cursor-not-allowed"
                  )}
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>Переместить вверх</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  icon={<ArrowDown size={16} />}
                  variant="ghost"
                  size="sm"
                  onClick={() => handleMoveColumn(index, "down", listType)}
                  disabled={index === list.length - 1}
                  className={cn(
                    "p-1",
                    index === list.length - 1 && "opacity-30 cursor-not-allowed"
                  )}
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>Переместить вниз</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Кнопка удаления (только для пользовательских) */}
          {isCustomField(column.name) && (
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <IconButton
                    icon={
                      remove.isPending && remove.variables === column.id ? (
                        <div className="animate-spin h-4 w-4 border-2 border-red-600 rounded-full border-t-transparent" />
                      ) : (
                        <Trash2 size={16} />
                      )
                    }
                    variant="ghost"
                    size="sm"
                    className="p-1 text-red-600 hover:bg-red-100"
                    onClick={() => handleDeleteColumn(column)}
                    disabled={remove.isPending}
                  />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Удалить столбец</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>
    );
  };

  // Рендер основного компонента
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Настройки столбцов"
      size="xl"
      // Указываем bodyClassName для добавления паддингов к прокручиваемой области
      bodyClassName="p-6"
      // Передаем кнопку "Закрыть" в футер
      footer={
        <div className="flex justify-end">
          <Button variant="secondary" onClick={onClose}>
            Закрыть
          </Button>
        </div>
      }
    >
      {/* Контент теперь напрямую внутри Modal (в children), Modal сам добавит скролл */}

      {/* Форма добавления нового столбца */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Добавить новый столбец</h3>
        {/* Адаптивный контейнер для формы */}
        <div className="flex flex-col md:flex-row md:items-end gap-3 md:gap-4">
          <div className="flex-grow">
            <Label
              htmlFor="newColumnDisplayName"
              className="mb-1 block text-sm font-medium"
            >
              Отображаемое имя
            </Label>
            <Input
              id="newColumnDisplayName"
              value={newColumnDisplayName}
              onChange={(e) => setNewColumnDisplayName(e.target.value)}
              placeholder="Например, Артикул"
              className="h-9"
            />
          </div>
          <div className="w-full md:w-40 flex-shrink-0">
            <Label
              htmlFor="newColumnType"
              className="mb-1 block text-sm font-medium"
            >
              Тип поля
            </Label>
            <Select
              value={newColumnType}
              onValueChange={(value: "text" | "number") =>
                setNewColumnType(value)
              }
            >
              <SelectTrigger id="newColumnType" className="h-9">
                <SelectValue placeholder="Выберите тип" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">Текст</SelectItem>
                <SelectItem value="number">Число</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button
            onClick={handleCreateColumn}
            disabled={create.isPending || !newColumnDisplayName.trim()}
            className="h-9 w-full md:w-auto flex-shrink-0" // Адаптивная ширина кнопки
          >
            <Plus className="w-4 h-4 mr-2" />
            {create.isPending ? "Добавление..." : "Добавить"}
          </Button>
        </div>
      </div>

      <Divider label="Настройка отображения" className="my-6" />

      {/* Списки столбцов */}
      {isLoading ? (
        <div className="flex items-center justify-center p-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
        </div>
      ) : (
        <div className="space-y-6">
          {/* Стандартные поля */}
          <div>
            <div className="mb-2">
              <h4 className="text-base font-medium text-gray-800">
                Стандартные поля
              </h4>
              <p className="text-xs text-gray-500">
                Можно изменять видимость и порядок.
              </p>
            </div>
            <div className="border rounded-md">
              {standardColumns.length > 0 ? (
                standardColumns.map((col, index) =>
                  renderColumnItem(col, index, "standard")
                )
              ) : (
                <p className="p-4 text-sm text-gray-500">
                  Стандартные поля не найдены.
                </p>
              )}
            </div>
          </div>

          {/* Пользовательские поля */}
          <div>
            <div className="mb-2">
              <h4 className="text-base font-medium text-gray-800">
                Пользовательские поля
              </h4>
              <p className="text-xs text-gray-500">
                Можно изменять имя, видимость, порядок и удалять.
              </p>
            </div>
            <div className="border rounded-md">
              {customColumns.length > 0 ? (
                customColumns.map((col, index) =>
                  renderColumnItem(col, index, "custom")
                )
              ) : (
                <p className="p-4 text-sm text-gray-500">
                  Нет пользовательских столбцов.
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Кнопка закрытия теперь рендерится через пропс `footer` компонента Modal */}
    </Modal>
  );
}
</file>

<file path="src/db/meta/_journal.json">
{
  "version": "7",
  "dialect": "postgresql",
  "entries": [
    {
      "idx": 0,
      "version": "7",
      "when": 1740509657229,
      "tag": "0000_crazy_energizer",
      "breakpoints": true
    },
    {
      "idx": 1,
      "version": "7",
      "when": 1741718478705,
      "tag": "0001_adorable_shriek",
      "breakpoints": true
    },
    {
      "idx": 2,
      "version": "7",
      "when": 1741718950041,
      "tag": "0002_silly_firebird",
      "breakpoints": true
    },
    {
      "idx": 3,
      "version": "7",
      "when": 1744114121484,
      "tag": "0003_tiresome_madame_hydra",
      "breakpoints": true
    },
    {
      "idx": 4,
      "version": "7",
      "when": 1744115402054,
      "tag": "0004_chilly_sister_grimm",
      "breakpoints": true
    },
    {
      "idx": 5,
      "version": "7",
      "when": 1744538559410,
      "tag": "0005_rare_cobalt_man",
      "breakpoints": true
    },
    {
      "idx": 6,
      "version": "7",
      "when": 1746561724916,
      "tag": "0006_last_the_watchers",
      "breakpoints": true
    },
    {
      "idx": 7,
      "version": "7",
      "when": 1747512837932,
      "tag": "0007_ambitious_vampiro",
      "breakpoints": true
    }
  ]
}
</file>

<file path="src/db/schema.ts">
import {
  pgTable,
  foreignKey,
  unique,
  pgPolicy,
  uuid,
  text,
  integer,
  boolean,
  timestamp,
  check,
  numeric,
  jsonb,
  pgEnum,
  index,
  primaryKey,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

export const columnType = pgEnum("column_type", ["text", "number"]);

// --- NEW: Product Groups Table ---
export const productGroups = pgTable(
  "product_groups",
  {
    id: uuid("id").defaultRandom().primaryKey().notNull(),
    name: text("name").notNull(),
    orderIndex: integer("order_index").notNull(),
    isHidden: boolean("is_hidden").default(false).notNull(),
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    })
      .defaultNow()
      .notNull(),
  },
  (table) => ({
    orderIdx: index("idx_product_groups_order_index").on(table.orderIndex), // Index for sorting
    // --- Add Policies ---
    authenticatedManagePolicy: pgPolicy(
      "Authenticated users can manage groups",
      {
        as: "permissive",
        for: "all",
        to: ["authenticated"],
        using: sql`true`,
        withCheck: sql`true`,
      }
    ),
    publicReadPolicy: pgPolicy("Public read access for groups", {
      as: "permissive",
      for: "select",
      to: ["public"],
      using: sql`true`,
    }),
  })
);
// ----------------------------------

export const products = pgTable(
  "products",
  {
    id: uuid("id").defaultRandom().primaryKey().notNull(),
    name: text("name").notNull(),
    weight: numeric("weight").notNull(),
    price: numeric("price").notNull(),
    validityPeriod: integer("validity_period").notNull(),
    isHidden: boolean("is_hidden").default(false),
    customFields: jsonb("custom_fields").default({}),
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    }).defaultNow(),
    updatedAt: timestamp("updated_at", {
      withTimezone: true,
      mode: "string",
    }).defaultNow(),
    // --- NEW: Group ID Column ---
    groupId: uuid("group_id").references(() => productGroups.id, {
      onDelete: "set null", // Important: Set to null when group is deleted
    }),
    // --------------------------
    // --- NEW: Order within Group Column ---
    orderInGroup: integer("order_in_group").notNull().default(0),
    // --------------------------
  },
  (table) => {
    return {
      // --- NEW: Index for sorting within groups ---
      groupOrderIdx: index("idx_products_group_order").on(
        table.groupId,
        table.orderInGroup
      ),
      // ---------------------------------------
      priceCheck: check("products_price_check", sql`price >= (0)::numeric`),
      validityPeriodCheck: check(
        "products_validity_period_check",
        sql`validity_period >= 0`
      ),
      weightCheck: check("products_weight_check", sql`weight >= (0)::numeric`),
      // --- Policies ---
      authenticatedInsertPolicy: pgPolicy(
        "Authenticated users can create products",
        {
          as: "permissive",
          for: "insert",
          to: ["authenticated"],
          withCheck: sql`true`,
        }
      ),
      authenticatedSelectPolicy: pgPolicy(
        "Authenticated users can read products",
        {
          as: "permissive",
          for: "select",
          to: ["authenticated"],
        }
      ),
      authenticatedDeletePolicy: pgPolicy(
        "Authenticated users can delete products",
        {
          as: "permissive",
          for: "delete",
          to: ["public"],
        }
      ),
      authenticatedUpdatePolicy: pgPolicy(
        "Authenticated users can update products",
        {
          as: "permissive",
          for: "update",
          to: ["public"],
        }
      ),
      publicSelectPolicy: pgPolicy(
        "Public read access for non-hidden products",
        {
          as: "permissive",
          for: "select",
          to: ["public"],
        }
      ),
      publicInsertPolicy: pgPolicy("Anyone can create products", {
        as: "permissive",
        for: "insert",
        to: ["public"],
      }),
    };
  }
);

// Таблица для хранения настроек столбцов
export const columnSettings = pgTable(
  "column_settings",
  {
    id: uuid("id").defaultRandom().primaryKey().notNull(),
    name: text("name").notNull(),
    displayName: text("display_name").notNull(),
    orderIndex: integer("order_index").notNull(),
    isVisible: boolean("is_visible").default(true),
    dataType: text("data_type").notNull(),
    prefix: text("prefix"),
    postfix: text("postfix"),
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    }).defaultNow(),
  },
  (table) => [
    unique("column_settings_name_unique").on(table.name),
    pgPolicy("Authenticated users can manage column settings", {
      as: "permissive",
      for: "all",
      to: ["authenticated"],
      using: sql`true`,
      withCheck: sql`true`,
    }),
    pgPolicy("Public read access for column settings", {
      as: "permissive",
      for: "select",
      to: ["public"],
    }),
    pgPolicy("Allow public insertion for initialization", {
      as: "permissive",
      for: "insert",
      to: ["public"],
      withCheck: sql`true`,
    }),
  ]
);

export const productImages = pgTable(
  "product_images",
  {
    id: uuid("id").defaultRandom().primaryKey().notNull(),
    productId: uuid("product_id").notNull(),
    yandexDiskPath: text("yandex_disk_path").notNull(),
    publicUrl: text("public_url").notNull(),
    sortOrder: integer("sort_order").notNull(),
    isMain: boolean("is_main").default(false),
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    }).defaultNow(),
  },
  (table) => [
    foreignKey({
      columns: [table.productId],
      foreignColumns: [products.id],
      name: "product_images_product_id_fkey",
    }).onDelete("cascade"),
    unique("product_images_product_id_sort_order_key").on(
      table.productId,
      table.sortOrder
    ),
    pgPolicy("Authenticated users can manage product images", {
      as: "permissive",
      for: "all",
      to: ["authenticated"],
      using: sql`true`,
      withCheck: sql`true`,
    }),
    pgPolicy("Public read access for product images", {
      as: "permissive",
      for: "select",
      to: ["public"],
    }),
  ]
);

// --- NEW: Session Lock Table ---
export const sessionLock = pgTable(
  "session_lock",
  {
    lockKey: text("lock_key").primaryKey().notNull(), // Ключ блокировки (e.g., "product_list_edit")
    userId: uuid("user_id"), // ID пользователя, держащего блокировку
    sessionId: text("session_id"), // Уникальный ID сессии клиента
    lockedAt: timestamp("locked_at", { withTimezone: true, mode: "string" }), // Время установки/обновления
  },
  (table) => ({
    // --- Policies ---
    // Разрешаем аутентифицированным пользователям читать и писать
    authenticatedAccessPolicy: pgPolicy("Allow authenticated access", {
      as: "permissive",
      for: "all",
      to: ["authenticated"],
      using: sql`true`,
      withCheck: sql`true`,
    }),
    // Опционально: Разрешаем публичное чтение, если нужно (может быть полезно для отладки)
    // publicReadPolicy: pgPolicy("Allow public read access", {
    //   as: "permissive",
    //   for: "select",
    //   to: ["public"],
    //   using: sql`true`,
    // }),
  })
);
// ----------------------------
</file>

<file path="src/App.tsx">
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useEffect } from "react";
import { AuthProvider } from "./lib/auth";
import { LoginPage } from "./components/LoginPage";
import { ProtectedRoute } from "./components/ProtectedRoute";
import UploadTest from "./components/UploadTest";
import { initializeColumnSettings } from "./db/init";
import { ProductList } from "./components/products/ProductList";

const queryClient = new QueryClient();

function AppInitializer() {
  useEffect(() => {
    // Инициализируем базу данных при первой загрузке приложения
    initializeColumnSettings().catch(console.error);
  }, []);

  return null;
}

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <AuthProvider>
          <AppInitializer />
          <Toaster />
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route path="/upload-test" element={<UploadTest />} />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <ProductList />
                </ProtectedRoute>
              }
            />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </AuthProvider>
      </Router>
    </QueryClientProvider>
  );
}
</file>

<file path="src/components/products/ProductToolbar.tsx">
import {
  Eye,
  EyeOff,
  FileText,
  LogOut,
  Plus,
  Settings,
  Trash2,
  Menu,
  Columns,
  Layers,
  Download,
  ChevronDown,
} from "lucide-react";
import { useProductGroups } from "../../hooks/useProductGroups";
import { useState } from "react";
import type { Database } from "../../types/supabase";
import { Button } from "@/components/ui/Button";
import { Divider } from "@/components/ui/Divider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet";
import { ProductGroupsModal } from "./modals/ProductGroupsModal";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ScrollArea } from "@/components/ui/scroll-area";
import { IconButton } from "@/components/ui/IconButton";

type Product = Database["public"]["Tables"]["products"]["Row"];

export interface ProductToolbarProps {
  showHidden: boolean;
  selectedRows: Product[];
  hasProducts: boolean;
  isExporting: boolean;
  isPreviewingPDF: boolean;
  onAddProduct: (groupId: string | null) => void;
  onAddGroupClick?: () => void;
  onToggleVisibility: () => void;
  onDelete: () => void;
  onExportToPDF: (preview: boolean) => void;
  onExportSettingsClick: () => void;
  onColumnSettingsClick: () => void;
  onSignOut: () => void;
  onToggleShowHidden: () => void;
}

export function ProductToolbar({
  showHidden,
  selectedRows,
  hasProducts,
  isExporting,
  isPreviewingPDF,
  onAddProduct,
  onAddGroupClick,
  onToggleVisibility,
  onDelete,
  onExportToPDF,
  onExportSettingsClick,
  onColumnSettingsClick,
  onSignOut,
  onToggleShowHidden,
}: ProductToolbarProps) {
  const { data: groupsData, isLoading: isLoadingGroups } = useProductGroups();
  const [isGroupsModalOpen, setIsGroupsModalOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const hasSelection = selectedRows.length > 0;
  const currentViewTitle = showHidden
    ? "Скрытые продукты"
    : "Активные продукты";

  const handleOpenGroupsModal = () => {
    setIsGroupsModalOpen(true);
    setIsMobileMenuOpen(false);
  };

  const createDropdownMenuItem = (
    label: string,
    icon: React.ReactNode,
    action: () => void,
    disabled: boolean = false,
    isDestructive: boolean = false,
    closeSheetOnClick: boolean = false
  ) => {
    const itemContent = (
      <div className="flex items-center gap-2">
        {icon}
        <span>{label}</span>
      </div>
    );

    const handleClick = () => {
      action();
      if (closeSheetOnClick) {
        setIsMobileMenuOpen(false);
      }
    };

    return (
      <DropdownMenuItem
        onClick={handleClick}
        disabled={disabled}
        className={`${isDestructive ? "text-red-600 focus:text-red-700" : ""}`}
      >
        {itemContent}
      </DropdownMenuItem>
    );
  };

  const SheetActionItem: React.FC<{
    label: string;
    icon: React.ReactNode;
    action: () => void;
    disabled?: boolean;
    variant?: "default" | "secondary" | "destructive" | "outline";
    isToggle?: boolean;
    isActive?: boolean;
  }> = ({
    label,
    icon,
    action,
    disabled = false,
    variant = "secondary",
    isToggle = false,
    isActive = false,
  }) => (
    <SheetClose asChild>
      <Button
        variant={isToggle ? (isActive ? "default" : "outline") : variant}
        onClick={action}
        disabled={disabled}
        className={`w-full justify-start gap-2 px-3 py-2 text-sm ${
          isActive && isToggle ? "font-semibold" : ""
        }`}
      >
        {icon}
        {label}
      </Button>
    </SheetClose>
  );

  const DesktopToolbar = () => (
    <div className="flex items-center justify-between gap-4 w-full">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="text-xl font-bold text-gray-900 hover:bg-gray-100 px-3 py-2"
            aria-label="Переключить вид продуктов"
          >
            {currentViewTitle}
            <ChevronDown className="ml-2 h-5 w-5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuRadioGroup
            value={showHidden ? "hidden" : "active"}
            onValueChange={(value) => {
              if (
                (value === "hidden" && !showHidden) ||
                (value === "active" && showHidden)
              ) {
                onToggleShowHidden();
              }
            }}
          >
            <DropdownMenuRadioItem value="active">
              Активные продукты
            </DropdownMenuRadioItem>
            <DropdownMenuRadioItem value="hidden">
              Скрытые продукты
            </DropdownMenuRadioItem>
          </DropdownMenuRadioGroup>
        </DropdownMenuContent>
      </DropdownMenu>

      <div className="flex items-center gap-2 flex-grow">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="default" size="sm">
              <Plus className="h-4 w-4 mr-1" />
              Добавить
              <ChevronDown className="h-4 w-4 ml-1 opacity-70" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuItem onClick={() => onAddProduct(null)}>
              <Plus className="h-4 w-4 mr-2" />
              Продукт (без группы)
            </DropdownMenuItem>
            {!isLoadingGroups && groupsData?.data && groupsData.data.length > 0 && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>Добавить в группу</DropdownMenuLabel>
                {groupsData.data.map((group) => (
                  <DropdownMenuItem
                    key={group.id}
                    onClick={() => onAddProduct(group.id)}
                  >
                    <Plus className="h-4 w-4 mr-2 opacity-50" />
                    {group.name}
                  </DropdownMenuItem>
                ))}
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
        {onAddGroupClick && (
          <Button onClick={onAddGroupClick} variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-1" />
            Группу
          </Button>
        )}

        {hasSelection && (
          <Divider orientation="vertical" className="h-6 mx-1" />
        )}

        <TooltipProvider delayDuration={100}>
          {hasSelection && (
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  icon={
                    selectedRows[0]?.is_hidden ? (
                      <Eye className="h-4 w-4" />
                    ) : (
                      <EyeOff className="h-4 w-4" />
                    )
                  }
                  onClick={onToggleVisibility}
                  variant="ghost"
                  size="sm"
                  tooltip={
                    selectedRows[0]?.is_hidden
                      ? "Показать выбранные"
                      : "Скрыть выбранные"
                  }
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {selectedRows[0]?.is_hidden
                    ? "Показать выбранные"
                    : "Скрыть выбранные"}
                </p>
              </TooltipContent>
            </Tooltip>
          )}
          {hasSelection && (
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  icon={<Trash2 className="h-4 w-4" />}
                  onClick={onDelete}
                  variant="ghost"
                  size="sm"
                  className="text-red-600 hover:bg-red-100 hover:text-red-700"
                  tooltip="Удалить выбранные"
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>Удалить выбранные</p>
              </TooltipContent>
            </Tooltip>
          )}
        </TooltipProvider>
      </div>

      <div className="flex items-center gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-1" />
              Настройки
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            {createDropdownMenuItem(
              "Настройка столбцов",
              <Columns className="h-4 w-4" />,
              onColumnSettingsClick
            )}
            {createDropdownMenuItem(
              "Управление группами",
              <Layers className="h-4 w-4" />,
              handleOpenGroupsModal
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={!hasProducts && !(isExporting || isPreviewingPDF)}
            >
              <Download className="h-4 w-4 mr-1" />
              Экспорт
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-60">
            {createDropdownMenuItem(
              isPreviewingPDF ? "Открытие..." : "Предпросмотр PDF",
              <FileText className="h-4 w-4" />,
              () => onExportToPDF(true),
              isPreviewingPDF || isExporting || !hasProducts
            )}
            {createDropdownMenuItem(
              isExporting ? "Создание..." : "Экспорт в PDF",
              <Download className="h-4 w-4" />,
              () => onExportToPDF(false),
              isExporting || isPreviewingPDF || !hasProducts
            )}
            {createDropdownMenuItem(
              "Настройки экспорта PDF",
              <Settings className="h-4 w-4" />,
              onExportSettingsClick
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <TooltipProvider delayDuration={100}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={onSignOut}
                variant="ghost"
                size="icon"
                className="ml-1 w-9 h-9"
              >
                <LogOut className="h-4 w-4" />
                <span className="sr-only">Выйти</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Выйти</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );

  const MobileToolbar = () => (
    <div className="flex items-center justify-between w-full">
      <h1 className="text-xl font-bold text-gray-900">{currentViewTitle}</h1>
      <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon">
            <Menu />
            <span className="sr-only">Открыть меню</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="right" className="w-[300px] sm:w-[340px] p-0">
          <SheetHeader className="p-4 border-b">
            <SheetTitle>Меню</SheetTitle>
          </SheetHeader>
          <ScrollArea className="h-[calc(100%-57px)]">
            <div className="p-4 space-y-2">
              <div>
                <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                  Вид
                </h3>
                <SheetActionItem
                  label="Активные продукты"
                  icon={<Eye className="h-4 w-4" />}
                  action={() => {
                    if (showHidden) onToggleShowHidden();
                    setIsMobileMenuOpen(false);
                  }}
                  isToggle={true}
                  isActive={!showHidden}
                />
                <SheetActionItem
                  label="Скрытые продукты"
                  icon={<EyeOff className="h-4 w-4" />}
                  action={() => {
                    if (!showHidden) onToggleShowHidden();
                    setIsMobileMenuOpen(false);
                  }}
                  isToggle={true}
                  isActive={showHidden}
                />
              </div>
              <Divider className="my-3" />

              <div>
                <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                  Создать
                </h3>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="secondary"
                      className="w-full justify-start gap-2 px-3 py-2 text-sm"
                    >
                      <Plus className="h-4 w-4" />
                      Добавить продукт
                      <ChevronDown className="h-4 w-4 ml-auto opacity-70" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" side="right" className="w-[calc(100vw-2rem-32px-16px)] min-w-[250px]">
                    <DropdownMenuItem onClick={() => { onAddProduct(null); setIsMobileMenuOpen(false); }}>
                      <Plus className="h-4 w-4 mr-2" />
                      Продукт (без группы)
                    </DropdownMenuItem>
                    {!isLoadingGroups && groupsData?.data && groupsData.data.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel>Добавить в группу</DropdownMenuLabel>
                        {groupsData.data.map((group) => (
                          <DropdownMenuItem
                            key={group.id}
                            onClick={() => { onAddProduct(group.id); setIsMobileMenuOpen(false); }}
                          >
                            <Plus className="h-4 w-4 mr-2 opacity-50" />
                            {group.name}
                          </DropdownMenuItem>
                        ))}
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
                {onAddGroupClick && (
                  <SheetActionItem
                    label="Добавить группу"
                    icon={<Plus className="h-4 w-4" />}
                    action={() => {
                      onAddGroupClick();
                      setIsMobileMenuOpen(false);
                    }}
                  />
                )}
              </div>

              {hasSelection && (
                <>
                  <Divider className="my-3" />
                  <div>
                    <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                      Действия с выбранным
                    </h3>
                    <SheetActionItem
                      label={
                        selectedRows[0]?.is_hidden
                          ? "Показать выбранные"
                          : "Скрыть выбранные"
                      }
                      icon={
                        selectedRows[0]?.is_hidden ? (
                          <Eye className="h-4 w-4" />
                        ) : (
                          <EyeOff className="h-4 w-4" />
                        )
                      }
                      action={() => {
                        onToggleVisibility();
                        setIsMobileMenuOpen(false);
                      }}
                      disabled={!hasSelection}
                    />
                    <SheetActionItem
                      label="Удалить выбранные"
                      icon={<Trash2 className="h-4 w-4" />}
                      action={() => {
                        onDelete();
                        setIsMobileMenuOpen(false);
                      }}
                      disabled={!hasSelection}
                      variant="destructive"
                    />
                  </div>
                </>
              )}
              <Divider className="my-3" />

              <div>
                <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                  Настройки вида
                </h3>
                <SheetActionItem
                  label="Настройка столбцов"
                  icon={<Columns className="h-4 w-4" />}
                  action={() => {
                    onColumnSettingsClick();
                    setIsMobileMenuOpen(false);
                  }}
                />
                <SheetActionItem
                  label="Управление группами"
                  icon={<Layers className="h-4 w-4" />}
                  action={() => {
                    handleOpenGroupsModal();
                  }}
                />
              </div>
              <Divider className="my-3" />

              <div>
                <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                  Экспорт PDF
                </h3>
                <SheetActionItem
                  label={isPreviewingPDF ? "Открытие..." : "Предпросмотр PDF"}
                  icon={<FileText className="h-4 w-4" />}
                  action={() => {
                    onExportToPDF(true);
                    setIsMobileMenuOpen(false);
                  }}
                  disabled={isPreviewingPDF || isExporting || !hasProducts}
                  variant="outline"
                />
                <SheetActionItem
                  label={isExporting ? "Создание..." : "Экспорт в PDF"}
                  icon={<Download className="h-4 w-4" />}
                  action={() => {
                    onExportToPDF(false);
                    setIsMobileMenuOpen(false);
                  }}
                  disabled={isExporting || isPreviewingPDF || !hasProducts}
                  variant="outline"
                />
                <SheetActionItem
                  label="Настройки экспорта PDF"
                  icon={<Settings className="h-4 w-4" />}
                  action={() => {
                    onExportSettingsClick();
                    setIsMobileMenuOpen(false);
                  }}
                />
              </div>
              <Divider className="my-3" />

              <div>
                <h3 className="mb-2 px-1 text-xs font-semibold text-muted-foreground uppercase">
                  Аккаунт
                </h3>
                <SheetActionItem
                  label="Выйти"
                  icon={<LogOut className="h-4 w-4" />}
                  action={() => {
                    onSignOut();
                    setIsMobileMenuOpen(false);
                  }}
                />
              </div>
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
    </div>
  );

  return (
    <>
      <div className="hidden md:flex w-full">
        <DesktopToolbar />
      </div>
      <div className="flex md:hidden w-full">
        <MobileToolbar />
      </div>

      {isGroupsModalOpen && (
        <ProductGroupsModal
          isOpen={isGroupsModalOpen}
          onClose={() => setIsGroupsModalOpen(false)}
        />
      )}
    </>
  );
}
</file>

<file path="src/services/pdfmakeExport.ts">
import pdfMake from "pdfmake/build/pdfmake";
import "pdfmake/build/vfs_fonts";
import type { Database } from "../types/supabase";
import { TDocumentDefinitions, Content } from "pdfmake/interfaces";
import { PDFExportSettings } from "../components/products/export/pdfExportUtils";
import { GroupHeaderRow } from "../components/products/grid/GroupHeaderRenderer";

const VIRTUAL_ORDER_COLUMN_HEADER = "ЗАКАЗ";

// Добавляем поддержку кириллицы
pdfMake.fonts = {
  Roboto: {
    normal: "Roboto-Regular.ttf",
    bold: "Roboto-Medium.ttf",
    italics: "Roboto-Italic.ttf",
    bolditalics: "Roboto-MediumItalic.ttf",
  },
};

// Определяем типы локально или импортируем, если они вынесены
type Product = Database["public"]["Tables"]["products"]["Row"] & {
  product_images?: Array<Database["public"]["Tables"]["product_images"]["Row"]>;
};
type RowData = Product | GroupHeaderRow;

// Вспомогательная функция type guard для GroupHeaderRow
const isGroupHeaderRow = (item: RowData): item is GroupHeaderRow => {
  return "isGroupHeader" in item && item.isGroupHeader === true;
};

// --- ИЗМЕНЕНИЕ: Обновляем formatValue для использования prefix и postfix ---
const formatValue = (
  product: Product,
  field: string,
  prefix?: string | null,
  postfix?: string | null
): string => {
  // --- Проверяем наличие product перед доступом к полям ---
  if (!product) return "";

  let valueToFormat: unknown;
  let isPriceField = false;

  if (field.startsWith("custom_fields.")) {
    const fieldName = field.split(".")[1];
    const customFields = product.custom_fields as Record<string, unknown>;
    valueToFormat = customFields?.[fieldName];
  } else {
    valueToFormat = product[field as keyof Product];
  }

  if (
    field === "price" ||
    (field.startsWith("custom_fields.") && postfix === "₽")
  ) {
    isPriceField = true;
  }

  let formattedValue = "";

  if (valueToFormat === null || valueToFormat === undefined) {
    formattedValue = "";
  } else if (isPriceField) {
    const priceNum = Number(valueToFormat);
    formattedValue = !isNaN(priceNum)
      ? priceNum.toFixed(2)
      : String(valueToFormat);
  } else if (field === "is_hidden") {
    formattedValue = valueToFormat ? "Скрыт" : "Активен";
  } else if (field === "created_at" && typeof valueToFormat === "string") {
    try {
      formattedValue = new Date(valueToFormat).toLocaleDateString("ru-RU");
    } catch {
      formattedValue = String(valueToFormat);
    }
  } else if (field === "product_images") {
    formattedValue =
      product.product_images && product.product_images.length > 0
        ? "Есть"
        : "Нет";
  } else {
    formattedValue = String(valueToFormat);
  }

  if (prefix && prefix.trim() !== "" && formattedValue) {
    formattedValue = `${prefix.trim()} ${formattedValue}`;
  }
  if (postfix && postfix.trim() !== "" && formattedValue) {
    formattedValue = `${formattedValue} ${postfix.trim()}`;
  }

  return formattedValue.trim(); // Убираем лишние пробелы, если значения нет, но есть префикс/постфикс
};

// Функция для создания URL продукта (остается без изменений)
const getProductUrl = (product: Product): string => {
  // Если у продукта есть изображения, используем URL первого изображения
  if (product.product_images && product.product_images.length > 0) {
    const mainImage =
      product.product_images.find((img) => img.is_main) ||
      product.product_images[0];
    if (mainImage && mainImage.public_url) {
      return mainImage.public_url;
    }
  }
  // Если у продукта нет изображений, возвращаем заглушку
  return `https://example.com/products/${product.id}`;
};

// --- ИЗМЕНЕНИЕ: Принимаем RowData[] и PDFExportSettings ---
export const exportToPDFMake = async (
  data: RowData[],
  settings: PDFExportSettings,
  openInNewWindow = false
): Promise<void> => {
  try {
    // Выбираем только выбранные колонки
    const selectedColumns = settings.columns.filter((col) => col.selected);
    const numberOfColumnsForPDF = selectedColumns.length + 1;

    // Создаем заголовки таблицы
    const tableHeaders = [
      ...selectedColumns.map((col) => {
        const isNumberColumn =
          col.dataType === "number" ||
          ["price", "weight", "validity_period"].includes(col.field);

        return {
          text: col.headerName,
          style: "tableHeaderCell",
          alignment: isNumberColumn ? "right" : "left",
        };
      }),
      {
        text: VIRTUAL_ORDER_COLUMN_HEADER,
        style: "tableHeaderCell",
        alignment: "center",
      },
    ];

    // --- ИЗМЕНЕНИЕ: Подготавливаем данные таблицы с учетом групп ---
    const prepareTableData = () => {
      // Используем any[] для совместимости с pdfMake - который не полностью типизирован
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const tableRows: any[][] = []; // Массив строк таблицы
      const nonEmptyGroupIds = new Set<string>();

      // 1. Определяем, какие группы содержат продукты, если группировка включена
      if (settings.includeGroups) {
        data.forEach((item) => {
          // Проверяем, что это продукт (не заголовок группы) и у него есть group_id
          if (!isGroupHeaderRow(item) && item.group_id) {
            nonEmptyGroupIds.add(item.group_id);
          }
        });
      }

      // 2. Формируем строки таблицы
      data.forEach((item) => {
        if (isGroupHeaderRow(item)) {
          // Если текущий элемент - это заголовок группы
          if (settings.includeGroups) {
            let shouldAddGroupHeader = false;
            if (item.groupId === "ungrouped") {
              // Заголовок "Без группы" добавляется всегда, если он присутствует в `data`.
              // Логика в `useProducts` гарантирует, что он там только если есть продукты без группы.
              shouldAddGroupHeader = true;
            } else if (nonEmptyGroupIds.has(item.groupId)) {
              // Для остальных (именованных) групп - добавляем заголовок, только если группа не пустая.
              shouldAddGroupHeader = true;
            }

            if (shouldAddGroupHeader) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              const groupHeaderCell: any = {
                text: item.groupName,
                colSpan: numberOfColumnsForPDF,
                style: "groupHeaderStyle",
                alignment: "left",
              };
              // Создаем строку заголовка группы
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              const groupRow: any[] = [groupHeaderCell];
              // Добавляем пустые ячейки для остальных колонок
              for (let i = 1; i < numberOfColumnsForPDF; i++) {
                groupRow.push({ text: "" });
              }
              tableRows.push(groupRow);
            }
          }
          // Если группировка отключена (settings.includeGroups === false), заголовки групп не добавляются.
        } else {
          // Если текущий элемент - это продукт
          const product = item as Product; // Мы знаем, что это продукт, так как isGroupHeaderRow(item) === false
          let shouldAddProduct = false;

          if (!settings.includeGroups) {
            // Если группировка отключена, добавляем все продукты
            shouldAddProduct = true;
          } else {
            // Если группировка включена
            if (product.group_id === null) {
              // Продукт без группы - добавляем (заголовок "Без группы" уже обработан выше)
              shouldAddProduct = true;
            } else if (nonEmptyGroupIds.has(product.group_id)) {
              // Продукт принадлежит непустой группе - добавляем
              shouldAddProduct = true;
            }
            // Если продукт принадлежит пустой группе (которой нет в nonEmptyGroupIds), он не добавляется.
          }

          if (shouldAddProduct) {
            const rowCells = selectedColumns.map((col) => {
              const isNumberCol =
                col.dataType === "number" ||
                ["price", "weight", "validity_period"].includes(col.field);
              const cellStyle = isNumberCol
                ? "tableCellNumber"
                : "tableCellText";
              let cellContent: Content = {
                text: formatValue(product, col.field, col.prefix, col.postfix),
                style: cellStyle,
              };

              if (col.field === "name") {
                cellContent = {
                  text: formatValue(
                    product,
                    col.field,
                    col.prefix,
                    col.postfix
                  ),
                  style: "productNameLink",
                  link: getProductUrl(product),
                };
              }
              return cellContent;
            });
            rowCells.push({
              text: "",
              style: "tableCellText",
              alignment: "center",
            });
            tableRows.push(rowCells);
          }
        }
      });

      return tableRows;
    };
    // --- КОНЕЦ ИЗМЕНЕНИЯ ---

    // Создаем пустой элемент для условных блоков
    const emptyElement: Content = { text: "" };

    // Функция для создания и скачивания PDF
    const generatePDF = () => {
      // Получаем данные таблицы
      const tableRows = prepareTableData();

      // Создаем содержимое документа
      const docDefinition: TDocumentDefinitions = {
        pageOrientation: "landscape",
        pageSize: "A4",
        pageMargins: [50, 60, 50, 60], // Увеличены боковые отступы
        defaultStyle: {
          font: "Roboto",
          fontSize: 9, // Базовый размер шрифта для таблицы
          color: "#333333", // Стандартный темный цвет текста
        },
        content: [
          // Заголовок документа и дата на одной линии
          {
            columns: [
              {
                text: `Коммерческое предложение ${settings.header.companyName}`,
                style: "mainHeader",
                width: "*", // Занимает большую часть
              },
              {
                text: settings.header.date,
                style: "documentDate",
                alignment: "right",
                width: "auto", // Занимает сколько нужно
              },
            ],
            margin: [0, 0, 0, 25], // Отступ снизу после блока заголовка/даты
          },

          // Контактная информация в две колонки
          {
            columns: [
              // Левая колонка контактов
              {
                width: "*",
                stack: [
                  { text: "Офис:", style: "contactDetailHeader" },
                  { text: settings.header.office, style: "contactDetailText" },
                  {
                    text: `тел. ${settings.header.officePhone}`,
                    style: "contactDetailText",
                    margin: [0, -2, 0, 8],
                  }, // Уменьшаем верхний отступ для телефона

                  settings.header.managerName
                    ? { text: "Менеджер:", style: "contactDetailHeader" }
                    : emptyElement,
                  settings.header.managerName
                    ? {
                        text: settings.header.managerName,
                        style: "contactDetailText",
                      }
                    : emptyElement,
                ],
              },
              // Правая колонка контактов
              {
                width: "*",
                stack: [
                  { text: "Склад:", style: "contactDetailHeader" },
                  {
                    text: settings.header.warehouse,
                    style: "contactDetailText",
                  },
                  {
                    text: `тел. ${settings.header.warehousePhone}`,
                    style: "contactDetailText",
                    margin: [0, -2, 0, 8],
                  },

                  settings.header.managerContact && settings.header.managerName // Показываем контакт менеджера, только если есть имя
                    ? { text: "E-mail/телефон:", style: "contactDetailHeader" }
                    : emptyElement,
                  settings.header.managerContact && settings.header.managerName
                    ? {
                        text: settings.header.managerContact,
                        style: "contactDetailText",
                      }
                    : emptyElement,
                ],
              },
            ],
            columnGap: 20, // Пространство между колонками контактов
            margin: [0, 0, 0, 15], // Отступ снизу после блока контактов
          },

          // Информация о доставке
          {
            text: "Условия доставки:", // Добавляем заголовок для блока доставки
            style: "contactDetailHeader", // Используем тот же стиль, что и для "Офис:", "Склад:"
            margin: [0, 0, 0, 3],
          },
          {
            stack: settings.header.deliveryInfo.map((info) => ({
              text: info,
              style: "deliveryDetail",
            })),
            margin: [0, 0, 0, 25], // Отступ после информации о доставке
          },

          // Таблица продуктов
          {
            table: {
              headerRows: 1,
              widths: [
                ...selectedColumns.map((col) => {
                  const field = col.field;
                  const isNumberField =
                    col.dataType === "number" ||
                    ["price", "weight", "validity_period"].includes(field);

                  if (field === "name") return "*";
                  if (field === "price") return 60;
                  if (field === "weight") return 50;
                  if (field === "validity_period") return 70;
                  if (field.startsWith("custom_fields.") && isNumberField)
                    return "auto";
                  if (
                    field.startsWith("custom_fields.") &&
                    col.dataType !== "number"
                  )
                    return 70;
                  if (
                    ["is_hidden", "product_images", "created_at"].includes(
                      field
                    )
                  )
                    return "auto";
                  return "auto";
                }),
                60,
              ],
              body: [
                tableHeaders, // Просто передаем массив tableHeaders, так как он уже содержит нужные стили и alignment
                ...tableRows, // tableRows генерируются в prepareTableData
              ],
            },
            layout: {
              hLineWidth: function (i) {
                // Линия только под заголовком таблицы
                if (i === 1) return 0.5; // Тонкая линия под заголовком
                return 0; // Нет других горизонтальных линий
              },
              vLineWidth: function () {
                return 0; // Нет вертикальных линий
              },
              hLineColor: function (i) {
                // Цвет линии под заголовком
                return i === 1 ? "#D1D5DB" : "#FFFFFF"; // Tailwind gray-300
              },
              paddingLeft: function (i, node) {
                // Для заголовков групп отступ слева 0, т.к. они на всю ширину
                const rowData =
                  i < node.table.body.length ? node.table.body[i][0] : null;
                if (
                  rowData &&
                  typeof rowData === "object" &&
                  "style" in rowData &&
                  rowData.style === "groupHeaderStyle"
                )
                  return 0;
                return 8; // Отступ для остальных ячеек
              },
              paddingRight: function (i, node) {
                const rowData =
                  i < node.table.body.length ? node.table.body[i][0] : null;
                if (
                  rowData &&
                  typeof rowData === "object" &&
                  "style" in rowData &&
                  rowData.style === "groupHeaderStyle"
                )
                  return 0;
                return 8;
              },
              paddingTop: function (i, node) {
                const rowData =
                  i < node.table.body.length ? node.table.body[i][0] : null;
                if (
                  rowData &&
                  typeof rowData === "object" &&
                  "style" in rowData &&
                  rowData.style === "groupHeaderStyle"
                )
                  return 8; // Отступ сверху для заголовка группы
                if (i === 0) return 6; // Отступ для заголовков таблицы
                return 5; // Отступ для строк данных
              },
              paddingBottom: function (i, node) {
                const rowData =
                  i < node.table.body.length ? node.table.body[i][0] : null;
                if (
                  rowData &&
                  typeof rowData === "object" &&
                  "style" in rowData &&
                  rowData.style === "groupHeaderStyle"
                )
                  return 8; // Отступ снизу для заголовка группы
                if (i === 0) return 6; // Отступ для заголовков таблицы
                return 5; // Отступ для строк данных
              },
              // Убираем заливку по умолчанию, она будет управляться стилями ячеек, если нужно
              // defaultBorder: false, // Это может убрать все границы, лучше управлять через hLineWidth/vLineWidth
            },
          },
        ],
        // --- Обновленный footer ---
        footer: function (currentPage, pageCount) {
          return {
            columns: [
              {
                text: settings.header.companyName,
                alignment: "left",
                style: "pageFooter",
              },
              {
                text: `Страница ${currentPage} из ${pageCount}`,
                alignment: "right",
                style: "pageFooter",
              },
            ],
            margin: [50, 10, 50, 0], // Отступы для футера [left, top, right, bottom]
          };
        },
        // --- Обновленные styles ---
        styles: {
          mainHeader: {
            fontSize: 18,
            bold: true,
            color: "#111827", // Tailwind gray-900
            margin: [0, 0, 0, 20], // Отступ снизу
          },
          documentDate: {
            // Стиль для даты документа (справа от заголовка)
            fontSize: 10,
            color: "#6B7280", // Tailwind gray-500
          },
          contactDetailHeader: {
            // Для заголовков "Офис:", "Склад:"
            fontSize: 9,
            bold: true,
            color: "#374151", // Tailwind gray-700
            margin: [0, 0, 0, 1],
          },
          contactDetailText: {
            // Для текста адресов и телефонов
            fontSize: 9,
            color: "#4B5563", // Tailwind gray-600
            margin: [0, 0, 0, 4], // Небольшой отступ снизу для каждого блока контакта
          },
          deliveryDetail: {
            // Стиль для строк информации о доставке
            fontSize: 9,
            color: "#4B5563", // Tailwind gray-600
            margin: [0, 0, 0, 2], // Небольшой отступ между строками доставки
          },
          tableHeaderCell: {
            // Стиль для заголовков колонок таблицы
            fontSize: 10,
            bold: true,
            color: "#1F2937", // Tailwind gray-800
          },
          tableCellText: {
            // Стиль для текстовых ячеек данных
            fontSize: 9,
            color: "#374151", // Tailwind gray-700
            alignment: "left",
          },
          tableCellNumber: {
            // Стиль для числовых ячеек данных
            fontSize: 9,
            color: "#374151", // Tailwind gray-700
            alignment: "right",
          },
          productNameLink: {
            // Стиль для наименования продукта (ссылка)
            fontSize: 9,
            color: "#2563EB", // Tailwind blue-600 (стандартный цвет ссылок)
            // decoration: 'underline', // Убираем подчеркивание для более чистого вида
            bold: false,
            alignment: "left",
          },
          groupHeaderStyle: {
            // Стиль для заголовков групп продуктов
            fontSize: 11,
            bold: true,
            color: "#111827", // Tailwind gray-900
            margin: [0, 0, 0, 0], // Управляется padding в layout
            alignment: "left",
          },
          pageFooter: {
            // Стиль для колонтитула
            fontSize: 8,
            color: "#9CA3AF", // Tailwind gray-400
          },
        }, // --- Конец styles ---
      }; // --- Конец docDefinition ---

      const pdfDoc = pdfMake.createPdf(docDefinition);
      if (openInNewWindow) {
        pdfDoc.open();
      } else {
        pdfDoc.download(
          `Прайс-лист_${new Date().toLocaleDateString("ru-RU")}.pdf`
        );
      }
    }; // --- Конец generatePDF ---

    // Запускаем генерацию PDF
    generatePDF();
  } catch (error) {
    console.error("Ошибка при создании PDF:", error);
    // Выбрасываем ошибку дальше, чтобы ее обработал useProductExport
    throw new Error(
      "Ошибка при генерации PDF: " +
        (error instanceof Error ? error.message : String(error))
    );
  }
};
</file>

<file path="src/hooks/useProducts.ts">
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { Database } from "../types/supabase";
import { supabase } from "../lib/supabase";
import { GroupHeaderRow } from "../components/products/grid/GroupHeaderRenderer";
import { toast } from "@/hooks/use-toast";

// --- Определение типов для большей ясности кода ---
type Product = Database["public"]["Tables"]["products"]["Row"] & {
  product_images?: Array<Database["public"]["Tables"]["product_images"]["Row"]>;
};

type ProductGroup = Database["public"]["Tables"]["product_groups"]["Row"];
type RowData = Product | GroupHeaderRow; // Объединенный тип для данных в таблице
// ------------------------------------------------

export function useProducts({
  showHidden = false,
}: {
  showHidden?: boolean;
} = {}) {
  return useQuery<{ data: RowData[] }>({
    queryKey: ["products", { showHidden }],
    queryFn: async () => {
      console.log(
        "[useProducts] Начало получения данных о продуктах и группах"
      );

      // --- Параллельная выборка продуктов и групп ---
      const [productsResponse, groupsResponse] = await Promise.all([
        supabase
          .from("products")
          .select("*, product_images(*)")
          .eq("is_hidden", showHidden),
        supabase
          .from("product_groups")
          .select("*")
          .order("order_index", { ascending: true }),
      ]);
      // ----------------------------------------

      const { data: productsData, error: productsError } = productsResponse;
      const { data: groupsData, error: groupsError } = groupsResponse;

      if (productsError) throw productsError;
      if (groupsError) throw groupsError;

      const products = (productsData || []) as Product[];
      const groups = (groupsData || []) as ProductGroup[];

      console.log(
        `[useProducts] Получено ${products.length} продуктов и ${groups.length} групп`
      );
      console.log(
        "[useProducts] Порядок групп:",
        groups.map((g) => ({ id: g.id, name: g.name, order: g.order_index }))
      );

      // --- Обработка данных: сортировка и вставка заголовков ---
      const groupMap = new Map(groups.map((g) => [g.id, g]));
      const processedData: RowData[] = [];

      // Счетчик продуктов в каждой группе
      const groupProductCount: Record<string, number> = {};

      // Подсчет продуктов в каждой группе
      products.forEach((product) => {
        if (product.group_id) {
          groupProductCount[product.group_id] =
            (groupProductCount[product.group_id] || 0) + 1;
        }
      });

      console.log(
        "[useProducts] Кол-во продуктов в группах:",
        groupProductCount
      );

      // Сортировка продуктов: по группе -> по orderInGroup -> по имени
      console.log("[useProducts] Начало сортировки продуктов...");
      const sortedProducts = [...products].sort((a, b) => {
        const groupA = a.group_id ? groupMap.get(a.group_id) : null;
        const groupB = b.group_id ? groupMap.get(b.group_id) : null;

        const groupAOrder = groupA?.order_index ?? Infinity;
        const groupBOrder = groupB?.order_index ?? Infinity;

        // Сначала сортируем по порядку групп
        if (groupAOrder !== groupBOrder) {
          return groupAOrder - groupBOrder;
        }

        // Затем сортируем по порядку внутри группы
        const orderA = a.order_in_group ?? 0;
        const orderB = b.order_in_group ?? 0;
        if (orderA !== orderB) {
          return orderA - orderB;
        }

        // И лишь затем по имени (если orderInGroup совпадает)
        return (a.name ?? "").localeCompare(b.name ?? "");
      });

      // Выводим для отладки первые несколько отсортированных продуктов
      console.log(
        "[useProducts] Первые 5 отсортированных продуктов:",
        sortedProducts.slice(0, 5).map((p) => ({
          id: p.id,
          name: p.name,
          group_id: p.group_id,
          order_in_group: p.order_in_group,
        }))
      );

      // Группировка продуктов по ID группы
      const productsByGroup: Record<string, Product[]> = {};
      const ungroupedProducts: Product[] = [];

      sortedProducts.forEach((product) => {
        // Убедимся, что у продукта есть custom_fields
        const normalizedProduct = {
          ...product,
          custom_fields: product.custom_fields || {},
        };

        if (normalizedProduct.group_id) {
          if (!productsByGroup[normalizedProduct.group_id]) {
            productsByGroup[normalizedProduct.group_id] = [];
          }
          productsByGroup[normalizedProduct.group_id].push(normalizedProduct);
        } else {
          ungroupedProducts.push(normalizedProduct);
        }
      });

      console.log(
        "[useProducts] Группировка завершена: ",
        Object.keys(productsByGroup).map((groupId) => ({
          groupId,
          groupName: groupMap.get(groupId)?.name || "Неизвестная группа",
          productCount: productsByGroup[groupId].length,
        }))
      );
      console.log(
        "[useProducts] Негруппированных продуктов:",
        ungroupedProducts.length
      );

      // Добавление заголовков групп и продуктов в итоговый массив
      console.log("[useProducts] Формирование итогового массива данных...");
      groups.forEach((group) => {
        // Добавляем заголовок группы
        processedData.push({
          isGroupHeader: true,
          groupId: group.id,
          groupName: group.name,
          totalProducts: groupProductCount[group.id] || 0,
        });

        // Добавляем продукты этой группы
        const groupProducts = productsByGroup[group.id] || [];
        if (groupProducts.length > 0) {
          processedData.push(...groupProducts);
        }
      });

      // В конце добавляем продукты без группы (если есть)
      if (ungroupedProducts.length > 0) {
        // Опционально: можно добавить заголовок для негруппированных продуктов
        processedData.push({
          isGroupHeader: true,
          groupId: "ungrouped",
          groupName: "Без группы",
          totalProducts: ungroupedProducts.length,
        });
        processedData.push(...ungroupedProducts);
      }
      // --------------------------------------------

      console.log(
        `[useProducts] Итоговый массив содержит ${processedData.length} элементов`
      );
      return { data: processedData };
    },
  });
}

export function useProductMutations() {
  const queryClient = useQueryClient();

  const update = useMutation({
    mutationFn: async ({
      id,
      updates,
    }: {
      id: string;
      updates: Partial<Database["public"]["Tables"]["products"]["Update"]>;
    }) => {
      try {
        console.log(`[API] Обновление продукта:`, { id, updates });

        // Проверяем, нет ли в updates динамических полей, которые должны быть внутри custom_fields
        const sanitizedUpdates = { ...updates } as Record<string, unknown>;
        const dynamicFields: Record<string, unknown> = {};
        let hasDynamicFields = false;

        // Ищем поля, которые не являются стандартными полями таблицы products
        // и добавляем их в custom_fields
        for (const key in sanitizedUpdates) {
          // Проверяем, является ли поле динамическим (не входит в схему таблицы)
          if (
            ![
              "id",
              "name",
              "weight",
              "price",
              "validity_period",
              "is_hidden",
              "custom_fields",
              "created_at",
              "updated_at",
              "group_id",
              "order_in_group", // Добавляем новое поле order_in_group в список стандартных полей
            ].includes(key)
          ) {
            dynamicFields[key] = sanitizedUpdates[key];
            delete sanitizedUpdates[key]; // Удаляем поле из основных обновлений
            hasDynamicFields = true;
          }
        }

        // Если найдены динамические поля, добавляем их в custom_fields
        if (hasDynamicFields) {
          // Получаем текущие данные продукта, чтобы объединить custom_fields
          const { data: existingProduct } = await supabase
            .from("products")
            .select("custom_fields")
            .eq("id", id)
            .single();

          const existingCustomFields = (existingProduct?.custom_fields ||
            {}) as Record<string, unknown>;

          // Объединяем существующие поля с новыми
          sanitizedUpdates.custom_fields = {
            ...existingCustomFields,
            ...dynamicFields,
          };

          console.log(
            `[API] Обнаружены динамические поля, перемещены в custom_fields:`,
            {
              originalUpdates: updates,
              dynamicFields,
              newCustomFields: sanitizedUpdates.custom_fields,
            }
          );
        }

        // Проверка и санитизация данных custom_fields
        if (sanitizedUpdates.custom_fields !== undefined) {
          // Убедимся, что custom_fields - валидный JSON
          try {
            // Преобразуем в строку и обратно для очистки от недопустимых значений
            const cleanJSON = JSON.parse(
              JSON.stringify(sanitizedUpdates.custom_fields)
            );
            sanitizedUpdates.custom_fields = cleanJSON;
          } catch (error) {
            console.error("Ошибка при санитизации custom_fields:", error);
            sanitizedUpdates.custom_fields = {}; // Используем пустой объект если невалидные данные
          }
        }

        const { data, error } = await supabase
          .from("products")
          .update(
            sanitizedUpdates as Partial<
              Database["public"]["Tables"]["products"]["Update"]
            >
          )
          .eq("id", id)
          .select()
          .single();

        if (error) {
          console.error(`[API] Ошибка при обновлении продукта:`, {
            id,
            updates: sanitizedUpdates,
            error: {
              message: error.message,
              details: error.details,
              hint: error.hint,
              code: error.code,
            },
          });
          throw error;
        }

        return data;
      } catch (error) {
        console.error(`[API] Неожиданная ошибка при обновлении продукта:`, {
          id,
          updates,
          error,
        });
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const updateImages = useMutation({
    mutationFn: async ({
      productId,
      images,
    }: {
      productId: string;
      images: Database["public"]["Tables"]["product_images"]["Insert"][];
    }) => {
      // Сначала удаляем все существующие изображения
      await supabase
        .from("product_images")
        .delete()
        .eq("product_id", productId);

      // Затем добавляем новые
      const { data, error } = await supabase
        .from("product_images")
        .insert(images.map((img) => ({ ...img, product_id: productId })))
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const remove = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase.from("products").delete().eq("id", id);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const toggleVisibility = useMutation({
    mutationFn: async ({
      ids,
      isHidden,
    }: {
      ids: string[];
      isHidden: boolean;
    }) => {
      const { error } = await supabase
        .from("products")
        .update({ is_hidden: isHidden })
        .in("id", ids);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  const create = useMutation({
    mutationFn: async (
      product: Omit<Database["public"]["Tables"]["products"]["Insert"], "id">
    ) => {
      try {
        // Убедимся, что у нового продукта есть custom_fields
        const productWithCustomFields = {
          ...product,
          custom_fields: product.custom_fields || {},
        };

        // Проверка и санитизация данных custom_fields
        try {
          const cleanJSON = JSON.parse(
            JSON.stringify(productWithCustomFields.custom_fields)
          );
          productWithCustomFields.custom_fields = cleanJSON;
        } catch (error) {
          console.error("Ошибка при санитизации custom_fields:", error);
          productWithCustomFields.custom_fields = {}; // Используем пустой объект если невалидные данные
        }

        const { data, error } = await supabase
          .from("products")
          .insert(productWithCustomFields)
          .select()
          .single();

        if (error) {
          console.error(`[API] Ошибка при создании продукта:`, {
            product: productWithCustomFields,
            error: {
              message: error.message,
              details: error.details,
              hint: error.hint,
              code: error.code,
            },
          });
          throw error;
        }

        return data;
      } catch (error) {
        console.error(`[API] Неожиданная ошибка при создании продукта:`, {
          product,
          error,
        });
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });

  // Новая мутация для массового обновления порядка продуктов
  const bulkUpdateOrder = useMutation({
    mutationFn: async (
      updates: Array<{ id: string; order_in_group: number }>
    ) => {
      console.log(
        `[BulkUpdate] Начало массового обновления порядка для ${updates.length} продуктов`
      );

      try {
        // Выполняем обновления через Promise.all
        const promises = updates.map((u) => {
          console.log(
            `[BulkUpdate] Обновление продукта ${u.id} -> order_in_group: ${u.order_in_group}`
          );
          return supabase
            .from("products")
            .update({ order_in_group: u.order_in_group })
            .eq("id", u.id);
        });

        const results = await Promise.allSettled(promises);

        // Проверяем на ошибки более детально
        const failed = results.filter(
          (r) => r.status === "rejected"
        ) as PromiseRejectedResult[];
        const errors = results
          .filter(
            (r) =>
              r.status === "fulfilled" &&
              (r.value as { error: unknown | null }).error !== null
          )
          .map((r) => {
            const result = r as PromiseFulfilledResult<{
              error: unknown;
              status: number;
            }>;
            return {
              error: result.value.error,
              status: result.value.status,
            };
          });

        if (failed.length > 0 || errors.length > 0) {
          console.error(
            "[BulkUpdate] Ошибки при обновлении порядка продуктов:",
            {
              rejections: failed.map((f) => f.reason),
              apiErrors: errors,
            }
          );

          throw new Error(
            `Не удалось обновить порядок ${
              failed.length + errors.length
            } продуктов.`
          );
        }

        console.log(
          `[BulkUpdate] Успешно обновлен порядок для ${updates.length} продуктов`
        );
      } catch (error) {
        console.error(
          "[BulkUpdate] Произошла ошибка при выполнении массового обновления:",
          error
        );
        throw error;
      }
    },
    onSuccess: () => {
      console.log(
        "[BulkUpdate] Инвалидация кеша после успешного обновления порядка продуктов"
      );
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast({ title: "Успех", description: "Порядок продуктов обновлен" });
    },
    onError: (error) => {
      console.error("[BulkUpdate] Ошибка в мутации:", error);
      toast({
        title: "Ошибка",
        description: `Ошибка обновления порядка продуктов: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  return {
    update,
    updateImages,
    remove,
    toggleVisibility,
    create,
    bulkUpdateOrder,
  };
}
</file>

<file path="src/components/products/ProductList.tsx">
import { useEffect, useState, useCallback } from "react";
import { AgGridReact } from "ag-grid-react";
import { useProducts, useProductMutations } from "../../hooks/useProducts";
import { useAuth } from "../../lib/auth";
import { ColumnSettingsModal } from "./modals/ColumnSettingsModal";
import { toast } from "@/hooks/use-toast";
import { ProductToolbar } from "./ProductToolbar";
import { PDFExportSettingsModal } from "./export/PDFExportSettingsModal";
import { useColumnSettings } from "../../hooks/useColumnSettings";
import {
  IsFullWidthRowParams,
  GetRowIdParams,
  RowDragEndEvent,
  IRowNode,
  RowDragMoveEvent,
  RowDragLeaveEvent,
} from "ag-grid-community";
import { useQueryClient } from "@tanstack/react-query";
import {
  useProductGroups,
  useProductGroupMutations,
} from "../../hooks/useProductGroups";
import type { Database } from "../../types/supabase"; // Импортируем тип Database

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";

// Импорт хуков, созданных при рефакторинге
import { useProductGrid } from "./grid/useProductGrid";
import { useProductColumns } from "./columns/useProductColumns";
import { useProductExport } from "./export/useProductExport";
import {
  GroupHeaderRenderer,
  GroupHeaderRow,
} from "./grid/GroupHeaderRenderer"; // Импортируем GroupHeaderRow
import { useProductEditHandlers } from "./editHandlers/useProductEditHandlers";

// Определяем тип Product локально для ясности внутри этого файла
type Product = Database["public"]["Tables"]["products"]["Row"] & {
  product_images?: Array<Database["public"]["Tables"]["product_images"]["Row"]>;
};
type ProductUpdate = Database["public"]["Tables"]["products"]["Update"];
type RowData = Product | GroupHeaderRow; // Корректный тип для данных строки

export function ProductList() {
  const { signOut } = useAuth();
  const [showHidden, setShowHidden] = useState(false);
  const [isExportSettingsOpen, setIsExportSettingsOpen] = useState(false);
  const [isColumnSettingsOpen, setIsColumnSettingsOpen] = useState(false);
  const queryClient = useQueryClient();

  // Состояние и обработчики таблицы
  const { gridApi, selectedRows, onGridReady, onSelectionChanged } =
    useProductGrid();

  // Колонки таблицы
  const { columnDefs, defaultColDef } = useProductColumns();

  // Обработчики редактирования
  const { onCellValueChanged } = useProductEditHandlers();

  // Экспорт в PDF
  const { isExporting, isPreviewingPDF, handleExportToPDF } =
    useProductExport();

  // Загрузка групп
  const { data: groupsData } = useProductGroups();
  const { data: columnSettingsData } = useColumnSettings();

  // Мутации продуктов и групп
  const {
    remove,
    toggleVisibility,
    create,
    update: updateProduct,
    // bulkUpdateOrder: bulkUpdateProductOrder, // Возможно, нам не нужна эта конкретная массовая мутация
  } = useProductMutations();
  const { create: createGroup, bulkUpdateOrder: bulkUpdateGroupOrder } =
    useProductGroupMutations();

  // --- НОВОЕ: Функции для полноширинных строк ---
  const isFullWidthRow = useCallback(
    (params: IsFullWidthRowParams): boolean => {
      // Проверяем, существуют ли данные и есть ли у них свойство isGroupHeader
      return !!params.rowNode?.data?.isGroupHeader;
    },
    []
  );

  const getRowId = useCallback((params: GetRowIdParams): string => {
    if (params.data?.isGroupHeader) {
      return `group-${params.data.groupId}`;
    }
    // Убеждаемся, что данные и id существуют перед обращением
    return params.data?.id ?? `temp-${Math.random()}`; // Предоставляем запасной вариант или обрабатываем ошибку
  }, []);
  // -------------------------------------------

  // =======================================================================
  // ===== УЛУЧШЕННАЯ ФУНКЦИЯ handleRowDragEnd =============================
  // =======================================================================
  const handleRowDragEnd = useCallback(
    async (event: RowDragEndEvent<RowData>) => {
      const movingNode = event.node;
      const overNode = event.overNode;

      if (!overNode || !gridApi || !movingNode.data) {
        console.log(
          "[DragEnd] Недопустимое состояние или сброс за пределами таблицы."
        );
        return;
      }

      console.log("[DragEnd] Начало обработки завершения перетаскивания", {
        movingNodeType:
          "isGroupHeader" in movingNode.data && movingNode.data.isGroupHeader
            ? "Группа"
            : "Продукт",
        movingNodeId: movingNode.id,
        overNodeId: overNode.id,
        overIndex: event.overIndex,
      });

      // --- Получаем текущий визуальный порядок из сетки ---
      const displayedRowsData: RowData[] = [];
      gridApi.forEachNodeAfterFilterAndSort((node: IRowNode<RowData>) => {
        if (node.data) {
          displayedRowsData.push(node.data);
        }
      });

      // Type guard функция для определения GroupHeaderRow
      const isGroupHeader = (item: RowData): item is GroupHeaderRow => {
        return "isGroupHeader" in item && item.isGroupHeader === true;
      };

      // Type guard функция для определения Product
      const isProduct = (item: RowData): item is Product => {
        return (
          "id" in item &&
          !("isGroupHeader" in item && item.isGroupHeader === true)
        );
      };

      // --- Сценарий 1: Перемещение ПРОДУКТА ---
      if (isProduct(movingNode.data)) {
        const movedProduct = movingNode.data; // Теперь TypeScript знает, что это Product
        const originalGroupId = movedProduct.group_id;
        const originalOrderInGroup = movedProduct.order_in_group;

        console.log("[DragEnd][Продукт] Перемещение продукта:", {
          id: movedProduct.id,
          name: movedProduct.name,
          originalGroupId: originalGroupId ?? "без группы",
          originalOrder: originalOrderInGroup,
        });

        // --- Определяем ID целевой группы ---
        let targetGroupId: string | null = null;
        let lastSeenGroupId: string | null = null;

        for (const row of displayedRowsData) {
          if (isGroupHeader(row)) {
            lastSeenGroupId = row.groupId === "ungrouped" ? null : row.groupId;
            // Если сброшено прямо на заголовок группы
            if (
              overNode.data &&
              isGroupHeader(overNode.data) &&
              row.groupId === overNode.data.groupId
            ) {
              targetGroupId = lastSeenGroupId;
              console.log(
                `[DragEnd][Продукт] Цель определена сбросом НА заголовок группы: ${
                  targetGroupId ?? "без группы"
                }`
              );
              break; // Нашли цель
            }
          } else if (
            isProduct(row) &&
            overNode.data !== undefined &&
            isProduct(overNode.data) &&
            row.id === overNode.data.id
          ) {
            // Если сброшено на продукт, целевая группа - последняя просмотренная группа
            targetGroupId = lastSeenGroupId;
            console.log(
              `[DragEnd][Продукт] Цель определена сбросом НА продукт (ID: ${
                row.id
              }): ${targetGroupId ?? "без группы"}`
            );
            break; // Нашли цель
          }
        }

        // Запасной вариант, если сброшено в самый конец? Должно быть обработано логикой цикла.
        if (targetGroupId === undefined) {
          console.warn(
            "[DragEnd][Продукт] Не удалось точно определить целевую группу, используем последнюю просмотренную группу."
          );
          targetGroupId = lastSeenGroupId;
        }

        console.log(
          `[DragEnd][Продукт] Итоговый ID целевой группы: ${
            targetGroupId ?? "без группы"
          }`
        );

        // --- Собираем продукты в целевой группе (новый порядок) ---
        const targetGroupProducts: Product[] = [];
        let currentProcessingGroupId: string | null = null;

        displayedRowsData.forEach((row) => {
          if (isGroupHeader(row)) {
            currentProcessingGroupId =
              row.groupId === "ungrouped" ? null : row.groupId;
          } else if (isProduct(row)) {
            // Продукт принадлежит целевой группе, если:
            // 1. Это перемещаемый продукт и его целевая группа соответствует
            // 2. Это НЕ перемещаемый продукт, но его текущая группа соответствует цели
            const productGroup = row.group_id;
            if (row.id === movedProduct.id) {
              if (currentProcessingGroupId === targetGroupId) {
                targetGroupProducts.push(row);
                console.log(
                  `[DragEnd][Продукт] Добавлен перемещаемый продукт ${row.id} в список целевой группы.`
                );
              }
            } else if (productGroup === targetGroupId) {
              targetGroupProducts.push(row);
              console.log(
                `[DragEnd][Продукт] Добавлен существующий продукт ${row.id} в список целевой группы.`
              );
            }
          }
        });

        console.log(
          `[DragEnd][Продукт] Собраны продукты для целевой группы (${
            targetGroupId ?? "без группы"
          }):`,
          targetGroupProducts.map((p) => ({ id: p.id, name: p.name }))
        );

        // --- Подготавливаем обновления ---
        const updatesToSend: Array<{ id: string; updates: ProductUpdate }> = [];
        targetGroupProducts.forEach((product, index) => {
          const newOrderInGroup = index;
          const updatePayload: ProductUpdate = {};
          let needsUpdate = false;

          // Проверяем, нужно ли обновлять группу (только для перемещаемого продукта)
          if (
            product.id === movedProduct.id &&
            originalGroupId !== targetGroupId
          ) {
            updatePayload.group_id = targetGroupId;
            needsUpdate = true;
            console.log(
              `[DragEnd][Продукт] Обновление для ${
                product.id
              }: Изменение группы с ${originalGroupId ?? "без группы"} на ${
                targetGroupId ?? "без группы"
              }`
            );
          }

          // Проверяем, нужно ли обновлять порядок
          if (product.order_in_group !== newOrderInGroup) {
            updatePayload.order_in_group = newOrderInGroup;
            needsUpdate = true;
            console.log(
              `[DragEnd][Продукт] Обновление для ${product.id}: Изменение порядка с ${product.order_in_group} на ${newOrderInGroup}`
            );
          }

          if (needsUpdate) {
            updatesToSend.push({ id: product.id, updates: updatePayload });
          }
        });

        console.log(
          `[DragEnd][Продукт] Подготовлено ${updatesToSend.length} обновлений:`,
          updatesToSend
        );

        // --- Выполняем обновления ---
        if (updatesToSend.length > 0) {
          try {
            console.log("[DragEnd][Продукт] Отправка обновлений на сервер...");
            // Используем Promise.all для параллельной отправки обновлений
            await Promise.all(
              updatesToSend.map((upd) => updateProduct.mutateAsync(upd))
            );
            toast({
              title: "Успех",
              description: "Порядок продуктов обновлен",
            });
            console.log("[DragEnd][Продукт] Обновления успешны.");
            // Инвалидация происходит в onSuccess мутации
          } catch (error) {
            console.error(
              "[DragEnd][Продукт] Ошибка при обновлении порядка/группы продукта:",
              error
            );
            toast({
              title: "Ошибка",
              description: "Ошибка при обновлении порядка продуктов",
              variant: "destructive",
            });
            // Рассмотрим откат визуальных изменений или повторную выборку для безопасности
            queryClient.invalidateQueries({ queryKey: ["products"] });
          }
        } else {
          console.log(
            "[DragEnd][Продукт] Не обнаружено изменений порядка или группы, обновления не отправлены."
          );
        }
      }
      // --- Сценарий 2: Перемещение ЗАГОЛОВКА ГРУППЫ ---
      else if (isGroupHeader(movingNode.data)) {
        const groupUpdates: Array<{ id: string; order_index: number }> = [];
        let currentOrderIndex = 0;

        console.log(
          "[DragEnd][Группа] Обработка перемещения заголовка группы."
        );

        displayedRowsData.forEach((row) => {
          if (isGroupHeader(row) && row.groupId !== "ungrouped") {
            const groupId = row.groupId;
            // Проверяем, действительно ли изменился order_index по сравнению с исходными данными
            const originalGroup = groupsData?.data.find(
              (g) => g.id === groupId
            );
            if (
              originalGroup &&
              originalGroup.order_index !== currentOrderIndex
            ) {
              groupUpdates.push({
                id: groupId,
                order_index: currentOrderIndex,
              });
              console.log(
                `[DragEnd][Группа] Требуется обновление для ${groupId}: новый индекс ${currentOrderIndex}`
              );
            } else if (!originalGroup) {
              console.warn(
                `[DragEnd][Группа] Не удалось найти исходные данные для группы ${groupId}`
              );
            } else {
              console.log(
                `[DragEnd][Группа] Нет изменения индекса для ${groupId} (все еще ${currentOrderIndex})`
              );
            }
            currentOrderIndex++;
          }
        });

        console.log(
          `[DragEnd][Группа] Подготовлено ${groupUpdates.length} обновлений групп:`,
          groupUpdates
        );

        if (groupUpdates.length > 0) {
          try {
            console.log(
              "[DragEnd][Группа] Отправка обновлений групп на сервер..."
            );
            await bulkUpdateGroupOrder.mutateAsync(groupUpdates);
            toast({ title: "Успех", description: "Порядок групп обновлен" });
            console.log("[DragEnd][Группа] Обновления групп успешны.");
            // Инвалидация происходит в onSuccess мутации
          } catch (error) {
            console.error(
              "[DragEnd][Группа] Ошибка при обновлении порядка групп:",
              error
            );
            toast({
              title: "Ошибка",
              description: "Ошибка при обновлении порядка групп",
              variant: "destructive",
            });
            queryClient.invalidateQueries({ queryKey: ["product_groups"] });
            queryClient.invalidateQueries({ queryKey: ["products"] }); // Также инвалидируем продукты, так как их отображение зависит от порядка групп
          }
        } else {
          console.log(
            "[DragEnd][Группа] Не обнаружено изменений порядка групп, обновления не отправлены."
          );
        }
      }
    },
    [
      gridApi,
      updateProduct,
      bulkUpdateGroupOrder,
      queryClient,
      groupsData?.data,
    ] // Добавлена зависимость groupsData
  );
  // =======================================================================
  // ===== КОНЕЦ УЛУЧШЕННОЙ handleRowDragEnd ==============================
  // =======================================================================

  const {
    data: productData,
    isLoading,
    error: productsError,
  } = useProducts({
    showHidden,
  });

  const handleAddProduct = useCallback(
    (groupId: string | null) => {
      if (!gridApi) return;

      let orderInGroup = 0;
      if (groupId && productData?.data) {
        const productsInGroup = productData.data.filter(
          (item): item is Product =>
            "id" in item &&
            !("isGroupHeader" in item && item.isGroupHeader === true) &&
            item.group_id === groupId
        );
        if (productsInGroup.length > 0) {
          orderInGroup =
            Math.max(...productsInGroup.map((p) => p.order_in_group || 0)) + 1;
        }
      }

      // Создаем новый пустой продукт
      const newProduct = {
        name: "",
        weight: 0,
        price: 0,
        validity_period: 30,
        is_hidden: false,
        custom_fields: {},
        group_id: groupId,
        order_in_group: orderInGroup,
      };

      create.mutate(newProduct, {
        onSuccess: (createdProduct) => {
          // После успешного создания выбираем новую строку и начинаем редактирование
          toast({ title: "Успех", description: "Создана новая запись" });

          // Ждем обновления данных в таблице через инвалидацию запроса
          setTimeout(() => {
            if (gridApi) {
              // Найти строку с созданным продуктом
              const rowNode = gridApi.getRowNode(createdProduct.id);
              if (rowNode) {
                // Выбрать строку
                rowNode.setSelected(true);

                // Прокручиваем к строке
                gridApi.ensureNodeVisible(rowNode, "middle");

                // Начать редактирование первой ячейки (name)
                gridApi.startEditingCell({
                  rowIndex: rowNode.rowIndex as number,
                  colKey: "name",
                });
              }
            }
          }, 200);
        },
        onError: (error) => {
          toast({
            title: "Ошибка",
            description: "Ошибка при создании записи",
            variant: "destructive",
          });
          console.error("Ошибка при создании продукта:", error);
        },
      });
    },
    [gridApi, create, productData?.data]
  );

  const handleAddGroup = useCallback(async () => {
    if (!groupsData?.data) return;

    try {
      // Находим максимальный текущий order_index
      const maxOrderIndex = groupsData.data.reduce(
        (max, g) => Math.max(max, g.order_index || 0),
        -1
      );

      // Создаем новую группу
      const newGroup = await createGroup.mutateAsync({
        name: "Новая группа",
        order_index: maxOrderIndex + 1,
      });

      // Обновление произойдет через invalidateQueries в мутации

      // Опционально: пытаемся начать редактирование имени новой группы
      setTimeout(() => {
        if (gridApi) {
          const groupNode = gridApi.getRowNode(`group-${newGroup.id}`);
          if (groupNode) {
            gridApi.ensureNodeVisible(groupNode);
            // GroupHeaderRenderer имеет собственную логику для редактирования
          }
        }
      }, 200);
    } catch (error) {
      console.error("Ошибка при создании группы:", error);
    }
  }, [groupsData, createGroup, gridApi]);

  // Отображение ошибки при загрузке данных
  useEffect(() => {
    if (productsError) {
      console.error("[ProductList] Ошибка при загрузке данных:", productsError);
      toast({
        title: "Ошибка",
        description: "Ошибка при загрузке данных продуктов",
        variant: "destructive",
      });
    }
  }, [productsError]);

  const handleToggleVisibility = () => {
    if (selectedRows.length === 0) return;
    const ids = selectedRows.map((row) => row.id);
    const isHidden = !selectedRows[0].is_hidden;
    toggleVisibility.mutate({ ids, isHidden });
  };

  const handleDelete = () => {
    if (selectedRows.length === 0) return;

    console.log(
      `[UI] Попытка удаления ${selectedRows.length} продуктов:`,
      selectedRows.map((row) => row.id)
    );

    if (confirm("Вы уверены, что хотите удалить выбранные продукты?")) {
      const deleteProducts = async () => {
        for (const row of selectedRows) {
          try {
            await remove.mutateAsync(row.id);
          } catch (error) {
            console.error(
              `[UI] Ошибка при удалении продукта ${row.id}:`,
              error
            );
            // Продолжаем удаление остальных продуктов
          }
        }
      };

      deleteProducts();
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error("Ошибка при выходе:", error);
      toast({
        title: "Ошибка",
        description: "Ошибка при выходе из системы",
        variant: "destructive",
      });
    }
  };

  // Обновляем сетку при изменении columnDefs
  useEffect(() => {
    if (gridApi) {
      gridApi.setColumnDefs(columnDefs);
    }
  }, [gridApi, columnDefs]);

  // Обработчик перемещения при перетаскивании строк
  const handleRowDragMove = useCallback((event: RowDragMoveEvent) => {
    console.log("[DragMove] Перемещение строки над:", {
      movingNode: event.node.data.isGroupHeader
        ? `Группа: ${event.node.data.groupName}`
        : `Продукт: ${event.node.data.name}`,
      overNode: event.overNode?.data
        ? event.overNode.data.isGroupHeader
          ? `Группа: ${event.overNode.data.groupName}`
          : `Продукт: ${event.overNode.data.name}`
        : "Нет узла",
    });
  }, []);

  // Обработчик выхода за пределы таблицы при перетаскивании
  const handleRowDragLeave = useCallback((event: RowDragLeaveEvent) => {
    console.log("[DragLeave] Выход за пределы таблицы:", {
      movingNode: event.node.data.isGroupHeader
        ? `Группа: ${event.node.data.groupName}`
        : `Продукт: ${event.node.data.name}`,
    });
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    );
  }

  // Отображение ошибки загрузки
  if (productsError) {
    return (
      <div className="flex flex-col items-center justify-center h-96 gap-4">
        <div className="text-red-500 text-xl">Ошибка загрузки данных</div>
        <button
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={() =>
            queryClient.invalidateQueries({ queryKey: ["products"] })
          }
        >
          Попробовать снова
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh-2rem)] gap-4 p-4">
      <ProductToolbar
        showHidden={showHidden}
        selectedRows={selectedRows}
        onAddProduct={handleAddProduct}
        onAddGroupClick={handleAddGroup}
        onToggleVisibility={handleToggleVisibility}
        onDelete={handleDelete}
        onExportToPDF={(preview) =>
          handleExportToPDF(productData?.data, columnDefs, columnSettingsData?.data, preview)
        }
        onExportSettingsClick={() => setIsExportSettingsOpen(true)}
        onColumnSettingsClick={() => setIsColumnSettingsOpen(true)}
        onSignOut={handleSignOut}
        onToggleShowHidden={() => setShowHidden(!showHidden)}
        isExporting={isExporting}
        isPreviewingPDF={isPreviewingPDF}
        hasProducts={!!productData?.data?.length}
      />

      <div className="flex-grow ag-theme-alpine">
        <AgGridReact
          rowData={productData?.data || []}
          columnDefs={columnDefs}
          defaultColDef={{
            ...defaultColDef,
            // По умолчанию включаем sortable для всех колонок
            sortable: true,
            resizable: true,
          }}
          rowSelection="multiple"
          onGridReady={onGridReady}
          onSelectionChanged={onSelectionChanged}
          onCellValueChanged={onCellValueChanged}
          suppressRowClickSelection
          // --- Настройки для полноширинных строк ---
          isFullWidthRow={isFullWidthRow}
          fullWidthCellRenderer={GroupHeaderRenderer}
          getRowId={getRowId}
          // --- Настройки для Drag & Drop ---
          rowDragManaged={true}
          rowDragEntireRow={true}
          rowDragMultiRow={true}
          suppressMoveWhenRowDragging={false}
          onRowDragEnd={handleRowDragEnd}
          animateRows={true}
          onRowDragMove={handleRowDragMove}
          onRowDragLeave={handleRowDragLeave}
          // ---------------------------------------
        />
      </div>

      {isExportSettingsOpen && (
        <PDFExportSettingsModal
          isOpen={isExportSettingsOpen}
          onClose={() => setIsExportSettingsOpen(false)}
          columnDefs={columnDefs}
        />
      )}

      {isColumnSettingsOpen && (
        <ColumnSettingsModal
          isOpen={isColumnSettingsOpen}
          onClose={() => setIsColumnSettingsOpen(false)}
        />
      )}
    </div>
  );
}
</file>

<file path="package.json">
{
  "name": "price-list-manager",
  "private": true,
  "version": "0.1.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "lint": "eslint .",
    "preview": "vite preview",
    "test": "vitest",
    "db:generate": "drizzle-kit generate",
    "db:migrate": "drizzle-kit push"
  },
  "dependencies": {
    "@fontsource/roboto": "^5.1.1",
    "@radix-ui/react-checkbox": "^1.1.5",
    "@radix-ui/react-dialog": "^1.1.6",
    "@radix-ui/react-dropdown-menu": "^2.1.6",
    "@radix-ui/react-label": "^2.1.3",
    "@radix-ui/react-scroll-area": "^1.2.4",
    "@radix-ui/react-select": "^2.1.7",
    "@radix-ui/react-slot": "^1.1.2",
    "@radix-ui/react-switch": "^1.1.4",
    "@radix-ui/react-toast": "^1.2.13",
    "@radix-ui/react-tooltip": "^1.1.8",
    "@supabase/supabase-js": "^2.39.7",
    "@tanstack/react-query": "^5.24.1",
    "@types/pdfkit": "^0.13.9",
    "@types/pdfmake": "^0.2.11",
    "ag-grid-community": "^31.1.1",
    "ag-grid-react": "^31.1.1",
    "axios": "^1.6.7",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "dotenv": "^16.4.7",
    "drizzle-orm": "^0.39.3",
    "embla-carousel-react": "^8.5.2",
    "lucide-react": "^0.344.0",
    "pdfmake": "^0.2.18",
    "postgres": "^3.4.5",
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "react-dropzone": "^14.2.3",
    "react-router-dom": "^7.2.0",
    "tailwind-merge": "^3.0.2",
    "tailwindcss-animate": "^1.0.7"
  },
  "devDependencies": {
    "@eslint/js": "^9.9.1",
    "@testing-library/react": "^14.2.1",
    "@types/node": "^22.13.5",
    "@types/react": "^18.3.5",
    "@types/react-dom": "^18.3.0",
    "@vitejs/plugin-react": "^4.3.1",
    "autoprefixer": "^10.4.18",
    "drizzle-kit": "^0.30.4",
    "eslint": "^9.9.1",
    "eslint-plugin-react-hooks": "^5.1.0-rc.0",
    "eslint-plugin-react-refresh": "^0.4.11",
    "globals": "^15.9.0",
    "jsdom": "^24.0.0",
    "postcss": "^8.4.35",
    "tailwindcss": "^3.4.1",
    "tsx": "^4.19.3",
    "typescript": "^5.5.3",
    "typescript-eslint": "^8.3.0",
    "vite": "^5.4.2",
    "vitest": "^1.3.1"
  }
}
</file>

</files>
