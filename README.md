# Price List Manager: Project Overview

This project is a web application for managing product lists, including names, prices, images, and other details. It's built with React, shadcn/ui, TypeScript, Supabase, AG Grid, and Yandex.Disk, showcasing a modern web development stack.

**Key Features:**

- **CRUD Operations:** Create, Read, Update, and Delete products.
- **Inline Adding:** Add new products directly in the table without modal forms.
- **Authentication:** Secure user login via Supabase Auth.
- **Real-time Updates:** Changes are reflected instantly across clients.
- **Interactive Data Grid:** Powerful table view with inline editing, sorting, and filtering (AG Grid).
- **Image Management:** Upload and store product images using Yandex.Disk.
- **Drag-and-Drop Image Upload:** User-friendly image uploading.
- **Modal Forms:** Improved user experience for creating products.
- **Protected Routes:** Access control for authenticated users.
- **Efficient State Management:** Uses React Query for data fetching and caching.
- **UI Feedback:** Loading states, notifications (react-hot-toast).
- **Component-Based Architecture:** Reusable React components.
- **TypeScript:** Enhanced code quality with strong typing
- **Tailwind CSS:** Rapid and consistent UI styling

**Technologies:**

- **Frontend:** React, TypeScript, AG Grid, React Router, React Query, Tailwind CSS, Lucide React, react-hot-toast, Vite.
- **Backend:** Supabase (PostgreSQL database, authentication, real-time).
- **Cloud Storage:** Yandex.Disk

**Project Structure (Simplified):**

- `components/`: React components (e.g., `ProductList`, `CreateProductModal`, `LoginPage`).
- `hooks/`: Custom React hooks (e.g., `useProducts` for data management).
- `lib/`: Utilities (e.g., Supabase client, authentication logic).
- `services/`: API interactions (e.g., Supabase database, Yandex.Disk).
- `types/`: TypeScript type definitions.
- `supabase/migrations`: Database schema

This project demonstrates a practical example of building a data-driven web application with a focus on user experience, real-time capabilities, and secure data management. It serves as a solid foundation for learning and building similar applications.
