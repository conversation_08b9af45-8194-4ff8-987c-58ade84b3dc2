# Форматирование текста в таблице

## Обзор

Система поддерживает форматирование текста в ячейках таблицы с помощью атрибутных HTML-подобных тегов. Форматирование применяется через горячие клавиши, поддерживает комбинирование нескольких типов форматирования и сохраняется при экспорте в Excel и PDF.

## ✨ Новые возможности (после cleanup)

- **Комбинированное форматирование**: Можно применить жирный И красный одновременно
- **Индивидуальное удаление**: Можно убрать только жирный, оставив красный
- **Улучшенная производительность**: Кэширование парсинга для быстрой обработки
- **Лучшая документация**: Полные JSDoc комментарии и примеры
- **Упрощенная архитектура**: Более понятный и поддерживаемый код

## Поддерживаемые форматы

### 1. Жирный текст

- **Тег**: `<bold>текст</bold>`
- **Горячая клавиша**: `Ctrl+B` (Windows/Linux) или `Cmd+B` (Mac)
- **Отображение**: Текст отображается жирным шрифтом

### 2. Красный цвет

- **Тег**: `<red>текст</red>`
- **Горячая клавиша**: `Ctrl+R` (Windows/Linux) или `Cmd+R` (Mac)
- **Отображение**: Текст отображается красным цветом (#dc2626)

### 3. Комбинированное форматирование

- **Новый тег**: `<format bold="true" red="true">текст</format>`
- **Результат**: жирный красный текст
- **Преимущества**: Нет проблем с вложенностью, легкое управление атрибутами

## Как использовать

### Применение форматирования:

1. Дважды кликните на ячейку для входа в режим редактирования
2. Выделите текст, который нужно отформатировать
3. Нажмите соответствующую горячую клавишу:
   - `Ctrl+B` для жирного текста
   - `Ctrl+R` для красного цвета
4. Нажмите `Enter` для сохранения

### Удаление форматирования:

1. Войдите в режим редактирования ячейки
2. Выделите отформатированный текст
3. Нажмите ту же горячую клавишу для отключения форматирования

### Отмена изменений:

- Нажмите `Escape` во время редактирования для отмены всех изменений

## Поддерживаемые поля

### Стандартные поля:

- **Наименование** - полная поддержка форматирования

### Кастомные поля:

- **Текстовые поля** - полная поддержка форматирования
- **Числовые поля** - форматирование не поддерживается

### Исключения:

- Поля с изображениями
- Поля статуса
- Поля дат
- Поля групп (выпадающий список)

## Экспорт в Excel

### Поддержка форматирования:

- ✅ Жирный текст сохраняется
- ✅ Красный цвет сохраняется
- ✅ Комбинированное форматирование сохраняется

### Особенности:

- Форматирование применяется на уровне символов в Excel
- Цвет соответствует оригинальному (#dc2626)
- Совместимость с Excel 2016+

## Техническая реализация

### Хранение данных:

- Форматирование хранится как HTML-подобные теги в тексте
- Пример: `"Обычный <bold>жирный</bold> и <red>красный</red> текст"`
- Теги не отображаются пользователю в интерфейсе

### Безопасность:

- Используются только предопределенные теги `<bold>` и `<red>`
- Нет риска XSS атак
- Простая и надежная реализация

### Производительность:

- Парсинг тегов происходит только при отображении
- Минимальное влияние на производительность
- Эффективная обработка больших таблиц

## Примеры использования

### Пример 1: Выделение важной информации

```
Обычный текст <bold>ВАЖНО</bold> продолжение
```

Результат: Обычный текст **ВАЖНО** продолжение

### Пример 2: Предупреждения

```
Статус: <red>Требует внимания</red>
```

Результат: Статус: <span style="color: #dc2626">Требует внимания</span>

### Пример 3: Комбинированное форматирование

```
<bold><red>КРИТИЧНО</red></bold> - срочно исправить
```

Результат: <span style="font-weight: bold; color: #dc2626">КРИТИЧНО</span> - срочно исправить

## Ограничения

### Текущие ограничения:

- Только 2 типа форматирования (жирный и красный)
- Нет поддержки других цветов
- Нет поддержки курсива или подчеркивания
- Нет поддержки размера шрифта

### Планируемые улучшения:

- Добавление других цветов
- Поддержка курсива
- Визуальная панель форматирования
- Поддержка ссылок

## Устранение неполадок

### Форматирование не применяется:

1. Убедитесь, что текст выделен
2. Проверьте, что поле поддерживает форматирование
3. Убедитесь, что у вас есть права на редактирование

### Форматирование не сохраняется:

1. Нажмите `Enter` для сохранения изменений
2. Проверьте подключение к серверу
3. Убедитесь, что нет ошибок в консоли

### Проблемы с экспортом:

1. Убедитесь, что используете последнюю версию Excel
2. Проверьте, что файл не поврежден
3. Попробуйте экспорт без форматирования для сравнения
