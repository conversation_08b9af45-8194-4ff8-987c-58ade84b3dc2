---
description: Best practices for database management with Drizzle and Supabase
globs: 
alwaysApply: true
---
- Always define schema in `src/db/schema.ts` and relations in `src/db/relations.ts`
- DO NOT create migrations manually, instead run `pnpm db:generate` after schema changes to create migration files
- Apply migrations with `pnpm db:migrate` to update remote Supabase database
- Use Drizzle's type-safe query builders instead of raw SQL queries
- Implement database transactions for operations that modify multiple tables
- Add proper indexes on frequently queried columns for performance
- Keep sensitive data protected with RLS (Row Level Security) policies in Supabase
- Define enum types in schema file for consistent data validation
- Create database views for complex, frequently used queries
- `pnpm db:generate` and `pnpm db:migrate` are already created in package.json