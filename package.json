{"name": "price-list-manager", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit push"}, "dependencies": {"@fontsource/roboto": "^5.1.1", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/supabase-js": "^2.39.7", "@tanstack/react-query": "^5.24.1", "@types/file-saver": "^2.0.7", "@types/pdfkit": "^0.13.9", "@types/pdfmake": "^0.2.11", "ag-grid-community": "^31.1.1", "ag-grid-react": "^31.1.1", "axios": "^1.6.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.39.3", "embla-carousel-react": "^8.5.2", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "lucide-react": "^0.344.0", "pdfmake": "^0.2.18", "postgres": "^3.4.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-router-dom": "^7.2.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.13.5", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "drizzle-kit": "^0.30.4", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^24.0.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tsx": "^4.19.3", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.3.1"}}