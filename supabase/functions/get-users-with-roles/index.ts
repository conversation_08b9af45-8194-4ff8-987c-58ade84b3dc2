import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { supabaseAdminClient } from '../_shared/supabaseAdmin.ts';
import { ensureAdmin, createJsonResponse, createErrorResponse } from '../_shared/authUtils.ts';

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return createJsonResponse({ message: 'ok' }, 200);
  }

  const adminCheck = await ensureAdmin(req);
  if (adminCheck.error) {
    return adminCheck.error;
  }

  try {
    const { data: users, error } = await supabaseAdminClient.auth.admin.listUsers();

    if (error) {
      console.error('Error listing users:', error);
      return createErrorResponse(error.message, 500);
    }

    const usersWithRoles = users.users.map(user => ({
      id: user.id,
      email: user.email,
      role: user.app_metadata?.role || null,
      created_at: user.created_at,
      last_sign_in_at: user.last_sign_in_at,
    }));

    return createJsonResponse(usersWithRoles);
  } catch (e) {
    console.error('Unexpected error in get-users-with-roles:', e);
    return createErrorResponse(e.message || 'Internal Server Error', 500);
  }
});
