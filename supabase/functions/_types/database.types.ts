export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      column_settings: {
        Row: {
          created_at: string | null
          data_type: string
          display_name: string
          id: string
          is_visible: boolean | null
          name: string
          order_index: number
          postfix: string | null
          prefix: string | null
        }
        Insert: {
          created_at?: string | null
          data_type: string
          display_name: string
          id?: string
          is_visible?: boolean | null
          name: string
          order_index: number
          postfix?: string | null
          prefix?: string | null
        }
        Update: {
          created_at?: string | null
          data_type?: string
          display_name?: string
          id?: string
          is_visible?: boolean | null
          name?: string
          order_index?: number
          postfix?: string | null
          prefix?: string | null
        }
        Relationships: []
      }
      product_groups: {
        Row: {
          created_at: string
          id: string
          is_hidden: boolean
          name: string
          order_index: number
        }
        Insert: {
          created_at?: string
          id?: string
          is_hidden?: boolean
          name: string
          order_index: number
        }
        Update: {
          created_at?: string
          id?: string
          is_hidden?: boolean
          name?: string
          order_index?: number
        }
        Relationships: []
      }
      product_images: {
        Row: {
          created_at: string | null
          id: string
          is_main: boolean | null
          product_id: string
          public_url: string
          sort_order: number
          yandex_disk_path: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_main?: boolean | null
          product_id: string
          public_url: string
          sort_order: number
          yandex_disk_path: string
        }
        Update: {
          created_at?: string | null
          id?: string
          is_main?: boolean | null
          product_id?: string
          public_url?: string
          sort_order?: number
          yandex_disk_path?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_images_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          created_at: string | null
          custom_fields: Json | null
          group_id: string | null
          id: string
          is_hidden: boolean | null
          name: string
          order_in_group: number
          price: number
          updated_at: string | null
          validity_period: number
          weight: number
        }
        Insert: {
          created_at?: string | null
          custom_fields?: Json | null
          group_id?: string | null
          id?: string
          is_hidden?: boolean | null
          name: string
          order_in_group?: number
          price: number
          updated_at?: string | null
          validity_period: number
          weight: number
        }
        Update: {
          created_at?: string | null
          custom_fields?: Json | null
          group_id?: string | null
          id?: string
          is_hidden?: boolean | null
          name?: string
          order_in_group?: number
          price?: number
          updated_at?: string | null
          validity_period?: number
          weight?: number
        }
        Relationships: [
          {
            foreignKeyName: "products_group_id_product_groups_id_fk"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "product_groups"
            referencedColumns: ["id"]
          },
        ]
      }
      session_lock: {
        Row: {
          lock_key: string
          locked_at: string | null
          session_id: string | null
          user_id: string | null
        }
        Insert: {
          lock_key: string
          locked_at?: string | null
          session_id?: string | null
          user_id?: string | null
        }
        Update: {
          lock_key?: string
          locked_at?: string | null
          session_id?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      version_history: {
        Row: {
          comment: string | null
          created_at: string
          id: string
          snapshot_data: Json
          user_id: string
        }
        Insert: {
          comment?: string | null
          created_at?: string
          id?: string
          snapshot_data: Json
          user_id: string
        }
        Update: {
          comment?: string | null
          created_at?: string
          id?: string
          snapshot_data?: Json
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_dynamic_column: {
        Args: { column_name: string; column_type: string }
        Returns: undefined
      }
      drop_dynamic_column: {
        Args: { column_name: string }
        Returns: undefined
      }
      get_my_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      rename_dynamic_column: {
        Args: { old_name: string; new_name: string }
        Returns: undefined
      }
    }
    Enums: {
      column_type: "text" | "number"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      column_type: ["text", "number"],
    },
  },
} as const
