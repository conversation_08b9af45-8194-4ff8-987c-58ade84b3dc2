import { createClient, SupabaseClient } from 'npm:@supabase/supabase-js@2';
import type { Database } from '../_types/database.types.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

if (!supabaseUrl) {
  console.error('SUPABASE_URL is not set in Edge Function environment variables.');
  throw new Error('SUPABASE_URL is not set.');
}
if (!supabaseServiceRoleKey) {
  console.error('SUPABASE_SERVICE_ROLE_KEY is not set in Edge Function environment variables.');
  throw new Error('SUPABASE_SERVICE_ROLE_KEY is not set.');
}

export const supabaseAdminClient: SupabaseClient<Database> = createClient<Database>(
  supabaseUrl,
  supabaseServiceRoleKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  },
);
