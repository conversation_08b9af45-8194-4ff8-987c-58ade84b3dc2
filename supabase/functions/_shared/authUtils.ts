import { User, createClient } from 'npm:@supabase/supabase-js@2';

export async function ensureAdmin(req: Request): Promise<{ user: User | null; error: Response | null }> {
  const authHeader = req.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    console.warn('ensureAdmin: Missing or invalid Authorization header');
    return { user: null, error: createErrorResponse('Missing or invalid Authorization header', 401) };
  }
  const token = authHeader.split(' ')[1];

  const supabaseUrl = Deno.env.get('SUPABASE_URL');
  const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('ensureAdmin: SUPABASE_URL or SUPABASE_ANON_KEY not set for user role check.');
    return { user: null, error: createErrorResponse('Server configuration error for auth check', 500) };
  }

  const tempClient = createClient(supabaseUrl, supabaseAnonKey, {
    auth: { persistSession: false, autoRefreshToken: false }
  });

  const { data: { user }, error: userError } = await tempClient.auth.getUser(token);

  if (userError || !user) {
    console.warn('ensureAdmin: Error fetching user or user not found for token.', userError);
    return { user: null, error: createErrorResponse(userError?.message || 'Invalid token or user not found', 401) };
  }

  const role = user.app_metadata?.role;
  if (role !== 'admin') {
    console.warn(`ensureAdmin: User ${user.id} with role '${role}' is not an admin.`);
    return { user, error: createErrorResponse('Access denied: Admin role required', 403) };
  }

  console.log(`ensureAdmin: User ${user.id} is admin.`);
  return { user, error: null };
}

export function createJsonResponse(body: unknown, status: number = 200, headers?: Record<string, string>): Response {
  return new Response(JSON.stringify(body), {
    status,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      ...headers,
    },
  });
}

export function createErrorResponse(message: string, status: number = 500, details?: unknown): Response {
  console.error(`Error Response: ${status} - ${message}`, details);
  return createJsonResponse({ error: { message, details } }, status);
}
