import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { supabaseAdminClient } from '../_shared/supabaseAdmin.ts';
import { ensureAdmin, createJsonResponse, createErrorResponse } from '../_shared/authUtils.ts';

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return createJsonResponse({ message: 'ok' }, 200);
  }

  const adminCheck = await ensureAdmin(req);
  if (adminCheck.error) {
    return adminCheck.error;
  }

  let userIdToDelete;
  try {
    const body = await req.json();
    userIdToDelete = body.userId;

    if (!userIdToDelete || typeof userIdToDelete !== 'string') {
      return createErrorResponse('userId is required and must be a string.', 400);
    }
  } catch (e) {
    return createErrorResponse(e.message || 'Invalid request body.', 400);
  }

  try {
    if (adminCheck.user && adminCheck.user.id === userIdToDelete) {
      return createErrorResponse('Ad<PERSON> cannot delete their own account.', 403);
    }

    const { data: targetUser, error: targetUserError } = await supabaseAdminClient.auth.admin.getUserById(userIdToDelete);
    
    if (targetUserError) {
      return createErrorResponse(targetUserError.message, targetUserError.status || 500);
    }
    
    if (targetUser?.user?.app_metadata?.role === 'admin') {
      const { data: users } = await supabaseAdminClient.auth.admin.listUsers();
      const adminCount = users.users.filter(user => user.app_metadata?.role === 'admin').length;
      
      if (adminCount <= 1) {
        return createErrorResponse('Cannot delete the last admin account.', 403);
      }
    }

    const { error: deleteError } = await supabaseAdminClient.auth.admin.deleteUser(userIdToDelete);

    if (deleteError) {
      console.error(`Error deleting user ${userIdToDelete}:`, deleteError);
      return createErrorResponse(deleteError.message, deleteError.status || 500);
    }

    return createJsonResponse({ message: `User ${userIdToDelete} deleted successfully.` });

  } catch (e) {
    console.error('Unexpected error in delete-user:', e);
    return createErrorResponse(e.message || 'Internal Server Error', 500);
  }
});
