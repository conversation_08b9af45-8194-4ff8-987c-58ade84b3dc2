import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    let targetUrl: string | null = null;

    // Проверяем URL в query параметрах (для обратной совместимости)
    const url = new URL(req.url);
    targetUrl = url.searchParams.get("url");

    // Если URL не найден в query параметрах, проверяем body
    if (!targetUrl && req.method === "POST") {
      try {
        const body = await req.json();
        targetUrl = body.url;
      } catch {
        // Игнорируем ошибки парсинга JSON
      }
    }

    if (!targetUrl) {
      return new Response(JSON.stringify({ error: "Missing url parameter" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Проверяем, что URL принадлежит Яндекс.Диску
    const allowedDomains = [
      "downloader.disk.yandex.ru",
      "getfile.yandex.net",
      "disk.yandex.ru",
      "yastatic.net",
    ];

    const targetUrlObj = new URL(targetUrl);
    const isAllowedDomain = allowedDomains.some((domain) =>
      targetUrlObj.hostname.includes(domain)
    );

    if (!isAllowedDomain) {
      return new Response(JSON.stringify({ error: "Domain not allowed" }), {
        status: 403,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Проксируем запрос к Яндекс.Диску
    const response = await fetch(targetUrl, {
      method: req.method,
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        Accept: "image/*,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate, br",
        DNT: "1",
        Connection: "keep-alive",
        "Upgrade-Insecure-Requests": "1",
      },
    });

    if (!response.ok) {
      return new Response(
        JSON.stringify({ error: `Failed to fetch image: ${response.status}` }),
        {
          status: response.status,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Получаем тип контента из ответа
    const contentType = response.headers.get("content-type") || "image/jpeg";

    // Возвращаем изображение с правильными заголовками CORS
    return new Response(response.body, {
      status: 200,
      headers: {
        ...corsHeaders,
        "Content-Type": contentType,
        "Cache-Control": "public, max-age=3600", // Кешируем на 1 час
      },
    });
  } catch (error) {
    console.error("Proxy error:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
