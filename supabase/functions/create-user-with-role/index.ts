import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { supabaseAdminClient } from '../_shared/supabaseAdmin.ts';
import { ensureAdmin, createJsonResponse, createErrorResponse } from '../_shared/authUtils.ts';

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return createJsonResponse({ message: 'ok' }, 200);
  }

  const adminCheck = await ensureAdmin(req);
  if (adminCheck.error) {
    return adminCheck.error;
  }

  let email, password, role;
  try {
    const body = await req.json();
    email = body.email;
    password = body.password;
    role = body.role;

    if (!email || typeof email !== 'string') {
      return createErrorResponse('Email is required and must be a string.', 400);
    }
    if (!password || typeof password !== 'string' || password.length < 6) {
      return createErrorResponse('Password is required and must be at least 6 characters long.', 400);
    }
    if (!role || !['reader', 'editor'].includes(role)) {
      return createErrorResponse('Role is required and must be "reader" or "editor".', 400);
    }
  } catch (e) {
    return createErrorResponse(e.message || 'Invalid request body.', 400);
  }

  try {
    const { data: newUser, error: createError } = await supabaseAdminClient.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true,
      app_metadata: {
        role: role,
      },
    });

    if (createError) {
      console.error('Error creating user:', createError);
      return createErrorResponse(createError.message, createError.status || 500);
    }

    if (!newUser || !newUser.user) {
      return createErrorResponse('User creation did not return a user object.', 500);
    }

    return createJsonResponse({
      id: newUser.user.id,
      email: newUser.user.email,
      role: newUser.user.app_metadata?.role,
      created_at: newUser.user.created_at,
    }, 201);

  } catch (e) {
    console.error('Unexpected error in create-user-with-role:', e);
    return createErrorResponse(e.message || 'Internal Server Error', 500);
  }
});
