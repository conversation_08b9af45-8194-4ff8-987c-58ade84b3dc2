import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { supabaseAdminClient } from '../_shared/supabaseAdmin.ts';
import { ensureAdmin, createJsonResponse, createErrorResponse } from '../_shared/authUtils.ts';

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return createJsonResponse({ message: 'ok' }, 200);
  }

  const adminCheck = await ensureAdmin(req);
  if (adminCheck.error) {
    return adminCheck.error;
  }

  let userId, newRole;
  try {
    const body = await req.json();
    userId = body.userId;
    newRole = body.newRole;

    if (!userId || typeof userId !== 'string') {
      return createErrorResponse('userId is required and must be a string.', 400);
    }
    if (!newRole || !['reader', 'editor', 'admin'].includes(newRole)) {
      return createErrorResponse('newRole is required and must be "reader", "editor", or "admin".', 400);
    }
  } catch (e) {
    return createErrorResponse(e.message || 'Invalid request body.', 400);
  }

  try {
    if (adminCheck.user && adminCheck.user.id === userId && adminCheck.user.app_metadata?.role === 'admin' && newRole !== 'admin') {
      const { data: admins, error: countError } = await supabaseAdminClient.auth.admin.listUsers();
      
      if (countError) throw countError;
      
      const adminCount = admins.users.filter(user => user.app_metadata?.role === 'admin').length;
      
      if (adminCount <= 1) {
        return createErrorResponse('Cannot remove the last admin or change own role if last admin.', 403);
      }
    }

    const { data: updatedUser, error: updateError } = await supabaseAdminClient.auth.admin.updateUserById(
      userId,
      {
        app_metadata: { role: newRole },
      }
    );

    if (updateError) {
      console.error(`Error updating role for user ${userId}:`, updateError);
      return createErrorResponse(updateError.message, updateError.status || 500);
    }
    
    if (!updatedUser || !updatedUser.user) {
      return createErrorResponse('User update did not return a user object.', 500);
    }

    return createJsonResponse({
      id: updatedUser.user.id,
      email: updatedUser.user.email,
      role: updatedUser.user.app_metadata?.role,
    });

  } catch (e) {
    console.error('Unexpected error in set-user-role:', e);
    return createErrorResponse(e.message || 'Internal Server Error', 500);
  }
});
