import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "npm:@supabase/supabase-js@2";
import { supabaseAdminClient } from "../_shared/supabaseAdmin.ts";
import {
  createJsonResponse,
  createErrorResponse,
} from "../_shared/authUtils.ts";

interface RollbackRequest {
  version_id: string;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === "OPTIONS") {
    return createJsonResponse({ message: "ok" }, 200);
  }

  try {
    console.log("Rollback function called");

    // Get the authorization header
    const authHeader = req.headers.get("Authorization");
    console.log("Auth header:", authHeader ? "present" : "missing");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      console.log("No valid auth header found");
      return createErrorResponse(
        "Missing or invalid Authorization header",
        401
      );
    }

    const token = authHeader.split(" ")[1];

    // Create Supabase client for auth check
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY");

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error("SUPABASE_URL or SUPABASE_ANON_KEY not set");
      return createErrorResponse("Server configuration error", 500);
    }

    const supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
      auth: { persistSession: false, autoRefreshToken: false },
    });

    // Get the current user using the token
    console.log("Getting user from token...");
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser(token);

    console.log("User result:", { user: user?.id, error: userError?.message });

    if (userError || !user) {
      console.log("User authentication failed:", userError?.message);
      return createErrorResponse(
        userError?.message || "Invalid token or user not found",
        401
      );
    }

    // Check user role from app_metadata
    const userRole = user.app_metadata?.role;
    console.log(`User ${user.id} has role: ${userRole}`);

    if (!userRole || !["admin", "editor"].includes(userRole)) {
      console.log(`Access denied for user ${user.id} with role '${userRole}'`);
      return createErrorResponse("Insufficient permissions", 403);
    }

    // Parse request body
    const { version_id }: RollbackRequest = await req.json();
    console.log("Received version_id:", version_id);

    if (!version_id) {
      return createErrorResponse("version_id is required", 400);
    }

    // Fetch the version snapshot using admin client to bypass RLS
    console.log("Fetching version from database using admin client...");
    const { data: versionData, error: versionError } = await supabaseAdminClient
      .from("version_history")
      .select("snapshot_data")
      .eq("id", version_id)
      .single();

    console.log("Version query result:", {
      hasData: !!versionData,
      error: versionError?.message,
      versionId: version_id,
    });

    if (versionError || !versionData) {
      console.error("Version not found or error:", versionError);
      return createErrorResponse(
        versionError?.message || "Version not found",
        404
      );
    }

    const snapshot = versionData.snapshot_data as {
      products: any[];
      product_groups: any[];
      column_settings: any[];
      product_images: any[];
    };

    // Use admin client for database operations
    const supabaseAdmin = supabaseAdminClient;

    // Perform rollback operations
    // Note: Supabase doesn't support transactions in Edge Functions,
    // so we'll do our best to maintain consistency

    try {
      // 1. Delete all current product images (do this first due to foreign key constraints)
      const { error: deleteImagesError } = await supabaseAdmin
        .from("product_images")
        .delete()
        .neq("id", "00000000-0000-0000-0000-000000000000");

      if (deleteImagesError) throw deleteImagesError;

      // 2. Delete all current products
      const { error: deleteProductsError } = await supabaseAdmin
        .from("products")
        .delete()
        .neq("id", "00000000-0000-0000-0000-000000000000"); // Delete all (using a condition that's always true)

      if (deleteProductsError) throw deleteProductsError;

      // 3. Delete all current product groups
      const { error: deleteGroupsError } = await supabaseAdmin
        .from("product_groups")
        .delete()
        .neq("id", "00000000-0000-0000-0000-000000000000");

      if (deleteGroupsError) throw deleteGroupsError;

      // 4. Delete all current column settings
      const { error: deleteSettingsError } = await supabaseAdmin
        .from("column_settings")
        .delete()
        .neq("id", "00000000-0000-0000-0000-000000000000");

      if (deleteSettingsError) throw deleteSettingsError;

      // 5. Insert product groups from snapshot
      if (snapshot.product_groups.length > 0) {
        const { error: insertGroupsError } = await supabaseAdmin
          .from("product_groups")
          .insert(snapshot.product_groups);

        if (insertGroupsError) throw insertGroupsError;
      }

      // 6. Insert products from snapshot
      if (snapshot.products.length > 0) {
        const { error: insertProductsError } = await supabaseAdmin
          .from("products")
          .insert(snapshot.products);

        if (insertProductsError) throw insertProductsError;
      }

      // 7. Insert column settings from snapshot
      if (snapshot.column_settings.length > 0) {
        const { error: insertSettingsError } = await supabaseAdmin
          .from("column_settings")
          .insert(snapshot.column_settings);

        if (insertSettingsError) throw insertSettingsError;
      }

      // 8. Insert product images from snapshot
      if (snapshot.product_images && snapshot.product_images.length > 0) {
        const { error: insertImagesError } = await supabaseAdmin
          .from("product_images")
          .insert(snapshot.product_images);

        if (insertImagesError) throw insertImagesError;
      }

      return createJsonResponse({
        success: true,
        message: "Rollback completed successfully",
      });
    } catch (error) {
      console.error("Rollback error:", error);
      return createErrorResponse(
        "Rollback failed",
        500,
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  } catch (error) {
    console.error("Request error:", error);
    return createErrorResponse(
      "Internal server error",
      500,
      error instanceof Error ? error.message : "Unknown error"
    );
  }
});
